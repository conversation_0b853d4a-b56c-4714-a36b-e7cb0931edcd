import { API_URL } from "../config";

class ApiService {
  constructor() {
    this.data = JSON.parse(localStorage.getItem("userData")) || null;
  }
  setData(data) {
    this.data = data;
    localStorage.setItem("userData", JSON.stringify(data));
  }

  getData() {
    return this.data;
  }

  // static baseUrl = "http://localhost:3001";
  // static baseUrl = 'https://3001-firebase-mfscustomergit-1747822525484.cluster-44kx2eiocbhe2tyk3zoyo3ryuo.cloudworkstations.dev';
  static baseUrl = API_URL;

  async get(endpoint) {
    try {
      const response = await fetch(`${ApiService.baseUrl}${endpoint}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch data from ${endpoint}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  }

  async post(endpoint, body) {
    try {
      const response = await fetch(`${ApiService.baseUrl}${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        throw new Error(`Failed to post data to ${endpoint}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      throw error;
    }
  }
}

export default new ApiService();
