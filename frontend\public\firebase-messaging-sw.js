// Import Firebase libraries for the service worker
importScripts('https://www.gstatic.com/firebasejs/10.11.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.11.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBBA5iHXupY7XWlVvF8Xm-Zc0HCC-Lv1d8",
    authDomain: "mfs-live.firebaseapp.com",
    projectId: "mfs-live",
    storageBucket: "mfs-live.appspot.com",
    messagingSenderId: "805155199510",
    appId: "1:805155199510:web:037c8a73ea4e0379883337",
    measurementId: "G-E7J1RPXRGK"
  };

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
    console.log('Received background message: ', payload);
});
