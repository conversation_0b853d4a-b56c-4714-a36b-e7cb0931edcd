<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&display=swap" rel="stylesheet">
  <title>Flip Ticket</title>
  <style>
    body {
      background-color: #f2f2f2;
      font-family: 'Nunito Sans', sans-serif;
      padding: 20px;
      margin: 0;
      display: flex;
      justify-content: center;
    }

    .ticket-container {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      max-width: 400px;
      width: 100%;
    }

    .ticket-header {
      position: relative;
      padding: 16px;
      display: flex;
      flex-direction: column;
    }

    .ticket-info {
      font-size: 14px;
    }

    .ticket-info h2 {
      font-size: 16px;
      margin: 0 0 6px 0;
      font-weight: 700;
      color: #111;
    }

    .ticket-info p {
      margin: 4px 0;
      color: #555;
      font-weight: 400;
    }

    .pickup-label {
      position: absolute;
      top: 0;
      right: -28px;
      transform: rotate(90deg);
      background: #f1f1f1;
      color: #555;
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 0 0 4px 4px;
    }

    .ticket-actions {
      background: #f5f5f5;
      text-align: center;
      padding: 12px;
      font-size: 14px;
      color: #777;
      border-top: 1px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;
      cursor: pointer;
    }

    .ticket-middle {
      text-align: center;
      padding: 16px 12px;
      font-size: 14px;
      color: #333;
    }

    .ticket-middle h3 {
      font-size: 18px;
      margin: 8px 0;
      font-weight: 600;
    }

    .ticket-middle p {
      margin: 4px 0;
      font-weight: 400;
    }

    .qr-image {
      width: 180px;
      height: 180px;
      object-fit: contain;
      border-radius: 8px;
    }

    .ticket-note {
      background: #f6f6f6;
      text-align: center;
      padding: 10px 14px;
      font-size: 13px;
      color: #888;
      border-top: 1px solid #eee;
    }

    .ticket-footer {
      display: flex;
      justify-content: space-between;
      padding: 14px 16px;
      font-size: 14px;
      border-top: 1px solid #eaeaea;
      font-weight: 500;
      color: #000;
    }

    .flip-container {
      perspective: 1000px;
      width: 100%;
      max-width: 400px;
      margin: auto;
    }

    .flipper {
      position: relative;
      transition: transform 0.8s ease;
      transform-style: preserve-3d;
    }

    .flip-container.flip .flipper {
      transform: rotateY(180deg);
    }

    .front,
    .back {
      backface-visibility: hidden;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .front {
      z-index: 2;
    }

    .back {
      transform: rotateY(180deg);
      z-index: 1;
    }

    .back .back-content {
      padding: 20px;
      text-align: center;
    }

    .back .back-content h3 {
      margin-bottom: 10px;
      color: #111;
    }

    .back .back-content p {
      margin: 6px 0;
      color: #444;
      font-size: 14px;
    }

    .back .ticket-note {
      background-color: #f0f0f0;
      text-align: center;
      padding: 12px;
      font-size: 13px;
      color: #777;
      border-top: 1px solid #ddd;
    }
  </style>
</head>

<body>
  <div class="flip-container" id="ticketCard">
    <div class="flipper">
      <!-- FRONT SIDE -->
      <div class="ticket-container front">
        <div class="ticket-header">
          <div class="ticket-info">
            <h2><%= eventName %></h2>
            <p>Venue: <%= venue %></p>
            <p>Time: <%= time %></p>
            <p>Date: <%= date %></p>
          </div>
        </div>

        <div class="ticket-actions">
          Tap for support, details & more actions
        </div>

        <div class="ticket-middle">
          <p><%= bookings %> x <%= ticketName %></p>
          <h3>ENTRY PASS</h3>
          <p>Booked by: <%= UserName %></p>
          <img src="data:image/svg+xml;utf8,<%= encodeURIComponent(QRCodeSVG) %>" class="qr-image" alt="QR Code" />
        </div>

        <div class="ticket-note">
          Show this QR at entry. No re-entry allowed once scanned.
        </div>

        <div class="ticket-footer">
          <span>Total Amount</span>
          <strong>₹ <%= amount %></strong>
        </div>
      </div>

      <!-- BACK SIDE -->
      <div class="ticket-container back">
        <div class="back-content">
          <h3>Need Help?</h3>
          <p>Email: <EMAIL></p>
          <p>Phone: +91-9876543210</p>
          <p>Terms: No refund after booking. Entry allowed till 6:30 PM.</p>
        </div>
        <div class="ticket-note">Tap again to return</div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener("DOMContentLoaded", () => {
      const card = document.getElementById("ticketCard");

      // Make the whole ticket clickable for flipping
      card.addEventListener("click", () => {
        card.classList.toggle("flip");
      });

      // Add a click listener to the 'support' text for more info
      const supportText = document.querySelector(".ticket-actions");
      supportText.addEventListener("click", (e) => {
        e.stopPropagation();
        alert("For support, contact: <EMAIL>\nOr call: +91-9876543210");
      });
    });
  </script>
</body>

</html>
