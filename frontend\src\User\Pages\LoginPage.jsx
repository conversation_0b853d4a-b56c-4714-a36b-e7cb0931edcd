import { useState, useEffect, useRef } from "react";
import { TextField } from "@mui/material";
import { Button } from "react-bootstrap"; // Assuming you want to keep Bootstrap button for consistency
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import auth from "../../firebaseConfig";
import apiService from "../../services/apiService";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { BsXCircle } from "react-icons/bs";
import "./LoginPage.css";
import indian_flag from "../../Assets/flag.png";
import SyncLoader from "react-spinners/SyncLoader";
import logo from "../../Assets/logo.png"; // Assuming you might want a logo on this login page too
import loginPlaceholder from "../../Assets/login_placeholder.jpg"; // Assuming you might want a placeholder image

/* global grecaptcha */

function LoginPage() {
  const [phone, setPhone] = useState("");
  const [user, setUser] = useState(null);
  const [otp, setOtp] = useState("");
  const [showOTPField, setShowOTPField] = useState(false);
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const source = queryParams.get("source");
  const recaptchaRef = useRef(null);

  useEffect(() => {
    let countdown;
    if (isResendDisabled && showOTPField) {
      countdown = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer <= 1) {
            clearInterval(countdown);
            setIsResendDisabled(false);
            return 60;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }
    return () => clearInterval(countdown);
  }, [isResendDisabled, showOTPField]);

  const setupRecaptcha = () => {
    if (!recaptchaRef.current) {
      console.error("reCAPTCHA element not found.");
      return;
    }

    if (!window.recaptchaVerifier) {
      window.recaptchaVerifier = new RecaptchaVerifier(auth, "recaptcha", {
        size: "invisible",
        callback: (response) => {
          console.log("Response", response);
        },
        "expired-callback": () => {
          console.log("Recaptcha expired, please try again.");
        },
      });

      window.recaptchaVerifier.render().then((widgetId) => {
        window.recaptchaWidgetId = widgetId;
      });
    } else {
      if (typeof grecaptcha !== "undefined" && window.recaptchaWidgetId) {
        grecaptcha.reset(window.recaptchaWidgetId);
      }
    }
  };

  const sendOTP = async () => {
    setLoading(true);
    setupRecaptcha();
    const phoneNumber = "+91" + phone;
    console.log("phoneNumber", phoneNumber);
    const appVerifier = window.recaptchaVerifier;
    try {
      const confirmation = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        appVerifier
      );
      setTimer(60);
      setUser(confirmation);
      setShowOTPField(true);
      setIsResendDisabled(true);
    } catch (err) {
      console.error("Error during OTP send:", err.message);

      if (typeof grecaptcha !== "undefined" && window.recaptchaWidgetId) {
        grecaptcha.reset(window.recaptchaWidgetId);
      }

      alert(`Failed to send OTP. Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const verifyOtp = async (event) => {
    event.preventDefault();
    setLoading(true);
    try {
      console.log("OTP", otp);
      const data = await user.confirm(otp);
      console.log("OTP Data", data);
      if (data) {
        if (localStorage.getItem("userData")) {
          localStorage.clear();
        }
        apiService.setData(data.user);
        const isUser = await apiService.get(
          `/user/getUserWithID/${data.user.phoneNumber}`
        );
        if (isUser) {
          apiService.setData(isUser);
          if (source === "event") {
            window.location.href = "https://hfs-live-31172.web.app";
          } else if (source === "login") {
            window.location.href = "/";
          } else if (source === "tickets") {
            window.location.href = "/paymentgateway";
          } else if (source === "yourtickets") {
            window.location.href = "/yourtickets";
          } else {
            window.location.href = "/";
          }
        } else {
          window.location.href = "/signup";
        }
      }
    } catch (err) {
      console.error("INVALID OTP", err.message);
      alert("Invalid OTP. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onClose = () => {
    navigate("/");
  };

  return (
    <>
      <div className="login-modal d-flex flex-row align-items-start justify-content-evenly">
        <div className="d-flex flex-column justify-content-start align-items-center w-100">
          {/* Logo container, similar to the first component */}
          <div className="w-100 p-5 mb-md-5" style={{ maxWidth: 500 }}>
            <img src={logo} alt="Logo" className="w-100" />
          </div>

          <div
            className="d-flex flex-column justify-content-center align-items-center w-100"
            style={{ maxWidth: 500 }}
          >
            {/* Close button for the modal-like behavior */}

            <h1 className="book mb-4 fw-bold fs-4">
              Book <span className="your-fs fs-4"> Your Front Seat</span>
            </h1>

            {!showOTPField && (
              <>
                <div className="w-100 p-3">
                  <div className="div-num">
                    <p className="enter-number fs-6 fw-light mb-1">
                      Login with your mobile number
                    </p>
                  </div>
                  <div className="mb-4">
                    <div className="input-group mb-3">
                      <span
                        className="input-group-text input-border-primary countryflag"
                        style={{ borderColor: "#2C9CF0" }}
                      >
                        <img
                          className="me-2 mt-1 indian_flag"
                          src={indian_flag}
                          alt="India"
                        />{" "}
                        <span className="countryCode">+91</span>
                      </span>
                      <input
                        type="tel"
                        className="form-control mobileinput input-border-primary"
                        name="mobileNumber"
                        placeholder="e.g. 9226773937"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        required
                        style={{ borderColor: "#2C9CF0" }}
                      />
                    </div>
                  </div>
                  <Button
                    onClick={sendOTP}
                    className="w-100"
                    disabled={loading || !phone || phone.length !== 10}
                    style={{
                      backgroundColor: "#2C9CF0",
                      borderColor: "#2C9CF0",
                      margin: 0,
                    }}
                  >
                    {loading ? (
                      <SyncLoader
                        animation="border"
                        color="#FFFF"
                        size="10"
                        speedMultiplier={1}
                        margin={4}
                      />
                    ) : (
                      "Get OTP"
                    )}{" "}
                  </Button>
                </div>
              </>
            )}

            {showOTPField && (
              <div className="w-100 p-3">
                <div className="div-num">
                  <p className="enter-number fs-6 fw-light mb-1">Enter OTP</p>
                </div>
                <input
                  type="password"
                  className="form-control mobileinput input-border-primary"
                  name="otp"
                  placeholder="e.g. xxxxxx"
                  onChange={(e) => setOtp(e.target.value)}
                  required
                  style={{ borderColor: "#2C9CF0" }}
                />

                <br />
                <div>
                  <Button
                    className="w-100"
                    onClick={verifyOtp}
                    disabled={loading || !otp || otp.length !== 6}
                    style={{
                      backgroundColor: "#2C9CF0",
                      borderColor: "#2C9CF0",
                      margin: 0,
                    }}
                  >
                    {loading ? (
                      <SyncLoader
                        animation="border"
                        color="#FFFF"
                        size="10"
                        speedMultiplier={1}
                        margin={4}
                      />
                    ) : (
                      "Login"
                    )}{" "}
                  </Button>
                </div>
                <div className="resend-otp">
                  <Link
                    className={`resend-text ${
                      isResendDisabled ? "isResendDisabled" : "isResendEnabled"
                    }`}
                    onClick={sendOTP}
                    disabled={isResendDisabled || loading}
                  >
                    Resend OTP
                  </Link>
                  {timer === 60 ? (
                    ""
                  ) : (
                    <span className="countdown"> in {timer} seconds</span>
                  )}
                </div>
              </div>
            )}
          </div>
          {/* Sign up and Help links, similar to the first component */}
          {/* <div className="mt-5">
            <p className="text-center mt-3 fw-medium mb-2 fs-6 text-decoration-none">
              Don't have an account?{" "}
              <Link
                to="/signup"
                className="text-decoration-none"
                style={{ color: "#2C9CF0" }}
              >
                Sign up
              </Link>
            </p>
            <p className="text-center fw-light">
              We are here to help{" "}
              <Link
                to="/forgot-password"
                className="text-decoration-none"
                style={{ color: "#2C9CF0" }}
              >
                <EMAIL>
              </Link>
            </p>
          </div> */}
          <div className="text-center mt-3">
            <Link
              htmlFor="terms"
              className="text-terms-acc text-decoration-none"
              to="/terms-condition"
            >
              I accept the Terms of Use & Privacy Policy
            </Link>
          </div>
        </div>

        {/* Placeholder image on the right, similar to the first component */}
        <div className="logo-container w-50 bg-black h-100 d-md-block d-none">
          <img
            src={loginPlaceholder}
            alt="Login Placeholder"
            className="object-fit-fill"
          />
        </div>
      </div>
      <div id="recaptcha" ref={recaptchaRef}></div>
    </>
  );
}

export default LoginPage;
