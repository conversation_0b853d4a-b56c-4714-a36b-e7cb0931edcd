.category-list-main {
  max-width: 1280px;
  width:100%;
  padding: 1rem;
}
.category-list-search-main {
  max-width: 960px;
  width:100%;
  padding: 1rem;
}
.category-type-card{
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.horizontalLine {
  width: 100%;
  border-color: rgb(143, 143, 143);
}
#forsmallScreen {
  display: none;
}
.browseHeading {
  
  margin-top: 2rem;
  font-size: 20px;
  font-weight: 600;
}
.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}
.carousel-search-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}
.carousel-wrapper {
  display: flex;
  overflow: hidden;
  width: 100%;
  margin: auto;
}
.carousel-content {
  display: flex;
  width: 100%;
  overflow: hidden;
  transition: transform 1.5s ease-in-out;
}
.carousel-slide {
  display: flex;
  width: 100%;
}

.categorycard {
  width: calc(25% - 20px);
  height: 170px !important;
  margin-left: 25px;
  border: none !important;
}
.category-title {
  padding: 0.3rem 0rem;
  border-radius: 1.5rem;
  background-color: rgba(4, 9, 44, 1);
  color: white;
  
  width: 100%;
  max-width: 14rem;
  font-weight: bold;
  position: relative;
  cursor: pointer;
  text-align: center;
  
}
.category-placeholder {
  border-radius: 1.5rem;
  color: white;
  
  background-color: rgb(233, 233, 233);
  width: 100%;
  font-weight: bold;
  position: relative;
  cursor: pointer;
  text-align: center;
}
.category-control-button {
  border: none !important;
  background: none;
  cursor: pointer;
  border-radius: 50%;
}
.categoryblock {
  padding: 0.3rem 0rem;
  border-radius: 1.5rem;
  background-color: rgba(4, 9, 44, 1);
  color: white;
  
  width: 100%;
  font-weight: bold;
  position: relative;
  cursor: pointer;
}
.card-img {
  height: 150px;
  border-radius: 1rem;
  position: relative;
  top : 1%;
  cursor: pointer;
}
.category-img {
  width: 100%;
  height: 150px;
  border-radius: 1rem;
  z-index: -1;
}

.carousel-control {
  width: 30px;
  background: none;
  color: black;
  border: none;
  cursor: pointer;
}
.carousel-control.prev {
  margin-left: -12px;
}
.carousel-control.next {
  margin-left: 17px;
}
.carousel-control.disabled {
  visibility: hidden;
}

@media screen and (max-width:1310px) {
  .categorycard {
    width: calc(25% - 20px);
    height: 160px !important;
    margin-left: 40px;
    border: none !important;
  }
  .categoryblock {
    padding: 0.3rem 0rem;
    border-radius: 1.5rem;
    background-color: #04092C;
    color: white;
    
    width: 150px;
    height: 35px;
    font-weight: bold;
    z-index: 1;
    border-bottom: 4px solid white;
    border-left: 4px solid white;
    border-right: 4px solid white;
  }
  .card-img {
    height: 140px;
    border-radius: 1rem;
    position: relative;
    top: 3%;
  }
  .category-img{
    width: 100%;
    height: 140px;
    border-radius: 1rem;
  }
}
@media screen and (max-width:1080px){
  .categorycard {
    width: calc(25% - 20px);
    height: 145px !important;
    margin-left: 30px;
    border: none !important;
  }
 
  .categoryblock {
    padding: 0.3rem 0rem;
    border-radius: 1.5rem;
    background-color: #04092C;
    color: white;
    
    width: 135px;
    height: 35px;
    font-weight: bold;
    z-index: 1;
    border-bottom: 3px solid white;
    border-left: 3px solid white;
    border-right: 3px solid white;
  }
  .card-img {
    height: 130px;
    border-radius: 1rem;
    position: relative;
    top: 3%;
  }
  .category-img{
    width: 100%;
    height: 130px;
    border-radius: 1rem;
  }
}
@media screen and (max-width:915px){
  
  .categorycard {
    width: calc(25% - 20px);
    height: 110px !important;
    margin-left: 30px;
    border: none !important;
  }
  .carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  .carousel-search-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }
  .category-title {
    padding: 0.2rem;
    border-radius: 1.5rem;
    background-color: rgba(4, 9, 44, 1);
    color: white;
    
    width: 100%;
    max-width: 10rem;
    font-weight:500;
    position: relative;
    cursor: pointer;
    text-align: center;
    font-size: 0.8rem
  }
  .categoryblock {
    padding: 0.1rem 0rem;
    border-radius: 1.5rem;
    background-color: #04092C;
    color: white;
    
    width: 135px;
    height: 35px;
    font-weight: bold;
    font-size: medium;
    z-index: 1;
    border-bottom: 3px solid white;
    border-left: 3px solid white;
    border-right: 3px solid white;
  }
  .card-img {
    height: 100px;
    border-radius: 1rem;
    position: relative;
    top: -3%;
  }
  .category-img{
    width: 100%;
    height: 90px;
    border-radius: 1rem;
  }
}
@media screen and (max-width:880px){
  .categorycard {
    width: calc(35% - 30px);
    height: 110px !important;
    margin-left: 25px;
    border: none !important;
  }
  .categoryblock {
    padding: 0.1rem 0rem;
    border-radius: 1.5rem;
    background-color: #04092C;
    color: white;
    
    width: 65%;
    height: 35px;
    font-weight: bold;
    font-size: small;
    z-index: 1;
    border-bottom: 3px solid white;
    border-left: 3px solid white;
    border-right: 3px solid white;
  }
  .card-img {
    height: 100px;
    border-radius: 1rem;
    position: relative;
    top: 3%;
  }
  .category-img{
    width: 100%;
    height: 90px;
    border-radius: 1rem;
  }
}
@media screen and (max-width:560px){
  .browseCarousel{
    margin-top: 1rem;
  ;
  }

  .category-title {
    padding: 0.1rem;
    border-radius: 1.5rem;
    background-color: rgba(4, 9, 44, 1);
    color: white;
    
    width: 100%;
    max-width: 14rem;
    font-weight:500;
    position: relative;
    cursor: pointer;
    text-align: center;
    font-size: 0.6rem
  }

  .category-control-button {
    border: none !important;
    background: none;
    cursor: pointer;
    border-radius: 50%;
    padding: 0;
  }

  .carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }
  .carousel-search-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }
  .categorycard {
    width: calc(55% - 10px);
    height: 97px !important;
    margin-left: 10px;
    border: none !important;
  }
  .browseHeading {
    margin-top: 0.5rem;
    font-size: 20px;
  }
  #forsmallScreen {
    display: block;
  }
  #forlargeScreen {
    display: none;
  }
  .categoryblock{
    position: relative;
    top: 2%;
    padding: 0.1rem 0.5rem !important;
    border-radius: 1rem;
    background-color: #04092C;
    color: white;
    
    width: 80%;
    height: 21px;
    font-weight:bold;
    font-size: 12px;
    z-index: 1;
    border-bottom: 2px solid white;
    border-left: 2px solid white;
    border-right: 2px solid white;
  }
 
  .card-img {
    height: 75.35px !important;
    border-radius: 1rem;
    position: relative;
    top: 5%;
  }
  .category-img {
    width: 100%;
    height: 75.35px;
    border-radius: 1rem;
  }
  .carousel-control {
    background: none;
    color: black;
    border: none;
    cursor: pointer;
    margin-top: 5%;
  }
  .carousel-control.next {
    margin-left: 1%;
  }
}

.browse-category-skeleton-box {
  background: #e0e0e0;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
  animation: shimmer 1.5s infinite linear;
}

.browse-category-skeleton-img {
  background: #e0e0e0;
}

.browse-category-skeleton-category-img {
  width: 100%;
  height: 150px;
  border-radius: 8px;
}

.browse-category-skeleton-line {
  height: 16px;
  background: #e0e0e0;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.browse-category-skeleton-box::after {
  content: '';
  position: absolute;
  top: 0;
  left: -150px;
  height: 100%;
  width: 150px;
  background: linear-gradient(to right, transparent 0%, #f0f0f0 50%, transparent 100%);
  animation: loading 1.2s infinite;
}

@keyframes loading {
  0% {
    left: -150px;
  }
  100% {
    left: 100%;
  }
}