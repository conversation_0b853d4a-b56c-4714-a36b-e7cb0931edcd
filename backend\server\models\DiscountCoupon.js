const MongoDB = require('mongoose');

const DiscountCouponSchema = new MongoDB.Schema({
    Name_of_Coupon: {
        type: String,
    },
    coupon_Code: {
        type: String,
    },
    discount_Type: {
        type: String,
    },
    coupon_Used_By: [
        { type: String }
    ],
    user: {
        type: String,
    },
    event: {
        type: MongoDB.Schema.Types.ObjectId,
        ref: 'CeremonyModel',
    },
    coupon_Code_Valid_From: {
        type: String,
        required: true,
    },
    coupon_Code_Valid_To: {
        type: String,
        required: true,
    },
    DicountValue: {
        type: Number,
        default: 0
    },
    createdAt: {
        type: Number,
        default: Date.now,
    },


});

const DiscountCouponModel = MongoDB.model("DiscountCouponData", DiscountCouponSchema);
module.exports = DiscountCouponModel;