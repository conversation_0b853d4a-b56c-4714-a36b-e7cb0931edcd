import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "./DateSelect.css";
import LocationModal from "./LocationModal";
import NavBar from "./NavBar";
import { BsArrowLeft } from "react-icons/bs";
import SyncLoader from "react-spinners/SyncLoader";
import "./SelectTickets.css";

const DateSelect = () => {
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [dates, setDates] = useState([]);
  const [selectedTime, setSelectedTime] = useState();
  const [selectedDate, setSelectedDate] = useState();
  const navigate = useNavigate();
  const location = useLocation();
  const { event } = location.state; // Correctly destructure the event object
  const [isProceeding, setIsProceeding] = useState(false);

  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  // Function to generate all dates between start and end date
  const getDatesInRange = (startDate, endDate) => {
    const date = new Date(startDate);
    const end = new Date(endDate);
    const datesArray = [];

    while (date <= end) {
      datesArray.push(new Date(date));
      date.setDate(date.getDate() + 1); // Move to the next day
    }

    return datesArray;
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
  };

  const formatTime = (timeString) => {
    const dateObj = new Date(`1970-01-01T${timeString}Z`);
    let hours = dateObj.getUTCHours();
    const minutes = String(dateObj.getUTCMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;
    return `${hours}:${minutes} ${ampm}`;
  };

  useEffect(() => {
    if (event && event.event_starts_date && event.event_ends_date) {
      const startDate = new Date(event.event_starts_date);
      const endDate = new Date(event.event_ends_date);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error(
          "Invalid start or end date",
          event.event_starts_date,
          event.event_ends_date
        );
        return;
      }

      const generatedDates = getDatesInRange(startDate, endDate);

      console.log("Generated Dates:", generatedDates);
      console.log("Event:", event);

      if (generatedDates.length > 0) {
        setDates(generatedDates);
      }
    }

    console.log(
      "EVENT Multicity Arrayy : ",
      event.multiCityArray,
      "\n Tickets : ",
      event.ticket
    );
  }, [event]);

  // Handle date selection and navigate to the ticket page
  const handleDateSelection = () => {
    navigate(
      `/ticketpage/${event.event_name.replace(/\s+/g, "_")}/${event.unique_id}`,
      {
        state: {
          date_for_booked_event: selectedDate.toISOString().split("T")[0],
          selectedCity: location.state.selectedCity
            ? location.state.selectedCity
            : event.event_city,
          selectedVenue: location.state.selectedVenue
            ? location.state.selectedVenue
            : event.event_Venue,
          event: event,
        },
      }
    );
  };

  const handleSet = (date) => {};

  return (
    <>
      <div className="select-ticket">
        {/* Show LocationModal */}
        {showLocationModal && (
          <LocationModal
            onClose={handleCloseLocationModal}
            onLocationChange={handleLocationChange}
          />
        )}
        <div id="hideforSmallDevices">
          <NavBar
            userLocation={userLocation}
            onNext={() => setShowLocationModal(true)}
            disableMargin={true}
          />
        </div>

        <div className="ticketlist">
          <div
            className="d-flex flex-row justify-content-between align-items-center m-0"
            id="largedeviceview"
          >
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center">
              <span
                className="backarrowlink h-100 link-dark "
                onClick={() => navigate(-1)}
              >
                <BsArrowLeft size={30} />
              </span>
            </div>
            <div className="col-10 col-10">
              <div className="d-flex flex-column justify-content-center">
                <span className="event-name">{event.event_name}</span>
                <span className="event-detail">
                {`${location.state.selectedCity
                    ? location.state.selectedCity
                    : event.event_city} | ${formatDate(event.event_starts_date)} | ${formatTime(event?.event_starts_Time)}`}
                </span>
              </div>
            </div>
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center"></div>
          </div>

          <div id="smalldeviceview">
            <div className="d-flex flex-row align-items-center shadow-sm py-1">
              <div className="col-2 d-flex justify-content-center align-items-center">
                <span className=" link-dark" onClick={() => navigate(-1)}>
                  <BsArrowLeft size={24} />
                </span>
              </div>
              <div className="col-8 d-flex flex-column justify-content-center align-items-center">
                <span className="event-name fw-semibold">
                  {event.event_name}
                </span>
                
                <span className="event-detail">
                  {`${formatDate(event.event_starts_date)} ${location.state.selectedCity
                    ? location.state.selectedCity
                    : event.event_city}`}
                </span>
                
              </div>
              <div className="col-2 d-flex justify-content-center align-items-center"></div>
            </div>
          </div>
          {event && !event.eventForMultiCity ? (
            <div className="p-3 p-md-0">
              <div className="fw-semibold mb-3" style={{ fontSize: 20 }}>
                Select Date
              </div>
              <div className="row sm-row mt-3 d-flex flex-row justify-content-start align-content-start p-0 gap-2 mx-0">
                {dates
                  .map((date) => new Date(date)) // Ensure dates are Date objects
                  .filter((date) => {
                    if (!event.ticket || event.ticket.length === 0)
                      return false;
                    const dateString = date.toISOString();

                    return event.ticket.some((ticket) => {
                      const ticketDate = new Date(ticket.ticket_for_Date);
                      if (isNaN(ticketDate.getTime())) {
                        console.error(
                          "Invalid ticket date:",
                          ticket.ticket_for_Date
                        );
                        return false;
                      }
                      return ticketDate.toISOString() === dateString;
                    });

                    // return event.ticket.some(ticket => new Date(ticket.ticket_for_Date).toISOString() === dateString);
                  })
                  .map((date, index) => {
                    const dateString = date.toISOString();

                    // Determine if tickets are sold out or selling fast
                    const isSoldOut = event.ticket.every(
                      (ticket) =>
                        (ticket.ticket_for_Date === dateString &&
                          ticket.ticket_avability === 0) ||
                        new Date(ticket.ticket_for_Date) < new Date()
                    );
                    const isSellingFast =
                      !isSoldOut &&
                      event.ticket.some(
                        (ticket) =>
                          ticket.ticket_for_Date === dateString &&
                          ticket.ticket_avability > 0
                      );

                    return (
                      <div
                        className="sm-custom col-lg-3 col-md-3 col-sm-2 col-4 mb-3 mx-0 px-0"
                        key={index}
                      >
                        <p
                          className={`dateSpanStyle text-center ${
                            isSoldOut ? "soldOut" : ""
                          } ${
                            (selectedDate
                              ? selectedDate.toISOString().split("T")[0]
                              : "") === date.toISOString().split("T")[0]
                              ? "highlightSelection"
                              : ""
                          }`}
                          onClick={() => setSelectedDate(date)}
                          style={
                            isSoldOut
                              ? { backgroundColor: "#6A6A6A", color: "white" }
                              : {}
                          }
                        >
                          {date.toLocaleDateString("en-GB", {
                            day: "2-digit",
                            month: "short",
                            year: "2-digit",
                          })}
                        </p>
                        {isSoldOut ? (
                          <div className="text-center text-danger fw-semibold">
                            Sold Out
                          </div>
                        ) : (
                          <div className="text-center text-success fw-semibold">
                            Selling Fast
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
              <div className="fw-semibold mb-3" style={{ fontSize: 20 }}>
                Select Time
              </div>
              <div className="row sm-row mt-3 row sm-row mt-3 d-flex flex-row justify-content-start align-content-start p-0 gap-2 mx-0">
                <div className="col-lg-3">
                  {event.event_starts_Time ? (
                    <p
                      className={`dateSpanStyle text-center ${
                        selectedTime === event.event_starts_Time
                          ? "highlightSelection"
                          : ""
                      }`}
                      onClick={() => setSelectedTime(event.event_starts_Time)}
                    >
                      {formatTime(event.event_starts_Time)}
                    </p>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="p-3 p-md-0">
              <div className="fs-5 fw-semibold mb-3">Select Date</div>
              <div className="row sm-row mt-3 row sm-row mt-3 d-flex flex-row justify-content-start align-content-start p-0 gap-2 mx-0">
                {event.multiCityArray &&
                  event.multiCityArray.length > 0 &&
                  event.multiCityArray
                    .filter((item) => {
                      if (!event.ticket || event.ticket.length === 0)
                        return false;

                      const dateString = new Date(item.event_date)
                        .toISOString()
                        .split("T")[0];
                      if (
                        item.event_city === location.state.selectedCity &&
                        item.event_venue === location.state.selectedVenue
                      ) {
                        return event.ticket.some((ticket) => {
                          const ticketDate = new Date(ticket.ticket_for_Date);
                          if (isNaN(ticketDate.getTime())) {
                            console.error(
                              "Invalid ticket date:",
                              ticket.ticket_for_Date
                            );
                            return false;
                          }

                          return (
                            ticketDate.toISOString().split("T")[0] ===
                            dateString
                          );
                        });
                      }
                    })
                    .map((item, index) => {
                      const dateObj = new Date(item.event_date);
                      const dateString = dateObj.toISOString().split("T")[0];

                      const isSoldOut = event.ticket.every(
                        (ticket) =>
                          ticket.ticket_for_Date.split("T")[0] === dateString &&
                          ticket.ticket_avability === 0
                      );

                      const isSellingFast =
                        !isSoldOut &&
                        event.ticket.some(
                          (ticket) =>
                            ticket.ticket_for_Date.split("T")[0] ===
                              dateString && ticket.ticket_avability > 0
                        );

                      return (
                        <div
                          className="sm-custom col-lg-3 col-md-3 col-sm-2 col-4 mb-3 mx-0 px-0"
                          key={index}
                        >
                          <p
                            className={`dateSpanStyle text-center ${
                              isSoldOut ? "soldOut" : ""
                            } ${
                              (selectedDate
                                ? selectedDate.toISOString().split("T")[0]
                                : "") === dateString
                                ? "highlightSelection"
                                : ""
                            }`}
                            onClick={() => {
                              setSelectedDate(dateObj);
                            }}
                            style={
                              isSoldOut
                                ? { backgroundColor: "#6A6A6A", color: "white" }
                                : {}
                            }
                          >
                            {dateObj.toLocaleDateString("en-GB", {
                              day: "2-digit",
                              month: "short",
                              year: "2-digit",
                            })}
                          </p>
                          {isSoldOut ? (
                            <div className="text-center text-danger fw-semibold">
                              Sold Out
                            </div>
                          ) : (
                            <div className="text-center text-success fw-semibold">
                              Selling Fast
                            </div>
                          )}
                        </div>
                      );
                    })}
              </div>
              <div className="fs-5 fw-semibold mb-3">Select Time</div>
              <div className="row sm-row mt-3 row sm-row mt-3 d-flex flex-row justify-content-start align-content-start p-0 gap-2 mx-0">
                {event.multiCityArray &&
                event.multiCityArray.length > 0 &&
                event.multiCityArray.filter((array) => {
                  const selectedDateString = selectedDate
                    ? selectedDate.toISOString().split("T")[0]
                    : null;
                  return (
                    selectedDateString &&
                    array.event_date === selectedDateString &&
                    array.event_city === location.state.selectedCity &&
                    array.event_venue === location.state.selectedVenue
                  );
                }).length > 0 ? (
                  event.multiCityArray
                    .filter((array) => {
                      const selectedDateString = selectedDate
                        ? selectedDate.toISOString().split("T")[0]
                        : null;
                      return (
                        selectedDateString &&
                        array.event_date === selectedDateString &&
                        array.event_city === location.state.selectedCity &&
                        array.event_venue === location.state.selectedVenue
                      );
                    })
                    .map((showTime, index) =>
                      showTime.show_time.map((time, index) => (
                        <div className="w-25 px-0" key={index}>
                          <p
                            className={`dateSpanStyle text-center text-nowrap ${
                              selectedTime === time.time
                                ? "highlightSelection"
                                : ""
                            }`}
                            onClick={() => setSelectedTime(time.time)}
                          >
                            {formatTime(time.time)}
                          </p>
                        </div>
                      ))
                    )
                ) : (
                  <div></div> // Optional: Handle case when no events match
                )}
              </div>
            </div>
          )}
        </div>

        <div className="footerPart py-2 mt-5" id="largedeviceview">
          <div className="footer-content">
            <button
              className={`booking-btn fs-5 rounded-3 text-white ${
                isProceeding || !selectedDate || !selectedTime
                  ? "btnisDisabled"
                  : "btnisEabled"
              }`}
              disabled={isProceeding || !selectedDate || !selectedTime}
              onClick={handleDateSelection}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>
        <div className="smallfooterPart py-2" id="smalldeviceview">
          <div className="footer-content px-3">
            <button
              className={`btn-booking text-white ${
                isProceeding || !selectedDate || !selectedTime
                  ? "btnisDisabled"
                  : "btnisEabled"
              }`}
              disabled={isProceeding || !selectedDate || !selectedTime}
              onClick={handleDateSelection}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default DateSelect;
