import React, { useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction"; // Allows date clicking

const CalendarComponent = () => {
  const [events, setEvents] = useState([
    { title: "Event title here...", start: "2024-05-01T10:30:00" },
    { title: "Cinco de Mayo", start: "2024-05-05", allDay: true },
    { title: "Mother's Day", start: "2024-05-12", allDay: true },
    { title: "Memorial Day", start: "2024-05-27", allDay: true },
    { title: "First Day of LGBTQ+...", start: "2024-06-01", allDay: true },
  ]);

  return (
    <div style={{ width: "90%", margin: "auto", paddingTop: "20px" }}>
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView="dayGridMonth"
        headerToolbar={{
          left: "prev,next today",
          center: "title",
          right: "dayGridMonth,timeGridWeek,timeGridDay",
        }}
        events={events}
        selectable={true}
        editable={true}
      />
    </div>
  );
};

export default CalendarComponent;
