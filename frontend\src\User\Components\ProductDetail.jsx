import { useState } from "react";
import { Calendar, Clock } from "react-feather";
import { FaFacebook } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { FiLink } from "react-icons/fi";
import { GoShareAndroid } from "react-icons/go";
import { IoLogoWhatsapp } from "react-icons/io";
import { SlArrowDown } from "react-icons/sl";
import { TbMapPin } from "react-icons/tb";
import { Link, useNavigate } from "react-router-dom";
import SyncLoader from "react-spinners/SyncLoader";
import fallAvatarImage from "../../Assets/Avatar.png";
import ReduceText from "../Interface";
import { MulticityVenueModal } from "./MulticityVenueModal";
import "./ProductDetail.css";

export const ProductDetailSkeleton = () => {
  return (
    <div className="productDetailDiv product-detail-skeleton h-100">
      <div className="detailDiv">
        <div className="event-image p-3">
          <div className="product-detail-skeleton-box product-detail-skeleton-img"></div>
        </div>
        <div className="event-info p-3">
          <div className="product-detail-skeleton-box product-detail-skeleton-title mb-3"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-subtitle mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-line mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-line mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-button mt-4"></div>
        </div>
      </div>

      <div className="aboutArtistDiv p-3 mt-5">
        <div className="w-100">
          <div className="product-detail-skeleton-box product-detail-skeleton-title mb-3"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-title mb-3"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-title mb-3"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-paragraph mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-paragraph mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-paragraph mb-2"></div>
        </div>
        <div className="w-100">
          <div className="product-detail-skeleton-box product-detail-skeleton-paragraph mb-2"></div>
          <div className="product-detail-skeleton-box product-detail-skeleton-paragraph mb-2"></div>
        </div>
      </div>
    </div>
  );
};
function ProductDetail({ eventDetail }) {
  const navigate = useNavigate();
  const event = eventDetail[0];
  const [loading, setLoading] = useState(false);
  const [showEventVenueModal, setShowEventVenueModal] = useState(false);

  const handleTicketSelection = () => {
    setLoading(true);
    if (event.eventForMultiCity) {
      console.log("Before Navigate : ", event);
      navigate(`/${event.event_name.replace(/\s+/g, "_")}/select-city`, {
        state: { event: event },
      });
    } else if (
      event.isAllSession ||
      event.event_starts_date === event.event_ends_date
    ) {
      navigate(
        `/ticketpage/${event.event_name.replace(/\s+/g, "_")}/${
          event.unique_id
        }`,
        { state: { date_for_booked_event: event.event_starts_date } }
      );
      setLoading(false);
    } else {
      navigate(
        `/dateselect/${event.event_name.replace(/\s+/g, "_")}/${
          event.unique_id
        }`,
        { state: { event: event } }
      );
      setLoading(false);
    }
  };

  const handleCloseEventVenueModal = () => {
    setShowEventVenueModal(false);
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day} ${month} '${year}`;
  };

  const formatTime = (timeString) => {
    if (timeString == null || timeString === undefined || timeString === "") {
      timeString = "00:00:00";
    }

    const dateObj = new Date(`1970-01-01T${timeString}Z`);
    let hours = dateObj.getUTCHours();
    const minutes = String(dateObj.getUTCMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;

    const formattedTime = `${hours}:${minutes} ${ampm}`;
    return formattedTime;
  };

  const lowestTicket = (ticketprices) => {
    let lowestPrice = ticketprices[0].ticket_price;
    for (let i = 1; i < ticketprices.length; i++) {
      if (
        ticketprices[i].ticket_price !== 0 &&
        lowestPrice > ticketprices[i].ticket_price
      ) {
        lowestPrice = ticketprices[i].ticket_price;
      }
    }
    return lowestPrice;
  };

  const [isExpanded, setIsExpanded] = useState(false);
  const toggleReadMore = () => {
    setIsExpanded(!isExpanded);
  };

  // copy link functionality
  const shareLink = window.location.href;
  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(shareLink)
      .then(() => {
        alert("Link copied to clipboard!");
      })
      .catch((err) => {
        alert("Failed to copy the link!");
      });
  };

  const handleShareWhatsApp = () => {
    const whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(
      shareLink
    )}`;
    window.open(whatsappUrl, "_blank");
  };

  const handleShareFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
      shareLink
    )}`;
    window.open(facebookUrl, "_blank");
  };

  const handleShareTwitter = () => {
    const tweetText = "Check out this link!";
    const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(
      shareLink
    )}&text=${encodeURIComponent(tweetText)}`;
    window.open(twitterUrl, "_blank");
  };

  const openGoogleMaps = () => {
    const venue = `${event.event_venue}, ${event.event_city}`;
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      venue
    )}`;
    window.open(googleMapsUrl, "_blank");
  };

  return (
    <>
      <div className="productDetailDiv">
        <MulticityVenueModal
          open={showEventVenueModal}
          handleClose={handleCloseEventVenueModal}
          multiCityArray={
            event?.multiCityArray?.length > 0
              ? event.multiCityArray
              : [
                  {
                    event_city: event.event_city,
                    event_venue: event.event_venue,
                    event_date: event.event_starts_date,
                  },
                ]
          }
        />
        <div className="detailDiv">
          <div className="event-image p-3">
            <img
              src={`${event?.cover_image}`}
              className="w-100 h-100 event-image-internal"
              alt="Event"
            />
          </div>
          <div className="event-info p-3">
            <div className="d-flex flex-column">
              <div className="d-flex justify-content-between align-items-center mb-1">
                <span className="event_name">
                  {ReduceText(event?.event_name || "")}
                </span>
                <div className="dropdown">
                  <span data-bs-toggle="dropdown" aria-expanded="false">
                    <GoShareAndroid size={25} style={{ color: "#2C9CF0" }} />
                  </span>
                  <ul className="dropdown-menu">
                    <li>
                      <button
                        className="dropdown-item d-flex justify-content-between"
                        onClick={handleCopyLink}
                        type="button"
                      >
                        <span>Copy link </span>{" "}
                        <FiLink className="primarycolor" />
                      </button>
                    </li>
                    <li>
                      <button
                        className="dropdown-item d-flex justify-content-between"
                        onClick={handleShareWhatsApp}
                        type="button"
                      >
                        <span>WhatsApp </span>{" "}
                        <IoLogoWhatsapp className="text-success" />
                      </button>
                    </li>
                    <li>
                      <button
                        className="dropdown-item d-flex justify-content-between"
                        onClick={handleShareFacebook}
                        type="button"
                      >
                        <span>Facebook </span>{" "}
                        <FaFacebook className="text-primary" />
                      </button>
                    </li>
                    <li>
                      <button
                        className="dropdown-item d-flex justify-content-between"
                        onClick={handleShareTwitter}
                        type="button"
                      >
                        <span>Twitter </span> <FaXTwitter />
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="my-2">
                <span className="event-category me-4" style={{ width: "5rem" }}>
                  {event.category}
                </span>
                <span className="event-detail">
                  {event.Addon?.[0]?.Event_lang
                    ? event.Addon[0].Event_lang
                    : "Not Mention"}{" "}
                  |{" "}
                  {event.Addon?.[0]?.Age_restriction
                    ? event.Addon[0].Age_restriction + "yrs +"
                    : "0"}
                </span>
              </div>
            </div>
            <div>
              <div className="d-flex flex-column justify-content-start align-items-start">
                <div className="venueDiv">
                  <TbMapPin className=" fs-6 me-1" />
                  <span className="fs-6 fw-medium">
                    {event.multiCityArray?.length > 1
                      ? event.multiCityArray

                          .map((city) => city.event_city)
                          .slice(0, 2)
                          .join(", ")
                      : event.event_city}
                  </span>
                  {event.multiCityArray?.length > 1 ? null : (
                    <Link
                      className="directionspan ml-0"
                      to="#"
                      onClick={openGoogleMaps}
                      style={{
                        marginLeft: 4,
                      }}
                    >
                      <span
                        style={{
                          fontSize: "12px",
                          color: "#2C9CF0",

                          marginTop: "5px",
                          marginLeft: 10,
                        }}
                      >
                        Get Direction
                      </span>
                    </Link>
                  )}
                </div>
                {event.multiCityArray?.length > 1 && (
                  <span
                    onClick={() => setShowEventVenueModal(true)}
                    style={{
                      fontSize: "12px",
                      marginLeft: "5px",
                      cursor: "pointer",
                      color: "#2C9CF0",
                    }}
                  >
                    {`View more venues`}
                  </span>
                )}
              </div>
              <div className="event-schedule mb-1 fs-6">
                <span className="event-timing fw-semibold fs-6">
                  <Calendar size={16} /> {formatDate(event.event_starts_date)}{" "}
                  {event.event_ends_date &&
                  event.event_starts_date !== event.event_ends_date
                    ? ` - ${formatDate(event.event_ends_date)}`
                    : ""}
                </span>
              </div>
              <div className="event-schedule mb-1">
                <span className="event-timing fw-semibold fs-6">
                  <Clock size={16} /> {formatTime(event?.event_starts_Time)} -{" "}
                  {formatTime(event?.event_ends_Time)}
                </span>
              </div>
              <div className="price-details-content">
                <p>
                  <span className="ticket-amt">
                    ₹{" "}
                    {event.ticket.length > 0 ? lowestTicket(event.ticket) : "0"}{" "}
                  </span>
                  <span
                    className="fw-normal text-secondary fs-6"
                    style={{ color: "#6A6A6A", fontSize: "24px" }}
                  >
                    onwards
                  </span>
                </p>
              </div>
              {new Date(new Date(event.event_ends_date).setHours(0, 0, 0, 0)) <
              new Date(new Date().setHours(0, 0, 0, 0)) ? (
                <button
                  className="booking-btn fs-5 rounded-3 mt-2 px-2"
                  disabled
                  style={{ background: "#6A6A6A" }}
                >
                  Sold Out
                </button>
              ) : (
                <button
                  className="booking-btn fs-5 rounded-3 mt-2"
                  onClick={handleTicketSelection}
                  disabled={loading}
                >
                  {loading ? (
                    <SyncLoader
                      animation="border"
                      color="#FFFF"
                      size="7"
                      speedMultiplier={1}
                      margin={4}
                    />
                  ) : (
                    "Book Now"
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="aboutArtistDiv p-3 gap-3 align-items-stretch">
          <div className="w-100 rounded-3">
            <div className="aboutArtist  d-flex flex-column">
              <span className="fw-semibold profileHead mb-1 fs-5">About: </span>
              <span
                className={`profilesub ${
                  isExpanded ? "profilesub-seemore" : ""
                }`}
                style={{ whiteSpace: "pre-wrap" }}
              >
                {event.event_description}
              </span>
              {event.event_description.length > 200 && (
                <button
                  className="fw-normal readmore  bg-transparent text-primary text-decoration-underline "
                  type="button"
                  style={{
                    padding: "0",
                    margin: "0",
                    width: "min-content",
                    textWrap: "nowrap",
                  }}
                  onClick={toggleReadMore}
                >
                  {isExpanded ? "Read less" : "Read more"}
                </button>
              )}

              <div className="d-flex flex-column mt-3">
                <span className="fw-semibold profile-Head mb-1">
                  Presented by:{" "}
                </span>
                <span className="profilesub">
                  {event.Addon?.[0]?.Event_genre}
                </span>
              </div>
            </div>
          </div>
          <div className="w-100 rounded-3">
            <div className="aboutArtist">
              <span className="fw-semibold profileHead fs-5">Artist: </span>
              <div className="artistSocialdetail">
                {event.Addon[0].Artists.map((artist) => (
                  <div key={artist._id} className="artistItem">
                    <img
                      className="user-image"
                      src={artist.artist_pic}
                      alt="User"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = fallAvatarImage;
                      }}
                    />
                    <p className="artist-name">{artist.artist_name}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="lastDiv px-3 mb-4">
          {!event.Addon?.[0]?.Venuepicture ? (
            ""
          ) : (
            <div
              className="extraDetail fw-semibold mt-1 px-1 shadow border"
              data-bs-toggle="collapse"
              data-bs-target="#venueLoyout"
              aria-expanded="false"
              aria-controls="venueLoyout"
            >
              <span className="product-collapse-text">Venue Layout</span>
              <span className="me-3">
                <SlArrowDown className="arrowdown" size={16} />
              </span>
            </div>
          )}
          <div className="collapse my-3" id="venueLoyout">
            {!event.Addon?.[0]?.Venuepicture ? (
              <div
                className="p-5 w-75 m-auto border border-dark m-5 text-center"
                style={{ minHeight: "250px", backgroundColor: "peach" }}
              >
                <span className="fs-1 fw-bold">Venue Layout Image</span>
              </div>
            ) : (
              <img
                className="layoutImg w-100 h-100"
                src={`${event.Addon[0]?.Venuepicture}`}
                alt="Venue Layout"
              />
            )}
          </div>
          <div
            className="extraDetail fw-semibold mt-1 px-1"
            data-bs-toggle="collapse"
            data-bs-target="#termscondition"
            aria-expanded="false"
            aria-controls="termscondition"
          >
            <span className="product-collapse-text">Terms & Conditions</span>
            <span className="me-3">
              <SlArrowDown className="arrowdown" size={16} />
            </span>
          </div>
          {/* Collapse Div for termscondition Layout */}
          <div className="collapse my-3" id="termscondition">
            <div>
              {/* Render an ordered list from the event.termsCondition */}
              <ol className="fs-6">
                {event.termsCondition
                  .split(/[\n.]+/) // Split by newlines and periods
                  .filter((condition) => condition.trim() !== "") // Filter out any empty strings
                  .map((condition, index) => (
                    <li key={index}>{condition.trim()}</li> // Render non-empty conditions
                  ))}
              </ol>
            </div>
          </div>
          <div
            className="extraDetail fw-semibold mt-1  px-1"
            data-bs-toggle="collapse"
            data-bs-target="#guideline"
            aria-expanded="false"
            aria-controls="guideline"
          >
            <span className="product-collapse-text">Guidelines</span>
            <span className="me-3">
              <SlArrowDown className="arrowdown" size={16} />
            </span>
          </div>
          {/* Collapse Div for guideline Layout */}
          <div className="collapse my-3" id="guideline">
            <div>
              {/* Render an ordered list from the event.termsCondition */}
              <ol className="fs-6">
                {event.guidelines
                  .split(/[\n.]+/)
                  .filter((condition) => condition.trim() !== "")
                  .map((condition, index) => (
                    <li key={index}>{condition.trim()}</li>
                  ))}
              </ol>
            </div>
          </div>
        </div>
        <div className="event-price p-3 py-2">
          <p className="m-0">
            <span className="event-ticket">
              ₹ {event.ticket.length > 0 ? lowestTicket(event.ticket) : "0"}{" "}
            </span>
            <span
              className="fs-6"
              style={{
                color: "#6A6A6A",
              }}
            >
              onwards
            </span>
          </p>
          {new Date(new Date(event.event_ends_date).setHours(0, 0, 0, 0)) <
          new Date(new Date().setHours(0, 0, 0, 0)) ? (
            <button className="btn-booking bg-secondary text-white" disabled>
              Sold Out
            </button>
          ) : (
            <button
              className="btn-booking text-white"
              onClick={handleTicketSelection}
              disabled={loading}
            >
              {loading ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Book Now"
              )}
            </button>
          )}
        </div>
      </div>
    </>
  );
}

export default ProductDetail;
