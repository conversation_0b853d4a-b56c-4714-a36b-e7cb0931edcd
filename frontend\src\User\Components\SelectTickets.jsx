import axios from "axios";
import "./SelectTickets.css";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { API_URL } from "../../config";
import { BsArrowLeft } from "react-icons/bs";
import { CiCircleMinus, CiCirclePlus } from "react-icons/ci";
import PuffLoader from "react-spinners/PuffLoader";
import SyncLoader from "react-spinners/SyncLoader";
import NavBar from "./NavBar";

function SelectTickets({ id }) {
  const user = JSON.parse(localStorage.getItem("userData"));
  const [ticketList, setTicketList] = useState([]);
  const [eventData, SetEventData] = useState([]);
  const [loading, setLoading] = useState(true); // Loading state added
  const [bookingQuantities, setBookingQuantities] = useState({});
  const [ticketbookedbyuser, setTicketbookedbyuser] = useState({});
  const [selectedTicket, setSelectedTicket] = useState(null);
  const Navigate = useNavigate();
  const location = useLocation();
  const [isProceeding, setIsProceeding] = useState(false);
  const [date_for_booked_event, setDateForBookedEvent] = useState("");
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [eventDataFromLocation, setEventDataFromLocation] = useState([]);

  useEffect(() => {
    console.log("location.state", location.state);
    if (location.state && location.state.event) {
      setEventDataFromLocation(location.state.event);
    }
  }, [location.state]);

  useEffect(() => {
    if (location.state && location.state.date_for_booked_event) {
      console.log(
        "date_for_booked_event",
        location.state.date_for_booked_event
      );
      setDateForBookedEvent(location.state.date_for_booked_event);
    } else {
      setDateForBookedEvent(eventData.event_starts_date);
    }
  }, [location.state, eventData.event_starts_date]);

  useEffect(() => {
    (async () => {
      try {
        setLoading(true); // Start loading
        const { data } = await axios.get(`${API_URL}/api/eventdetail/${id}`);
        if (
          data &&
          Array.isArray(data[0]?.ticket) &&
          data[0] &&
          data[0].ticket
        ) {
          setTicketList(data[0].ticket);
          console.log("data[0].ticket", data[0].ticket);
          console.log(
            "date_for_booked_event",
            new Date().setHours(0, 0, 0, 0) >
              new Date(
                new Date(data[0].ticket[0].sale_end).setHours(0, 0, 0, 0)
              )
          );

          SetEventData(data[0]);
          console.log("data[0]", data[0]);

          findUserExitMaxTickets(data[0].ticket, data[0]);
        }

        console.log("TicketList", ticketList);
      } catch (error) {
        console.error("Error fetching ticket data:", error);
        setLoading(false); // End loading
      } finally {
        setLoading(false); // End loading
      }
    })();
  }, [id]);

  const findUserExitMaxTickets = async (tickets, events) => {
    try {
      for (let i = 0; i < tickets.length; i++) {
        const ticketId = tickets[i]._id;

        // Fetch the booked ticket quantities for the specific ticket
        const response = await axios.get(
          `${API_URL}/ticket/findusermaxticket/${
            user._id || user.uid
          }/${ticketId}/${events._id}`
        );

        // Sum up the bookedTicketQuantities from the response data
        const totalBookedQuantity = response.data.data.reduce((sum, entry) => {
          return sum + (entry.bookedTicketQuantities || 0);
        }, 0);

        // Update the state with the total quantity for this ticket
        setTicketbookedbyuser((previous) => ({
          ...previous,
          [ticketId]: totalBookedQuantity,
        }));
      }
    } catch (error) {
      console.error("Error in findUserExitMaxTickets:", error);
    }
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day} ${month} '${year}`;
  };

  const formatTime = (timeString) => {
    if (timeString == null || timeString === undefined || timeString === "") {
      timeString = "00:00:00";
    }
    const dateObj = new Date(`1970-01-01T${timeString}Z`);
    let hours = dateObj.getUTCHours();
    const minutes = String(dateObj.getUTCMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;

    const formattedTime = `${hours}:${minutes} ${ampm}`;
    return formattedTime;
  };

  const incrementQuantity = (ticketId) => {
    setBookingQuantities((prevQuantities) => {
      // Reset all quantities when a new ticket is selected
      if (selectedTicket !== ticketId) {
        Object.keys(prevQuantities).forEach((key) => {
          prevQuantities[key] = 0;
        });
      }
      const currentQuantity = prevQuantities[ticketId] || 0;
      const ticket = ticketList.find((ticket) => ticket._id === ticketId);

      const maxBooking = ticket.max_booking; // Maximum tickets the user can book
      const userBookedQuantity = ticketbookedbyuser[ticketId] || 0; // Already booked tickets by the user
      const availableQuantity = ticket.isTicketForPerDay
        ? ticket.available_daily_ticket_quantities[date_for_booked_event] || 0
        : ticket.ticket_avability;

      // Only proceed with booking limits if max_booking is not zero
      if (maxBooking !== 0) {
        // Calculate how many more tickets the user can book
        const remainingTickets = maxBooking - userBookedQuantity;

        // Ensure that the user can book more tickets, and they don't exceed the remaining available or their max allowed
        if (currentQuantity < Math.min(availableQuantity, remainingTickets)) {
          return { [ticketId]: currentQuantity + 1 };
        } else {
          alert(`You can only book ${maxBooking} tickets for this event.`);
        }
      } else {
        // If max_booking is 0, allow the user to book up to available tickets, as there's no limit
        if (currentQuantity < availableQuantity) {
          return { [ticketId]: currentQuantity + 1 };
        }
      }

      return prevQuantities;
    });
    setSelectedTicket(ticketId);
  };

  const decrementQuantity = (ticketId) => {
    setBookingQuantities((prevQuantities) => {
      const newQuantity = Math.max((prevQuantities[ticketId] || 0) - 1, 0);
      if (newQuantity === 0) {
        setSelectedTicket(null);
        return {};
      }
      return {
        [ticketId]: newQuantity,
      };
    });
  };

  const handleTicketBooking = async (e) => {
    e.preventDefault();
    setIsProceeding(true); // Start button loader
    console.log("YOUR SELECTED TICKETS ARE :", bookingQuantities);
    try {
      const response = await axios.post(`${API_URL}/ticket/booktickets`, {
        bookingQuantities,
        date_for_booked_event,
      });
      if (response.status === 200) {
        Navigate(
          `/eventbooking/${eventData.event_name.replace(/\s+/g, "_")}/${
            eventData.unique_id
          }`,
          {
            state: {
              bookingQuantities,
              selectedCity: location.state.selectedCity
                ? location.state.selectedCity
                : eventData.event_city,
            },
          }
        );
      }
    } catch (error) {
      console.error("Error booking tickets:", error);
    } finally {
      setIsProceeding(false); // Stop button loader
    }
  };

  const getTotalQuantity = () => {
    return Object.values(bookingQuantities).reduce(
      (total, quantity) => total + quantity,
      0
    );
  };

  const getTotalPrice = () => {
    return ticketList.reduce((total, ticket) => {
      const quantity = bookingQuantities[ticket._id] || 0;
      return total + quantity * ticket.ticket_price;
    }, 0);
  };

  const isButtonDisabled = getTotalQuantity() === 0;

  return (
    <>
      <div className="select-ticket">
        <div id="hideforSmallDevices">
          <NavBar
            userLocation={userLocation}
            onNext={() => setShowLocationModal(true)}
            disableMargin={true}
          />
        </div>
        <div className="ticketlist">
          <div
            className="d-flex flex-row justify-content-between align-items-center m-0"
            id="largedeviceview"
          >
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center">
              <span
                className="backarrowlink h-100 link-dark "
                onClick={() => Navigate(-1)}
              >
                <BsArrowLeft size={30} />
              </span>
            </div>
            <div className="col-10 col-10">
              <div className="d-flex flex-column justify-content-center">
                <span className="event-name">
                  {eventDataFromLocation?.event_name}
                </span>
                <span className="event-detail">
                  {location.state.selectedCity
                    ? location.state.selectedCity
                    : eventData.event_city}{" "}
                  | &nbsp;{" "}
                  {formatDate(date_for_booked_event)
                    ? formatDate(date_for_booked_event)
                    : formatDate(eventData.event_starts_date)}
                </span>
              </div>
            </div>
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center"></div>
          </div>
          <div id="smalldeviceview">
            <div className="d-flex flex-row align-items-center shadow-sm py-1">
              <div className="col-2 d-flex justify-content-center align-items-center">
                <span className=" link-dark" onClick={() => Navigate(-1)}>
                  <BsArrowLeft size={24} />
                </span>
              </div>
              <div className="col-8 d-flex flex-column justify-content-center align-items-center">
                <span className="event-name fw-semibold">
                  {eventDataFromLocation?.event_name}
                </span>
                
                <span className="event-detail">
                  {formatDate(date_for_booked_event)
                    ? formatDate(date_for_booked_event)
                    : formatDate(eventData.event_starts_date)}{" "}
                  &nbsp;
                  {location.state.selectedCity
                    ? location.state.selectedCity
                    : eventData.event_city}
                </span>
              </div>
              <div className="col-2 d-flex justify-content-center align-items-center"></div>
            </div>
          </div>
          {/* Loader */}
          {loading ? (
            <div
              className="d-flex justify-content-center align-items-center"
              style={{ height: "230px" }}
            >
              <PuffLoader size={28} loading={loading} />
            </div>
          ) : (
            <>
              <div className="head" style={{ fontSize: 20 }}>
                Select Your Front Seat
              </div>
              {!eventData.eventForMultiCity ? (
                <div className="flex flex-column justify-content-start align-content-stretch w-100 p-3 p-md-0 mt-2">
                  {ticketList && ticketList.length > 0 ? (
                    <>
                      {(() => {
                        const currentDate = new Date();
                        const date_for_booked_event_ISO = new Date(
                          date_for_booked_event
                        ).toISOString();
                        const filteredTickets = ticketList.filter((ticket) => {
                          if (!ticket.sale_start && !ticket.sale_end) {
                            return true;
                          }
                          const saleStartDate = new Date(
                            ticket.sale_start
                          ).setHours(0, 0, 0, 0);
                          const saleEndDate = new Date(
                            ticket.sale_end
                          ).setHours(0, 0, 0, 0);
                          return currentDate >= saleStartDate;
                        });
                        return filteredTickets.length > 0 ? (
                          !eventData.isAllSession ? (
                            filteredTickets
                              .filter(
                                (ticket_for) =>
                                  ticket_for.ticket_for_Date &&
                                  new Date(
                                    ticket_for.ticket_for_Date
                                  ).toISOString() === date_for_booked_event_ISO
                              )
                              .map((ticket) => (
                                <div
                                  className="row listoftickets  rounded shadow-sm border"
                                  key={ticket._id}
                                >
                                  <div className="col-lg-10 col-md-10 col-sm-9 col-9 ticketDetails">
                                    <span className="ticketname">
                                      {ticket.ticket_Name}
                                    </span>
                                    <span className="ticketdescription">
                                      {ticket.ticket_description}
                                    </span>
                                  </div>
                                  <div className="col-lg-2 col-md-2 col-sm-3 col-3  d-flex flex-column justify-content-end">
                                    <div className="d-flex flex-column justify-content-center">
                                      <span
                                        className="ticketname ms-3"
                                        style={{ whiteSpace: "nowrap" }}
                                      >
                                        {" "}
                                        ₹ {ticket.ticket_price}
                                      </span>
                                      <span className="d-flex pt-1 m-auto">
                                        {selectedTicket !== ticket._id ? (
                                          <>
                                            {(ticket.isTicketForPerDay &&
                                              ticket
                                                .available_daily_ticket_quantities[
                                                date_for_booked_event
                                              ] === 0) ||
                                            ticket.showTicketSoldOut ||
                                            new Date().setHours(0, 0, 0, 0) >
                                              new Date(
                                                new Date(
                                                  ticket.sale_end
                                                ).setHours(0, 0, 0, 0)
                                              ) ? (
                                              <button
                                                className="add-btn bg-secondary"
                                                disabled
                                              >
                                                Sold out
                                              </button>
                                            ) : (bookingQuantities[
                                                ticket._id
                                              ] || 0) <
                                              (ticket.isTicketForPerDay
                                                ? ticket
                                                    .available_daily_ticket_quantities[
                                                    date_for_booked_event
                                                  ]
                                                : ticket.ticket_avability) ? (
                                              <button
                                                className="add-btn"
                                                onClick={() =>
                                                  incrementQuantity(ticket._id)
                                                }
                                                disabled={false}
                                              >
                                                Add
                                              </button>
                                            ) : (
                                              <button
                                                className="add-btn"
                                                disabled
                                              >
                                                Sold out
                                              </button>
                                            )}
                                          </>
                                        ) : (
                                          <>
                                            <CiCircleMinus
                                              className="couting"
                                              onClick={() =>
                                                decrementQuantity(ticket._id)
                                              }
                                            />{" "}
                                            <span className="ticketcount mx-2">
                                              {" "}
                                              {bookingQuantities[ticket._id] ||
                                                0}{" "}
                                            </span>
                                            <CiCirclePlus
                                              className={`couting text-primary ${
                                                (bookingQuantities[
                                                  ticket._id
                                                ] || 0) >=
                                                (ticket.isTicketForPerDay
                                                  ? ticket
                                                      .available_daily_ticket_quantities[
                                                      date_for_booked_event
                                                    ]
                                                  : ticket.ticket_avability)
                                                  ? "disabled"
                                                  : ""
                                              }`}
                                              onClick={() =>
                                                incrementQuantity(ticket._id)
                                              }
                                            />{" "}
                                          </>
                                        )}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              ))
                          ) : (
                            filteredTickets.map((ticket) => (
                              <div
                                className="row listoftickets rounded"
                                key={ticket._id}
                              >
                                <div className="col-lg-10 col-md-10 col-sm-9 col-9 ticketDetails">
                                  <span className="ticketname">
                                    {ticket.ticket_Name}
                                  </span>
                                  <span className="ticketdescription">
                                    {ticket.ticket_description}
                                  </span>
                                </div>
                                <div className="col-lg-2 col-md-2 col-sm-3 col-3 d-flex flex-column justify-content-end">
                                  <div className="d-flex flex-column justify-content-center">
                                    <span
                                      className="ticketname ms-3"
                                      style={{ whiteSpace: "nowrap" }}
                                    >
                                      {" "}
                                      ₹ {ticket.ticket_price}
                                    </span>
                                    <span className="d-flex pt-1 m-auto">
                                      {selectedTicket !== ticket._id ? (
                                        <>
                                          {(ticket.isTicketForPerDay &&
                                            ticket
                                              .available_daily_ticket_quantities[
                                              date_for_booked_event
                                            ] === 0) ||
                                          new Date().setHours(0, 0, 0, 0) >
                                            new Date(
                                              new Date(
                                                ticket.sale_end
                                              ).setHours(0, 0, 0, 0)
                                            ) ? (
                                            <button
                                              className="add-btn"
                                              disabled
                                            >
                                              Sold out
                                            </button>
                                          ) : (bookingQuantities[ticket._id] ||
                                              0) <
                                            (ticket.isTicketForPerDay
                                              ? ticket
                                                  .available_daily_ticket_quantities[
                                                  date_for_booked_event
                                                ]
                                              : ticket.ticket_avability) ? (
                                            <button
                                              className="add-btn"
                                              onClick={() =>
                                                incrementQuantity(ticket._id)
                                              }
                                              disabled={false}
                                            >
                                              Add
                                            </button>
                                          ) : (
                                            <button
                                              className="add-btn bg-secondary"
                                              disabled
                                            >
                                              Sold out
                                            </button>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          <CiCircleMinus
                                            className="couting"
                                            onClick={() =>
                                              decrementQuantity(ticket._id)
                                            }
                                          />{" "}
                                          <span className="ticketcount mx-2">
                                            {" "}
                                            {bookingQuantities[ticket._id] ||
                                              0}{" "}
                                          </span>
                                          <CiCirclePlus
                                            className={`couting text-primary ${
                                              (bookingQuantities[ticket._id] ||
                                                0) >=
                                              (ticket.isTicketForPerDay
                                                ? ticket
                                                    .available_daily_ticket_quantities[
                                                    date_for_booked_event
                                                  ]
                                                : ticket.ticket_avability)
                                                ? "disabled"
                                                : ""
                                            }`}
                                            onClick={() =>
                                              incrementQuantity(ticket._id)
                                            }
                                          />{" "}
                                        </>
                                      )}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))
                          )
                        ) : (
                          <p>Coming Soon</p>
                        );
                      })()}
                    </>
                  ) : (
                    ""
                  )}
                </div>
              ) : (
                <div className="flex flex-column justify-content-start align-content-stretch w-100 p-3 p-md-0 mt-2">
                  {ticketList && ticketList.length > 0 ? (
                    <>
                      {(() => {
                        const currentDate = new Date();
                        const date_for_booked_event_ISO = new Date(
                          date_for_booked_event
                        ).toISOString();
                        const filteredTickets = ticketList.filter((ticket) => {
                          if (
                            ticket.ticket_for_city ===
                              location.state.selectedCity &&
                            ticket.ticket_for_city_venue ===
                              location.state.selectedVenue
                          ) {
                            if (!ticket.sale_start && !ticket.sale_end) {
                              return true;
                            }
                            const saleStartDate = new Date(
                              ticket.sale_start
                            ).setHours(0, 0, 0, 0);
                            const saleEndDate = new Date(
                              ticket.sale_end
                            ).setHours(0, 0, 0, 0);
                            return (
                              currentDate >= saleStartDate &&
                              currentDate <= saleEndDate
                            );
                          }
                        });
                        return filteredTickets.length > 0 ? (
                          !eventData.isAllSession ? (
                            filteredTickets
                              .filter(
                                (ticket_for) =>
                                  ticket_for.ticket_for_Date &&
                                  new Date(
                                    ticket_for.ticket_for_Date
                                  ).toISOString() === date_for_booked_event_ISO
                              )
                              .map((ticket) => (
                                <div
                                  className="listoftickets rounded shadow-sm"
                                  key={ticket._id}
                                >
                                  <div className="ticketDetails">
                                    <span className="ticketname">
                                      {ticket.ticket_Name}
                                    </span>
                                    <span className="ticketdescription">
                                      {ticket.ticket_description}
                                    </span>
                                  </div>
                                  <div className="text-center d-flex flex-column align-items-end justify-content-end">
                                    <span className="ticketname ms-3 me-1">
                                      {" "}
                                      ₹ {ticket.ticket_price}
                                    </span>
                                    <span className="d-flex pt-1">
                                      {selectedTicket !== ticket._id ? (
                                        <>
                                          {ticket.isTicketForPerDay &&
                                          ticket
                                            .available_daily_ticket_quantities[
                                            date_for_booked_event
                                          ] === 0 ? (
                                            <button className="add-btn">
                                              Sold out
                                            </button>
                                          ) : (bookingQuantities[ticket._id] ||
                                              0) <
                                            (ticket.isTicketForPerDay
                                              ? ticket
                                                  .available_daily_ticket_quantities[
                                                  date_for_booked_event
                                                ]
                                              : ticket.ticket_avability) ? (
                                            <button
                                              className="add-btn"
                                              onClick={() =>
                                                incrementQuantity(ticket._id)
                                              }
                                              disabled={false}
                                            >
                                              Add
                                            </button>
                                          ) : (
                                            <button
                                              className="add-btn"
                                              disabled
                                            >
                                              Sold out
                                            </button>
                                          )}
                                        </>
                                      ) : (
                                        <>
                                          <CiCircleMinus
                                            className="couting"
                                            onClick={() =>
                                              decrementQuantity(ticket._id)
                                            }
                                          />{" "}
                                          <span className="ticketcount mx-2">
                                            {" "}
                                            {bookingQuantities[ticket._id] ||
                                              0}{" "}
                                          </span>
                                          <CiCirclePlus
                                            className={`couting text-primary ${
                                              (bookingQuantities[ticket._id] ||
                                                0) >=
                                              (ticket.isTicketForPerDay
                                                ? ticket
                                                    .available_daily_ticket_quantities[
                                                    date_for_booked_event
                                                  ]
                                                : ticket.ticket_avability)
                                                ? "disabled"
                                                : ""
                                            }`}
                                            onClick={() =>
                                              incrementQuantity(ticket._id)
                                            }
                                          />{" "}
                                        </>
                                      )}
                                    </span>
                                  </div>
                                </div>
                              ))
                          ) : (
                            filteredTickets.map((ticket) => (
                              <div
                                className="listoftickets rounded"
                                key={ticket._id}
                              >
                                <div className="ticketDetails">
                                  <span className="ticketname">
                                    {ticket.ticket_Name}
                                  </span>
                                  <span className="ticketdescription">
                                    {ticket.ticket_description}
                                  </span>
                                </div>
                                <div className="text-center">
                                  <span className="ticketname ms-3">
                                    {" "}
                                    ₹ {ticket.ticket_price}
                                  </span>
                                  <span className="d-flex pt-1">
                                    {selectedTicket !== ticket._id ? (
                                      <>
                                        {(ticket.isTicketForPerDay &&
                                          ticket
                                            .available_daily_ticket_quantities[
                                            date_for_booked_event
                                          ] === 0) ||
                                        ticket.showTicketSoldOut ? (
                                          <button
                                            className="add-btn bg-secondary"
                                            disabled
                                          >
                                            Sold out
                                          </button>
                                        ) : (bookingQuantities[ticket._id] ||
                                            0) <
                                          (ticket.isTicketForPerDay
                                            ? ticket
                                                .available_daily_ticket_quantities[
                                                date_for_booked_event
                                              ]
                                            : ticket.ticket_avability) ? (
                                          <button
                                            className="add-btn"
                                            onClick={() =>
                                              incrementQuantity(ticket._id)
                                            }
                                            disabled={false}
                                          >
                                            Add
                                          </button>
                                        ) : (
                                          <button className="add-btn" disabled>
                                            Sold out
                                          </button>
                                        )}
                                      </>
                                    ) : (
                                      <>
                                        <CiCircleMinus
                                          className="couting"
                                          onClick={() =>
                                            decrementQuantity(ticket._id)
                                          }
                                        />{" "}
                                        <span className="ticketcount mx-2">
                                          {" "}
                                          {bookingQuantities[ticket._id] ||
                                            0}{" "}
                                        </span>
                                        <CiCirclePlus
                                          className={`couting text-primary ${
                                            (bookingQuantities[ticket._id] ||
                                              0) >=
                                            (ticket.isTicketForPerDay
                                              ? ticket
                                                  .available_daily_ticket_quantities[
                                                  date_for_booked_event
                                                ]
                                              : ticket.ticket_avability)
                                              ? "disabled"
                                              : ""
                                          }`}
                                          onClick={() =>
                                            incrementQuantity(ticket._id)
                                          }
                                        />{" "}
                                      </>
                                    )}
                                  </span>
                                </div>
                              </div>
                            ))
                          )
                        ) : (
                          <p>Coming Soon</p>
                        );
                      })()}
                    </>
                  ) : (
                    ""
                  )}
                </div>
              )}
            </>
          )}
        </div>
        <div style={{ height: "200px" }}></div>
        {/* Footer Part */}
        <div className="footerPart py-2" id="largedeviceview">
          <div className="footer-content justify-content-between">
            <div className="d-flex flex-column">
              <span className="ticketPrice fw-semibold mb-1">
                ₹{getTotalPrice()}
              </span>
              <span className="totalCount">
                {getTotalQuantity()} {`seat(s)`}
              </span>
            </div>
            <button
              className={`booking-btn fs-5 rounded-3 text-white ${
                isButtonDisabled || isProceeding
                  ? "btnisDisabled"
                  : "btnisEabled"
              }`}
              disabled={isButtonDisabled || isProceeding}
              onClick={handleTicketBooking}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>

        <div className="smallfooterPart py-0" id="smalldeviceview">
          <div className="footer-content px-3 justify-content-between">
            <div className="d-flex flex-column">
              <span className="ticketPrice fw-semibold mb-1">
                ₹{getTotalPrice()}
              </span>
              <span className="totalCount">
                {getTotalQuantity()} {`seat(s)`}
              </span>
            </div>
            <button
              className={`btn-booking text-white ${
                isButtonDisabled || isProceeding
                  ? "btnisDisabled"
                  : "btnisEabled"
              }`}
              disabled={isButtonDisabled || isProceeding}
              onClick={handleTicketBooking}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default SelectTickets;
