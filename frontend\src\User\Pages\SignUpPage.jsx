import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./SignUpPage.css"; // Ensure you have this CSS file for custom styles
import apiService from "../../services/apiService";
import { BsXCircle } from "react-icons/bs";
import { useLocation, useNavigate, Link } from "react-router-dom"; // Import Link
import { Button } from "react-bootstrap"; // Import Button from react-bootstrap
import SyncLoader from "react-spinners/SyncLoader"; // Import SyncLoader
import logo from "../../Assets/logo.png";
import loginPlaceholder from "../../Assets/login_placeholder.jpg";

function SignUpPage() {
  const navigate = useNavigate();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [gender, setGender] = useState("");
  const [dob, setDob] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [renderFlag, setRenderFlag] = useState("");
  const location = useLocation();
  const userData = apiService.getData();

  const handleDateChange = (date) => {
    setDob(date);
  };

  const onClose = () => {
    navigate("/login");
  };

  const saveUserandProceed = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const body = {
        _id: userData.uid,
        phone: userData.phoneNumber,
        firstName: firstName,
        lastName: lastName,
        email: email,
        gender: gender,
        dob: dob,
      };
      const res = await apiService.post("/user/addUser", body);

      if (res.status === 200) {
        // Update local storage with the new user data
        apiService.setData(body);

        switch (renderFlag) {
          case "event":
            window.location.href = "https://hfs-live-31172.web.app";
            break;
          case "login":
            navigate("/");
            break;
          case "ticket":
            navigate("/paymentgateway");
            break;
          case "yourtickets": // Added for consistency with LoginPage
            window.location.href = "/yourtickets";
            break;
          default:
            navigate("/");
            break;
        }
      }
    } catch (err) {
      console.error(err);
      alert(`Failed to register: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (location.state && location.state.renderFlag) {
      setRenderFlag(location.state.renderFlag);
    }
  }, [location.state]);

  return (
    <>
      <div className="login-modal d-flex flex-row align-items-start justify-content-evenly">
        <div className="d-flex flex-column justify-content-start align-items-center w-100">
          {/* Logo container */}
          <div className="w-100 p-5 mb-md-5" style={{ maxWidth: 500 }}>
            <img src={logo} alt="Logo" className="w-100" />
          </div>

          <div
            className="d-flex flex-column justify-content-center align-items-center w-100"
            style={{ maxWidth: 500 }}
          >
            <h1 className="book mb-4 fw-bold fs-4">
              Book <span className="your-fs fs-4"> Your Front Seat</span>
            </h1>

            <div className="w-100 p-3">
              <div className="div-num">
                <p className="enter-number fs-6 fw-light mb-1">
                  Enter your details to signup
                </p>
              </div>
              <input
                className="form-control border-primary mb-3 input-border-primary"
                type="text"
                id="first-name"
                placeholder="First Name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                style={{ borderColor: "#2C9CF0" }}
              />
              <input
                className="form-control border-primary mb-3 input-border-primary"
                type="text"
                id="last-name"
                placeholder="Last Name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                style={{ borderColor: "#2C9CF0" }}
              />
              <input
                className="form-control border-primary mb-1 input-border-primary"
                type="email" // Changed to email type
                id="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                style={{ borderColor: "#2C9CF0" }}
              />
              <span className="mail-recieve fs-6 fw-light mb-4 text-secondary">
                Tickets will be sent to this email.
              </span>
              <div className="row mb-4">
                <div className="col-lg-6 col-md-6 col-12 d-flex justify-content-between align-items-center mb-3 mb-md-0">
                  <div className="gender">
                    <input
                      className="pers-selection input-border-primary"
                      type="radio"
                      id="female"
                      name="gender"
                      value="female"
                      checked={gender === "female"}
                      onChange={() => setGender("female")}
                    />
                    <label className="pers-gender ms-1" htmlFor="female">
                      Female
                    </label>
                  </div>
                  <div className="gender">
                    <input
                      className="pers-selection input-border-primary"
                      type="radio"
                      id="male"
                      name="gender"
                      value="male"
                      checked={gender === "male"}
                      onChange={() => setGender("male")}
                    />
                    <label className="pers-gender ms-1" htmlFor="male">
                      Male
                    </label>
                  </div>
                  <div className="gender">
                    <input
                      className="pers-selection input-border-primary"
                      type="radio"
                      id="other"
                      name="gender"
                      value="other"
                      checked={gender === "other"}
                      onChange={() => setGender("other")}
                    />
                    <label className="pers-gender ms-1" htmlFor="other">
                      Other
                    </label>
                  </div>
                </div>
                <div className="col-lg-6 col-md-6 col-12">
                  <DatePicker
                    className="form-control input-border-primary  w-100"
                    selected={dob}
                    id="dob"
                    onChange={handleDateChange}
                    placeholderText="DOB    DD/MM/YYYY"
                    dateFormat="dd/MM/yyyy"
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                    style={{ borderColor: "#858585FF" }}
                  />
                </div>
              </div>

              <Button
                onClick={saveUserandProceed}
                className="w-100"
                disabled={isLoading}
                style={{
                  backgroundColor: "#2C9CF0",
                  borderColor: "#2C9CF0",
                  margin: 0,
                }}
              >
                {isLoading ? (
                  <SyncLoader
                    animation="border"
                    color="#FFFF"
                    size="10"
                    speedMultiplier={1}
                    margin={4}
                  />
                ) : (
                  <span>
                    {renderFlag === "ticket"
                      ? "Proceed to Pay"
                      : "Register Yourself"}
                  </span>
                )}
              </Button>
            </div>
            <div className="text-center mt-3">
              <Link
                htmlFor="terms"
                className="text-terms-acc text-decoration-none"
                to="/terms-condition"
              >
                I accept the Terms of Use & Privacy Policy
              </Link>
            </div>
          </div>

          {/* Login link and Help link, similar to LoginPage */}
          <div className="mt-5">
            <p className="text-center mt-3 fw-medium mb-2 fs-6 text-decoration-none">
              Already have an account?{" "}
              <Link
                to="/login"
                className="text-decoration-none"
                style={{ color: "#2C9CF0" }}
              >
                Login
              </Link>
            </p>
            <p className="text-center fw-light">
              We are here to help{" "}
              <Link
                to="/contact"
                className="text-decoration-none"
                style={{ color: "#2C9CF0" }}
              >
                <EMAIL>
              </Link>
            </p>
          </div>
        </div>
        {/* Placeholder image on the right */}
        <div className="logo-container w-50 bg-black h-100 d-md-block d-none">
          <img
            src={loginPlaceholder}
            alt="Login Placeholder"
            className="object-fit-fill"
          />
        </div>
      </div>
    </>
  );
}

export default SignUpPage;
