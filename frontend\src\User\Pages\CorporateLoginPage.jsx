import axios from "axios";
import React, { useState } from "react";
import './CorporateLoginPage.css'
import { useNavigate } from "react-router-dom";
import SyncLoader from "react-spinners/SyncLoader";
import { API_URL } from "../../config";
import corpLogo from '../../Assets/corpLogo.png'

const CorporateLoginPage = () => {
    const [login_id, setLogin_id] = useState("");
    const [password, setPassword] = useState("");
    const [isResendDisabled, setIsResendDisabled] = useState(true);
    const navigate = useNavigate();

    const [loading, setLoading] = useState(false);

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);
        try {
            const body = {
                login_id, password
            };
            const response = await axios.post(`${API_URL}/corporate/poc-login`, body);
            if (response.status === 200) {
                localStorage.setItem("corporateCompanyData", JSON.stringify(response.data.employee));
                navigate(`/corporate/${response.data.employee.company_name}/dashbord`);
            }
        } catch (error) {
            alert("Invalid Credentials")
        } finally {
            setLoading(false);
        }
    }

    return (
        <>
            <div className="corplogin-modal">
                <div className="w-lg-50">
                    <div className="login-container shadow">
                        <div className="w-50 m-auto">
                            <img src={corpLogo} className="w-100" />
                        </div>
                        <>
                            <form>
                                <div className="copdiv-num mb-3">
                                    <p className="copenter-number">Login Id:</p>
                                    <input
                                        className="form-control mobileinput py-3 border-primary"
                                        type="text"
                                        name="login_id"
                                        placeholder="Enter the login ID provided by the admin"
                                        value={login_id}
                                        onChange={(e) => setLogin_id(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="copdiv-num mb-5">
                                    <p className="copenter-number">Password:</p>
                                    <input
                                        className="form-control mobileinput py-3 border-primary"
                                        type="text"
                                        name="password"
                                        placeholder="Enter the password provided by the admin"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                    />
                                </div>
                                <button onClick={handleLogin} className="btn getotpbtn btn-primary w-50" disabled={loading}>
                                    {loading ? <SyncLoader animation="border" color="#FFFF" size="10" speedMultiplier={1} margin={4} /> : "Login"}
                                </button>
                            </form>
                        </>
                    </div>
                    <div className="bottomNotification d-flex flex-column" >
                        <span className="fw-bold">
                            Don't have the credentials?
                        </span>
                        <span className="fw-semibold">
                            We are here to help <span className="text-primary"><EMAIL></span>
                        </span>
                    </div>
                </div>
            </div>
        </>
    );
}

export default CorporateLoginPage; 