const MongoDB = require('mongoose');

const VendorPaymentSchema = new MongoDB.Schema({
    vendorId:{
        type: String,
        required: true
    },
    eventId: {
        type: String,
    },
    Amt_Paid: {
        type: Number,
        default:0
    },
    isAmtPaid: {
        type: Boolean,
        default: false
    }

});

const VendorPaymentModel = MongoDB.model("VendorPaymentData", VendorPaymentSchema);
module.exports = VendorPaymentModel;