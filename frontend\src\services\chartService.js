import { API_URL } from "../config";

class ChartService {
  static baseUrl = API_URL;

  /**
   * Build query string from parameters
   * @param {Object} params - Query parameters
   * @returns {string} Query string
   */
  static buildQueryString(params) {
    const queryParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        queryParams.append(key, params[key]);
      }
    });
    
    return queryParams.toString();
  }

  /**
   * Generic method to fetch chart data
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Chart data response
   */
  static async fetchChartData(endpoint, params = {}) {
    try {
      const queryString = this.buildQueryString(params);
      const url = `${this.baseUrl}/api/charts/${endpoint}${queryString ? `?${queryString}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch ${endpoint} data: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error fetching ${endpoint} data:`, error);
      throw error;
    }
  }

  /**
   * Get sales data for charts
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (ISO string)
   * @param {string} options.endDate - End date (ISO string)
   * @param {string} options.period - Period: hourly, daily, weekly, monthly, yearly
   * @param {string} options.createdBy - Filter by event creator ID (vendor ID)
   * @returns {Promise<Object>} Sales data
   */
  static async getSalesData(options = {}) {
    return this.fetchChartData('sales', options);
  }

  /**
   * Get revenue data for charts
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (ISO string)
   * @param {string} options.endDate - End date (ISO string)
   * @param {string} options.period - Period: hourly, daily, weekly, monthly, yearly
   * @param {string} options.createdBy - Filter by event creator ID (vendor ID)
   * @returns {Promise<Object>} Revenue data
   */
  static async getRevenueData(options = {}) {
    return this.fetchChartData('revenue', options);
  }

  /**
   * Get profit data for charts
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (ISO string)
   * @param {string} options.endDate - End date (ISO string)
   * @param {string} options.period - Period: hourly, daily, weekly, monthly, yearly
   * @param {string} options.createdBy - Filter by event creator ID (vendor ID)
   * @returns {Promise<Object>} Profit data
   */
  static async getProfitData(options = {}) {
    return this.fetchChartData('profit', options);
  }

  /**
   * Get dashboard summary with key metrics
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (ISO string)
   * @param {string} options.endDate - End date (ISO string)
   * @param {string} options.createdBy - Filter by event creator ID (vendor ID)
   * @returns {Promise<Object>} Dashboard summary
   */
  static async getDashboardSummary(options = {}) {
    return this.fetchChartData('summary', options);
  }

  /**
   * Get all chart data at once
   * @param {Object} options - Query options
   * @returns {Promise<Object>} All chart data
   */
  static async getAllChartData(options = {}) {
    try {
      const [salesData, revenueData, profitData, summaryData] = await Promise.all([
        this.getSalesData(options),
        this.getRevenueData(options),
        this.getProfitData(options),
        this.getDashboardSummary(options)
      ]);

      return {
        sales: salesData,
        revenue: revenueData,
        profit: profitData,
        summary: summaryData
      };
    } catch (error) {
      console.error('Error fetching all chart data:', error);
      throw error;
    }
  }

  /**
   * Helper method to format date for API
   * @param {Date} date - Date object
   * @returns {string} ISO date string
   */
  static formatDateForAPI(date) {
    return date instanceof Date ? date.toISOString() : date;
  }

  /**
   * Helper method to get default date range (last 30 days)
   * @returns {Object} Date range object
   */
  static getDefaultDateRange() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    return {
      startDate: this.formatDateForAPI(startDate),
      endDate: this.formatDateForAPI(endDate)
    };
  }

  /**
   * Helper method to get predefined date ranges
   * @returns {Object} Predefined date ranges
   */
  static getDateRangePresets() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    return {
      today: {
        startDate: this.formatDateForAPI(today),
        endDate: this.formatDateForAPI(now),
        label: 'Today'
      },
      yesterday: {
        startDate: this.formatDateForAPI(new Date(today.getTime() - 24 * 60 * 60 * 1000)),
        endDate: this.formatDateForAPI(new Date(today.getTime() - 1)),
        label: 'Yesterday'
      },
      last7Days: {
        startDate: this.formatDateForAPI(new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)),
        endDate: this.formatDateForAPI(now),
        label: 'Last 7 Days'
      },
      last30Days: {
        startDate: this.formatDateForAPI(new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)),
        endDate: this.formatDateForAPI(now),
        label: 'Last 30 Days'
      },
      thisMonth: {
        startDate: this.formatDateForAPI(new Date(now.getFullYear(), now.getMonth(), 1)),
        endDate: this.formatDateForAPI(now),
        label: 'This Month'
      },
      lastMonth: {
        startDate: this.formatDateForAPI(new Date(now.getFullYear(), now.getMonth() - 1, 1)),
        endDate: this.formatDateForAPI(new Date(now.getFullYear(), now.getMonth(), 0)),
        label: 'Last Month'
      },
      thisYear: {
        startDate: this.formatDateForAPI(new Date(now.getFullYear(), 0, 1)),
        endDate: this.formatDateForAPI(now),
        label: 'This Year'
      }
    };
  }
}

export default ChartService;
