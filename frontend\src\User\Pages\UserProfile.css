.userInfo {
    margin-top: 12px;
}

.profilepage{
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.lists {
    
}

.Input {
    
    min-width: 240px;
    height: 34px;
    font-size: 18px;
    border: none !important;
    margin: 0rem;
}

.personalDetail {
    margin-bottom: 23px;
}

.fieldname {
    font-size: 20px;
    
    font-weight: 400;
    margin-right: 0.5rem;
}

/* Additional Styles */
.form-control {
    width: 100%;
    border-color: #2C9CF0 !important;
}

.react-datepicker-wrapper {
    width: 100%;
}

.updatebtn{
    padding:0.5rem 1rem;
    font-size: 20px;
    text-align: center;
    
    color: #fff;
    background: #2C9CF0;
    border-radius: 0.5rem;
    border: none;
}

@media screen and (max-width: 768px){
    .fieldname {
        font-size: 14px;
        
        font-weight: 400;
    }
    
    .Input {
        
        min-width: 180px;
        height: 24px;
        font-size: 12px;
        border: none !important;
        margin: 0rem !important;
    }
    .updatebtn{
        width: 100%;
    }
}

/* Base styles for skeleton elements */
.shimmer {
    background: #f6f7f8;
    background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    animation-duration: 1.2s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: shimmer;
    animation-timing-function: linear;
    border-radius: 4px; /* Slightly rounded corners for a softer look */
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.profilepage-skeleton {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Specific skeleton element styles */
.profileheading-skeleton {
    height: 24px; /* Height similar to your heading */
    width: 200px; /* Width for the heading text */
    margin-bottom: 10px;
}

.fieldname-skeleton {
    height: 16px; /* Height for field labels */
    width: 100px; /* Width for field labels */
    margin-bottom: 8px;
    display: block; /* Ensures it takes up space like a block element */
}

.input-skeleton {
    height: 38px; /* Height similar to Bootstrap's form-control */
    width: 100%;
    margin-bottom: 1rem;
}

.large-input-skeleton {
    width: 100%; /* Wider input for address */
}

.datepicker-skeleton {
    width: 100%; /* Match the width of the DatePicker */
}

.radio-option-skeleton {
    height: 20px; /* Height for radio buttons and labels */
    width: 80px; /* Width for each radio option */
    margin-right: 20px;
}

.border-bottom-primary-skeleton {
    height: 2px; /* Mimic the border thickness */
    background-color: #0d6efd; /* Bootstrap primary color */
    width: 100%;
    margin-bottom: 1rem;
}

  
.pers-gender {
    font-size: 15px;
    
    margin-left: 10px;
} 
.updatebtn-skeleton {
    height: 40px; /* Height similar to your update button */
    width: 120px; /* Width for the button */
    margin-top: 20px;
    border-radius: 0.25rem; /* Bootstrap button border-radius */
}