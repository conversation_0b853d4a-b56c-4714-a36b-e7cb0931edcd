import { Link, useNavigate, useParams } from "react-router-dom";
import { PuffLoader } from "react-spinners";
import CropNavbarComponent from "./CropNavbarComponent";
import { useEffect, useState } from "react";
import { AiOutlineDashboard } from "react-icons/ai";
import { HiMiniArrowTrendingUp } from "react-icons/hi2";
import { IoMdCopy } from "react-icons/io";
import { API_URL } from "../../config";
import axios from "axios";
import { GoShareAndroid } from "react-icons/go";
import { FiLink } from "react-icons/fi";
import { IoLogoWhatsapp } from "react-icons/io";
import { Dropdown } from 'react-bootstrap';

const CorporateEventDetail = () => {
    const { event_id } = useParams();
    const [isLoading, setIsLoading] = useState(false);
    const [corporateEventDetail, setCorporateEventDetail] = useState();
    const navigate = useNavigate();
    const [generatedLink, SetGeneratedLink] = useState();
    const [ticket_hosted, setTicket_hosted] = useState(0);
    const [ticket_sold, setTicket_sold] = useState(0);
    const [users, SetUsers] = useState([]);
    const [Revenue, setRevenue] = useState(0);
    const [visibleEvents, setVisibleEvents] = useState(5);
    const [totalGuestEntered, setTotalGuestEntered] = useState(0);
    const [CorporateCompanyDetailData, setCorporateCompanyDetailData] = useState();
    const [isViewModal, setIsViewModal] = useState({
        EventLinkModal: false,
        ScannerLinkModal: false,
        TicketsModal: false,
        LoginDetailModal: false
    });

    const downloadExcel = () => {
        // const tableData = users.map((user, index) => ({
        //     "Sr. No.": index + 1,
        //     "User Name": user.user.firstName || "N/A", // Ensure it's a string
        //     "Registered Number": user.user.phone || "N/A", // Ensure it's a string
        //     "Location": user.booking.eventDetails?.event_city || "N/A", // Use optional chaining
        //     "Event Date": user.booking.eventDetails?.event_starts_date || "N/A",
        //     "Date of Booking": user.booking.createdAt
        //         ? new Date(user.booking.createdAt).toLocaleDateString('en-GB').replace(/\//g, '-')
        //         : "N/A",
        //     "Tickets Bought": user.booking.bookedTicketQuantities || 0,
        //     "Payment Status": user.booking.status || "N/A",
        // }));

        // const worksheet = XLSX.utils.json_to_sheet(tableData);
        // const workbook = XLSX.utils.book_new();
        // XLSX.utils.book_append_sheet(workbook, worksheet, "Ticket Buyers");

        // // Download the Excel file
        // XLSX.writeFile(workbook, "Ticket_Buyers.xlsx");
    };
    const handleCopy = (copy_type) => {
        let cardContent = '';
        if (copy_type === "loginDetail") {
            // const loginId = generatedLink?.login_id;
            // const password = generatedLink?.password;

            //  cardContent = `
            //     Ticket Scanner Link: ${ticketScannerLink}
            //     Login id: ${loginId}
            //     Password: ${password}
            // `;

        }
        if (copy_type === "scannerLink") {
            const ticketScannerLink = `https://www.hostyourfrontseat.in/scanner/scanqr/event-${corporateEventDetail?.event_name.replace(/\s+/g, '_')}/${generatedLink?.event_id || ""}`;
            // const loginId = generatedLink?.login_id;
            // const password = generatedLink?.password;

            cardContent = `
            ${ticketScannerLink}
            `;

        }
        if (copy_type === "eventLink") {
            const eventLink = `https://www.myfrontseat.in/corporateevent/${(corporateEventDetail?.event_name).replace(/\s+/g, '_')}/${corporateEventDetail.corporate_event_id}`;

            cardContent = `
                ${eventLink}
            `;

        }

        navigator.clipboard.writeText(cardContent)
            .then(() => {
                alert("Link copied!");
            })
            .catch((err) => {
                console.error("Failed to copy text: ", err);
            });
    };

    // copy link functionality
    const shareLink = `
    link : https://www.hostyourfrontseat.in/scanner/scanqr/event-${corporateEventDetail?.event_name.replace(/\s+/g, '_')}/${generatedLink?.event_id || ""},
    login id : ${generatedLink?.login_id},
    password : ${generatedLink?.password}
    `
    const handleCopyLink = () => {
        
        navigator.clipboard.writeText(shareLink).then(() => {
            alert("Link copied to clipboard!");
        }).catch(err => {
            alert("Failed to copy the link!");
        });
    };

    const handleShareWhatsApp = () => {
        const whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(JSON.stringify(shareLink))}`;
        window.open(whatsappUrl, '_blank');
    };

    const handleSeeMore = () => {
        setVisibleEvents((prevVisible) => Math.min(prevVisible + 5, users.length));
    };

    const handleSeeLess = () => {
        setVisibleEvents(5);
    };


    const getCorporateEventDetails = async () => {
        try {
            // Fetch Event Details
            const responseData = await axios.get(`${API_URL}/corporate/get-corporate-event/${event_id}`);
            console.log(responseData)
            if (responseData.status === 200 && responseData.data) {
                setCorporateEventDetail(responseData.data); // Update state with event details

                const companyDetailResponse = await axios.get(`${API_URL}/corporate/company-detail/${responseData.data?.eventCreatedBy}`)
                if (companyDetailResponse.status === 200 && companyDetailResponse.data.company) {
                    setCorporateCompanyDetailData(companyDetailResponse.data.company); // Update state with event details
                }

                let total_ticket = 0;
                let ticket_sell = 0;

                // Calculate ticket details
                for (const ticket of responseData.data.tickets) {
                    total_ticket += ticket.total_Quantity;
                }

                // Update ticket states
                setTicket_hosted(total_ticket);

                if (responseData.data && responseData.data._id) {
                    console.log("getNoOFGuestEntered Condition Ture Api will called")
                    const guestEntered = await axios.get(`${API_URL}/admin/guest-entered/${responseData.data._id}`);
                    if (guestEntered.status === 200 && guestEntered.data.count) {
                        setTotalGuestEntered(guestEntered.data.count)
                    }
                }
            }

            // Fetch User Details
            const userResponse = await axios.get(`${API_URL}/corporate/events/${responseData.data._id}`);
            if (userResponse.status === 200 && userResponse.data.users) {
                SetUsers(userResponse.data.users);
            } else {
                console.warn("No user data found!");
            }

            // Fetch and Calculate Revenue
            const soldData = await axios.get(`${API_URL}/vendor/eventbooking/${responseData.data._id}`);
            if (soldData.status === 200) {
                // Check if soldData.data is an array
                if (Array.isArray(soldData.data)) {
                    let NoOfticket_sold = 0; // Initialize ticket_sold to 0
                    const total_revenue = soldData.data.reduce((acc, booking) => {
                        if (booking.isPaid) {
                            // Ensure amount is a number and fallback to 0 if not valid
                            const amount = booking.amount ? booking.amount / 100 : 0;
                            NoOfticket_sold += booking.bookedTicketQuantities; // Increment ticket_sold
                            return acc + amount;
                        }
                        return acc; // Ensure acc is returned if isPaid is false
                    }, 0);

                    setTicket_sold(NoOfticket_sold); // Update state for ticket_sold
                    setRevenue(total_revenue); // Update revenue
                } else {
                    console.warn("Expected soldData.data to be an array, but received:", soldData.data);
                }
            }

        } catch (error) {
            console.error("Failed to fetch event details:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const getScannerLink = async () => {
        const response = await axios.get(`${API_URL}/corporate/get-scanner-link/${event_id}`);
        if (response.status === 200) {
            const link = response.data.Links;
            if (link) {
                SetGeneratedLink(link);
            }
        }
    };

    const GetCompanyDetails = async () => {
        console.log("COrporate Company Details  ID : ", corporateEventDetail?.eventCreatedBy)
        try {
            const companyDetailResponse = await axios.get(`${API_URL}/corporate/company-detail/${corporateEventDetail?.eventCreatedBy}`)
            if (companyDetailResponse.status === 200 && companyDetailResponse.data.company) {
                setCorporateCompanyDetailData(companyDetailResponse.data.company); // Update state with event details
            }
            console.log("COrporate Company Details : ", corporateEventDetail)
        } catch (error) {
            console.log("COrporate Company Details Error: ", error)
        }
    }



    useEffect(() => {
        getCorporateEventDetails();
        getScannerLink();
        // GetCompanyDetails();
    }, [event_id]);
    return (
        <>
            <CropNavbarComponent />
            <div className="container-fluid">
                {isLoading ? (
                    <div
                        className="d-flex justify-content-center align-items-center"
                        style={{ height: "50vh" }}
                    >
                        <PuffLoader size={48} loading={isLoading} />
                    </div>
                ) : (
                    <div className="row mt-3">
                        <div className="event-detail-head d-flex align-items-center">
                            <div className="me-3">
                                <Link className='link-dark link-underline-opacity-0 fw-semibold' to={`/corporate/${CorporateCompanyDetailData?.company_name}/dashbord`}>
                                    <AiOutlineDashboard className='sidebar-icon' />
                                </Link>
                            </div>
                            <div className="row w-100 d-flex align-items-center justify-content-between">
                                <div className="col-lg-6">
                                    <span className="event-name-heading">
                                        {corporateEventDetail?.event_name}
                                    </span>
                                </div>
                                <div className="col-lg-6 d-flex  justify-content-end">
                                    <div>
                                        <span className="event-detail-heading">Venue:
                                            {" " + corporateEventDetail?.event_city}
                                        </span>
                                        <span className="event-detail-heading">Date :
                                            {" " + new Date(
                                                corporateEventDetail?.event_start_date
                                            )
                                                .toLocaleDateString(
                                                    "en-GB"
                                                )
                                                .replace(/\//g, "-")}
                                        </span>
                                        <span className="event-detail-heading">Organiser:
                                            {" " + corporateEventDetail?.company_name}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-3 eventMaxtixMainDiv">
                            <div className="corporatematrixDiv me-lg-3">
                                <div className="card h-100">
                                    <div className="card-body">
                                        <div className="d-flex justify-content-between">
                                            <div>
                                                <p className='dashboard-summary fw-semibold'>Total Guests Entered</p>
                                                <p className="count-style">{totalGuestEntered}</p>
                                            </div>
                                            <div></div>
                                        </div>
                                        <div>
                                            <span className='dashboard-summary fw-semibold'><span className="text-success"><HiMiniArrowTrendingUp /> 1.8% </span>
                                                <span>Up from Last year</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="corporatematrixDiv me-lg-3">
                                <div className="card h-100">
                                    <div className="card-body">
                                        <div className="d-flex justify-content-between">
                                            <div>
                                                <p className='dashboard-summary fw-semibold'>Total Registrations</p>
                                                <p className="count-style">{ticket_sold}</p>
                                            </div>
                                            <div></div>
                                        </div>
                                        <div>
                                            <span className='dashboard-summary fw-semibold'><span className="text-success"><HiMiniArrowTrendingUp /> 1.8% </span>
                                                <span>Up from Last year</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="corporatematrixDiv-last text-center me-lg-3">
                                <div className="card corporatematrixcard-last mb-2">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold'>Company Details</span>
                                        <span>
                                            <button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                                onClick={() => setIsViewModal(prevState => ({
                                                    ...prevState,
                                                    EventLinkModal: false,
                                                    ScannerLinkModal: false,
                                                    TicketsModal: false,
                                                    LoginDetailModal: false,
                                                    companydetail: true
                                                }))}>View</button>
                                        </span>
                                        <span className="corporatedashboard-summary">
                                            View Event Details
                                        </span>
                                    </div>
                                </div>
                                <div className="card corporatematrixcard-last">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold'>Ticket Details</span>
                                        <span ><button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                            onClick={() => setIsViewModal(prevState => ({
                                                ...prevState,
                                                EventLinkModal: false,
                                                ScannerLinkModal: false,
                                                TicketsModal: true,
                                                LoginDetailModal: false,
                                                companydetail: false
                                            }))}>View</button></span>
                                        <span className="corporatedashboard-summary">
                                            View Ticket Details
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="corporatematrixDiv-last text-center  me-lg-3">
                                <div className="card corporatematrixcard-last mb-2">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold'>Ticket Scanner</span>
                                        <span>
                                            <button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                                onClick={() => setIsViewModal(prevState => ({
                                                    ...prevState,
                                                    EventLinkModal: false,
                                                    ScannerLinkModal: true,
                                                    TicketsModal: false,
                                                    LoginDetailModal: false,
                                                    companydetail: false
                                                }))}>Generate Link</button>
                                        </span>
                                        <span className="corporatedashboard-summary">
                                            Generate Scanner Link
                                        </span>
                                    </div>
                                </div>
                                <div className="card corporatematrixcard-last">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold'>Event Link</span>
                                        <span ><button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                            onClick={() => setIsViewModal(prevState => ({
                                                ...prevState,
                                                EventLinkModal: true,
                                                ScannerLinkModal: false,
                                                TicketsModal: false,
                                                LoginDetailModal: false,
                                                companydetail: false
                                            }))}>View Link</button></span>
                                        <span className="corporatedashboard-summary">
                                            Shows the sharable link of the event
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="corporatematrixDiv-last smallViewSettopMargin text-center">
                                <div className="card corporatematrixcard-last  mb-2">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold' style={{ whiteSpace: "wrap" }}>Company POC Login Credentials</span>
                                        <span>
                                            <button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                                onClick={() => setIsViewModal(prevState => ({
                                                    ...prevState,
                                                    EventLinkModal: false,
                                                    ScannerLinkModal: false,
                                                    TicketsModal: false,
                                                    LoginDetailModal: true,
                                                    companydetail: false
                                                }))}>View</button>
                                        </span>
                                    </div>
                                </div>
                                {/* <div className="card corporatematrixcard-last">
                                    <div className="card-body d-flex flex-column">
                                        <span className='dashboard-summary fw-bold'>Event Link</span>
                                        <span ><button className="sub-mat-btn" onClick={() => navigate(``)}>View Link</button></span>
                                        <span className="corporatedashboard-summary">
                                            Shows the sharable link of the event
                                        </span>
                                    </div>
                                </div> */}
                            </div>
                        </div>

                        <div className="mt-lg-3">
                            <div className="card mt-lg-3">
                                <div className="card-body">
                                    <div className="d-flex justify-content-between">
                                        <span className="vender-heading">Registrations </span>
                                        <button className="download-btn" onClick={downloadExcel}>Download .xlsx</button>
                                    </div>
                                    <div className="table-responsive">
                                        <table className="table table-hover text-center mt-2">
                                            <thead className="tableHead">
                                                <tr className="table-light">
                                                    <th className="table-field" scope="col">Employee Name</th>
                                                    <th className="table-field" scope="col">Registered Number</th>
                                                    <th className="table-field" scope="col">Location</th>
                                                    <th className="table-field" scope="col">Event Date</th>
                                                    <th className="table-field" scope="col">Registration Date</th>
                                                    <th className="table-field" scope="col">Tickets Bought</th>
                                                    <th className="table-field" scope="col">Registration Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {users.length > 0 ? (
                                                    <>
                                                        {
                                                            users.sort((a, b) => new Date(b.booking.createdAt) - new Date(a.booking.createdAt)).slice(0, visibleEvents).map((event, index) => {
                                                                return (
                                                                    <tr className="tableDataRow" key={event.booking._id}>
                                                                        <td className="table-value">{event.user.First_name}</td>
                                                                        <td className="table-value">{event.user.Phone_number}</td>
                                                                        <td className="table-value">{event.booking.eventDetails.event_city}</td>
                                                                        <td className="table-value">{new Date(event.booking.eventDetails.event_start_date).toLocaleDateString('en-GB').replace(/\//g, '-')}</td>
                                                                        <td className="table-value">{new Date(event.booking.createdAt).toLocaleDateString('en-GB').replace(/\//g, '-')}</td>
                                                                        <td className="table-value">{event.booking.bookedTicketQuantities}</td>
                                                                        <td className="table-value"><span className={`${event.booking.status === 'paid' || event.booking.status === 'Booked' ? 'payment_status_paid' : 'payment_status'}`}>{event.booking.status ? event.booking.status : "Failed"}</span></td>
                                                                    </tr>
                                                                )
                                                            })
                                                        }
                                                        {visibleEvents < users.length ? (
                                                            <tr className="text-center">
                                                                <td colSpan={8}>
                                                                    <button className="btn-text btn btn-link" onClick={handleSeeMore}>
                                                                        See More
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        ) : visibleEvents > 5 ? (
                                                            <tr className="text-center">
                                                                <td colSpan={8}>
                                                                    <button className="btn-text btn btn-link" onClick={handleSeeLess}>
                                                                        See Less
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        ) : null}
                                                    </>
                                                ) :
                                                    <tr>
                                                        <td className="table-value text-start" colSpan={8}>
                                                            <p>.........No Event Added........</p>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>


            {/* <!-- Detail Modal --> */}
            <div className="modal fade" id="TicketDetailsModal" tabIndex="-1" aria-labelledby="TicketDetailsModalLabel" aria-hidden="true">
                <div className="modal-dialog modal-lg modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-body">
                            <div className="text-center">
                                <p className={`sub-mat-head ${isViewModal.TicketsModal ? 'd-block' : 'd-none'}`} >Ticket Details</p>
                                <p className={`sub-mat-head ${isViewModal.ScannerLinkModal ? 'd-block' : 'd-none'}`} >Ticket Scanner Link Generation</p>
                                <p className={`sub-mat-head ${isViewModal.EventLinkModal ? 'd-block' : 'd-none'}`} >Sharable event link</p>
                                <p className={`sub-mat-head ${isViewModal.LoginDetailModal ? 'd-block' : 'd-none'}`} >Company POC Login Details</p>
                                <p className={`sub-mat-head ${isViewModal.companydetail ? 'd-block' : 'd-none'}`} >Company Details</p>
                            </div>
                            <div className={`${isViewModal.TicketsModal ? 'd-block' : 'd-none'}`}>
                                <div className="table-responsive">
                                    <table className="table table-hover text-center">
                                        <thead className="tableHead">
                                            <tr className="table-light">
                                                <th scope="col">Ticket Type</th>
                                                <th className="hideit_768" scope="col">Sale Start - Sale End</th>
                                                <th className="hideit_768" scope="col">Tickets Hosted</th>
                                                <th className="hideit_768" scope="col">Tickets Sold</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {corporateEventDetail?.tickets && (corporateEventDetail.tickets).length > 0 ? (
                                                <>
                                                    {
                                                        corporateEventDetail.tickets.map((tic) => {
                                                            return (
                                                                <tr className="tableDataRow" key={tic._id}>
                                                                    <td>{tic.ticket_Name}</td>
                                                                    <td>{tic.sale_start}  -  {tic.sale_end}</td>
                                                                    <td>{tic.total_Quantity}</td>
                                                                    <td>
                                                                        {tic.total_Quantity - tic.ticket_avability}
                                                                    </td>
                                                                </tr>
                                                            )
                                                        })
                                                    }
                                                </>
                                            ) : <p>.........No Event Added........</p>}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div className={`${isViewModal.ScannerLinkModal ? 'd-block' : 'd-none'}`}>
                                <div className="mt-5 d-flex align-items-end w-100">
                                    <div className="card w-75 m-auto">
                                        <div className="card-body d-flex justify-content-between align-items-top">
                                            <div>
                                                <p style={{ whiteSpace: "normal", wordBreak: "break-word" }}><b>Ticket Scanner Link:</b>
                                                    &nbsp;https://www.hostyourfrontseat.in/scanner/scanqr/event-{corporateEventDetail?.event_name}/{generatedLink?.event_id || ""}
                                                </p>
                                                <p><b>Login id:</b>
                                                    {" " + generatedLink?.login_id}
                                                </p>
                                                <p><b>Password:</b>
                                                    {" " + generatedLink?.password}
                                                </p>
                                            </div>
                                            <div className="d-flex flex-column justify-content-between">
                                                <IoMdCopy size={30} onClick={() => handleCopy("scannerLink")} style={{ color: "#2C9CF0" }} />
                                                <Dropdown>
                                                    <Dropdown.Toggle as="div" className="d-flex align-items-center" aria-expanded="false">
                                                        <GoShareAndroid size={25} style={{ color: "#2C9CF0", cursor: 'pointer' }} />
                                                    </Dropdown.Toggle>
                                                    <Dropdown.Menu>
                                                        <Dropdown.Item as="button" onClick={handleCopyLink} className="d-flex justify-content-between">
                                                            <span>Copy link</span> <FiLink className="primarycolor" />
                                                        </Dropdown.Item>
                                                        <Dropdown.Item as="button" onClick={handleShareWhatsApp} className="d-flex justify-content-between">
                                                            <span>WhatsApp</span> <IoLogoWhatsapp className="text-success" />
                                                        </Dropdown.Item>
                                                    </Dropdown.Menu>
                                                </Dropdown>
                                                {/* <div className="dropdown">
                                                    <span data-bs-toggle="dropdown" aria-expanded="false"><GoShareAndroid size={25} style={{ color: "#2C9CF0" }} /></span>
                                                    <ul className="dropdown-menu">
                                                        <li>
                                                            <button className="dropdown-item d-flex justify-content-between" onClick={handleCopyLink} type="button">
                                                                <span>Copy link  </span> <FiLink className='primarycolor' />
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button className="dropdown-item d-flex justify-content-between" onClick={handleShareWhatsApp} type="button">
                                                                <span>WhatsApp  </span> <IoLogoWhatsapp className='text-success' />
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div> */}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={`${isViewModal.EventLinkModal ? 'd-block' : 'd-none'}`}>
                                <div className="mt-5 d-flex align-items-end w-100">
                                    <div className="card w-75 m-auto">
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between align-items-top">
                                                <div>
                                                    <p style={{ whiteSpace: "normal", wordBreak: "break-word" }}><b>“{corporateEventDetail?.event_name}” {" "} Event Link: </b>
                                                        https://www.myfrontseat.in/corporateevent/{corporateEventDetail?.event_name}/{corporateEventDetail?.corporate_event_id}
                                                    </p>
                                                </div>
                                                <div>
                                                    <IoMdCopy size={30} onClick={() => handleCopy("eventLink")} style={{ color: "#2C9CF0" }} />
                                                </div>
                                            </div>
                                            <div><span className="corporatedashboard-summary">Note:</span><span className="note_description">This is a private event shall be visible only to employees of “Company Name”.</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={`${isViewModal.LoginDetailModal ? 'd-block' : 'd-none'}`}>
                                <div className="mt-5 d-flex align-items-end w-100">
                                    <div className="card w-75 m-auto">
                                        <div className="card-body d-flex justify-content-between align-items-top">
                                            <div>
                                                <p><b>Login id:</b>
                                                    &nbsp; &nbsp; &nbsp; {CorporateCompanyDetailData?.poc_contact_number}
                                                </p>
                                                <p><b>Password:</b>
                                                    &nbsp; &nbsp; &nbsp; {CorporateCompanyDetailData?.passcode}
                                                </p>
                                            </div>
                                            <div>
                                                <IoMdCopy size={30} onClick={() => handleCopy("loginDetail")} style={{ color: "#2C9CF0" }} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={`${isViewModal.companydetail ? 'd-block' : 'd-none'}`}>
                                <div className="mt-5 d-flex align-items-end w-100">
                                    <div className="card w-75 m-auto">
                                        <div className="card-body d-flex justify-content-between align-items-top">
                                            <div>
                                                <p><b>Company Name:</b>
                                                    &nbsp; &nbsp; &nbsp; {CorporateCompanyDetailData?.company_name}
                                                </p>
                                                <p><b>POC Name:</b>
                                                    &nbsp; &nbsp; &nbsp; {CorporateCompanyDetailData?.poc_name}
                                                </p>
                                                <p><b>POC Contact:</b>
                                                    &nbsp; &nbsp; &nbsp; {CorporateCompanyDetailData?.poc_contact_number}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className=" w-50 m-auto my-2 text-center d-flex justify-content-center">
                            <button type="button" className="update-btn" data-bs-dismiss="modal">Okay</button>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default CorporateEventDetail;