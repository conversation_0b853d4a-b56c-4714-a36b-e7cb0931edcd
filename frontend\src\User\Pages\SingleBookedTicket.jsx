import React, { useEffect, useReducer, useRef, useState } from "react";
import "./SingleBookedTicket.css";
import Ticket from "../../Assets/ticket.png";
import CorporateUserSideBar from "../Components/CorporateUserSideBar";
import CorporateNavBar from "../Components/corporateNavBar";
import { QRCodeSVG } from "qrcode.react";
import { API_URL } from "../../config";
import axios from "axios";
import { Loader } from "react-feather";
import { Link, useNavigate, useParams } from "react-router-dom";
import { IoIosArrowBack } from "react-icons/io";
import PuffLoader from "react-spinners/PuffLoader";
import ticket_Image_Templet from "../../Assets/TicketTemplate.png";
import NavBar from "../Components/NavBar";

// Initial state for the reducer
const initialState = {
  bookedevent: [],
  username: "",
};

// Reducer function to manage ticket and event detail state
const reducer = (state, action) => {
  switch (action.type) {
    case "SET_RESPONSE_DATA":
      return {
        ...state,
        bookedevent: action.payload.bookedevent,
        username: action.payload.username,
      };
    default:
      return state;
  }
};

const SingleBookedTicket = () => {
  const { booking_id } = useParams();
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const [showAllTickets, setShowAllTickets] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setuser] = useState(true);
  const Navigate = useNavigate();
  const [active, setActive] = useState(false);
  const [flipped, setFlipped] = useState(false);
  const [isFrontfaceVisible,setIsFrontfaceVisible] = useState(true);
  const boxRef = useRef(null);
  const animationRef = useRef(null);

  const animateOpacity = (from, to, duration = 500) => {
    const element = boxRef.current;
    let start = null;

    const step = (timestamp) => {
      if (!start) start = timestamp;
      const elapsed = timestamp - start;
      const progress = Math.min(elapsed / duration, 1);
      const newOpacity = from + (to - from) * progress;

      if (element) element.style.opacity = newOpacity;

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(step);
      }
    };

    cancelAnimationFrame(animationRef.current);
    animationRef.current = requestAnimationFrame(step);
  };

  useEffect(() => {
    // On state change, animate
    if (flipped) {
      animateOpacity(1, 0); // fade out
    } else {
      animateOpacity(0, 1); // fade in
    }
  }, [flipped]);

  // Function to handle location change
  const bookedTickets = async () => {
    try {
      const ticketResponse = await axios.get(
        `${API_URL}/ticket/yourticket/${booking_id}`
      );
      if (ticketResponse.status === 200) {
        let Data = ticketResponse.data.data;
        dispatch({
          type: "SET_RESPONSE_DATA",
          payload: {
            bookedevent: Data,
            username: ticketResponse.data.username,
          },
        });
        setuser(ticketResponse.data.username);
      }
      console.log("ORDER DATA saved: ", state.bookedevent);
    } catch (error) {
      alert("No data available!");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
  };

  const formatTimespanDate = (timestamp) => {
    const date = new Date(timestamp);

    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    };

    return date.toLocaleString("en-US", options);
  };

  const handleSupportClick = () => {
    setActive(!active);
    // alert("For support, contact: <EMAIL>\nOr call: +91-XXXXXXXXXX");
  };

  const handleCardClick = () => {
    setFlipped(!flipped);
  };

  useEffect(() => {
    bookedTickets();
    document.addEventListener("DOMContentLoaded", () => {
      const card = document.getElementById("ticketCard");

      // Make the whole ticket clickable for flipping
      card.addEventListener("click", () => {
        alert("flip event");
        card.classList.toggle("singlebookedticket-flip");
      });

      // Add a click listener to the 'support' text for more info
      const supportText = document.querySelector(".singlebookedticket-actions");
      supportText.addEventListener("click", (e) => {
        e.stopPropagation();
        alert(
          "For support, contact: <EMAIL>\nOr call: +91-9876543210"
        );
      });
    });
  }, []);

  useEffect(() => {
    // Scroll to top when the component loads or data is done loading
    if (!loading) {
      window.scrollTo({
        top: 0,
        behavior: "smooth", // Adds smooth scrolling
      });
    }
  }, [loading]);

  return (
    <>
      <div
        className="singlebookedticketpage"
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "start",
          alignItems: "center",
        }}
      >
        <NavBar userLocation={userLocation} isCrop={true} />
        {loading ? (
          <div
            className="d-flex justify-content-center align-items-center w-100"
            style={{ height: "100vh" }}
          >
            <PuffLoader size={28} loading={loading} />
          </div>
        ) : (
          <>
            <div className="mx-4 w-100 pt-3 px-3">
              {/* <div className="d-flex justify-content-center border-bottom border-primary mb-5 flex-row align-items-center">
                <span className="singlebookedticket-heading fw-semibold">
                  Your Front Seats
                </span>
              </div> */}
              <div>
                {!state.bookedevent ? (
                  <div>
                    <Loader />
                  </div>
                ) : state.bookedevent ? (
                  <>
                    {state.bookedevent.tickets.map((ticket, index) => {
                      return (
                        <div key={ticket._id}>
                          {ticket.ticket_type === "paid" ? (
                            state.bookedevent.isPaid ? (
                              <div
                                className={`singlebookedticket-flip-container ${
                                  flipped ? "singlebookedticket-flip" : ""
                                }`}
                                id="ticketCard"
                                onClick={handleCardClick}
                              >
                                <div className="singlebookedticket-flipper">
                                  <div ref={boxRef} className="singlebookedticket-container singlebookedticket-front">
                                    <div className="singlebookedticket-header">
                                      <div className="singlebookedticket-info">
                                        <h2>
                                          {
                                            state.bookedevent.eventDetails
                                              ?.event_name
                                          }
                                        </h2>
                                        <p>
                                          Venue:{" "}
                                          {state.bookedevent?.reserveeventcity}
                                        </p>
                                        <p>
                                          Time:{" "}
                                          {
                                            state.bookedevent.eventDetails
                                              ?.event_starts_Time
                                          }
                                        </p>
                                        <p>
                                          Date:{" "}
                                          {formatDate(
                                            state.bookedevent.eventreserveDate
                                              ? state.bookedevent
                                                  .eventreserveDate
                                              : state.bookedevent.eventDetails
                                                  ?.event_starts_date
                                          )}
                                        </p>
                                      </div>
                                    </div>

                                    <div
                                      className={`singlebookedticket-actions ${
                                        active
                                          ? "singlebookedticket-active"
                                          : ""
                                      }`}
                                      onClick={handleSupportClick}
                                    >
                                      Tap for support, details & more actions
                                    </div>

                                    <div className="singlebookedticket-middle">
                                      <p>
                                        {
                                          state.bookedevent
                                            .bookedTicketQuantities
                                        }{" "}
                                        x {ticket.ticket_Name}
                                      </p>
                                      <h3>ENTRY PASS</h3>
                                      <p>Booked by: {state.username}</p>
                                      <QRCodeSVG
                                        className="singlebookedticket-qr-image"
                                        value={`${ticket._id}_${state.bookedevent.order_id}`}
                                      />
                                    </div>

                                    <div className="singlebookedticket-note">
                                      Show this QR at entry. No re-entry allowed
                                      once scanned.
                                    </div>

                                    <div className="singlebookedticket-footer">
                                      <span>Total Amount</span>
                                      <strong>
                                        ₹ {state.bookedevent?.paid_Amount / 100}
                                      </strong>
                                    </div>
                                  </div>

                                  <div className="singlebookedticket-container singlebookedticket-back">
                                    <div className="singlebookedticket-back-content">
                                      <h3>Need Help?</h3>
                                      <p>Email: <EMAIL></p>
                                      <p>Phone: +91-9876543210</p>
                                      <p>
                                        Terms: No refund after booking. Entry
                                        allowed till 6:30 PM.
                                      </p>
                                    </div>
                                    <div className="singlebookedticket-note">
                                      Tap again to return
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              ""
                            )
                          ) : (
                            <div
                              className={`singlebookedticket-flip-container ${
                                flipped ? "singlebookedticket-flip" : ""
                              }`}
                              id="ticketCard"
                              onClick={handleCardClick}
                            >
                              <div className="singlebookedticket-flipper">
                                <div ref={boxRef} className="singlebookedticket-container singlebookedticket-front">
                                  <div className="singlebookedticket-header">
                                    <div className="singlebookedticket-info">
                                      <h2>
                                        {
                                          state.bookedevent.eventDetails
                                            ?.event_name
                                        }
                                      </h2>
                                      <p>
                                        Venue:{" "}
                                        {state.bookedevent?.reserveeventcity}
                                      </p>
                                      <p>
                                        Time:{" "}
                                        {
                                          state.bookedevent.eventDetails
                                            ?.event_starts_Time
                                        }
                                      </p>
                                      <p>
                                        Date:{" "}
                                        {formatDate(
                                          state.bookedevent.eventreserveDate
                                            ? state.bookedevent.eventreserveDate
                                            : state.bookedevent.eventDetails
                                                ?.event_starts_date
                                        )}
                                      </p>
                                    </div>
                                  </div>

                                  <div
                                    className={`singlebookedticket-actions ${
                                      active ? "singlebookedticket-active" : ""
                                    }`}
                                    onClick={handleSupportClick}
                                  >
                                    Tap for support, details & more actions
                                  </div>

                                  <div className="singlebookedticket-middle">
                                    <p>
                                      {state.bookedevent.bookedTicketQuantities}{" "}
                                      x {ticket.ticket_Name}
                                    </p>
                                    <h3>ENTRY PASS</h3>
                                    <p>Booked by: {state.username}</p>
                                    <QRCodeSVG
                                      className="singlebookedticket-qr-image"
                                      value={`${ticket._id}_${state.bookedevent.order_id}`}
                                    />
                                  </div>

                                  <div className="singlebookedticket-note">
                                    Show this QR at entry. No re-entry allowed
                                    once scanned.
                                  </div>

                                  <div className="singlebookedticket-footer">
                                    <span>Total Amount</span>
                                    <strong>
                                      ₹ {state.bookedevent?.paid_Amount / 100}
                                    </strong>
                                  </div>
                                </div>

                                <div className="singlebookedticket-container singlebookedticket-back">
                                  <div className="singlebookedticket-back-content">
                                    <h3>Need Help?</h3>
                                    <p>Email: <EMAIL></p>
                                    <p>Phone: +91-9876543210</p>
                                    <p>
                                      Terms: No refund after booking. Entry
                                      allowed till 6:30 PM.
                                    </p>
                                  </div>
                                  <div className="singlebookedticket-note">
                                    Tap again to return
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <div>No tickets available</div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default SingleBookedTicket
