// models/User.js

const mongoose = require('mongoose');

const vendorUserSchema = new mongoose.Schema({
  _id: {
    type: String,
  },
  phone: {
    type: String,
    required: true,
    unique: true
  },

  vendorType: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  Gender:{
    type: String
  },
  pan_no: {
    type: String,
    required: true
  },
  address: {
    type: String,
    required: true
  },
  isGSTIN_No: {
    type: String,
  },
  GSTIN_Details: [{
    GSTIN_No: String,
    state: String
  }],
  userName: { 
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true
  },
  bank_Account_type: {
    type: String,
    required: true
  },
  beneficiar_name: {
    type: String,
    required: true
  },
  bank_account_no: {
    type: String,
    required: true
  },
  bank_name: {
    type: String,
    required: true
  },
  IFSC_code: {
    type: String,
    required: true
  },
  ITR_Filed: {
    type: String
  },
  cancel_cheque: {
    type: String,
    required: true
  },
  pan_card: {
    type: String,
    required: true
  },
  signed_Agreement: {
    type: String,
    required: true
  },
  is2YearsITR: {
    type: String
  },
  undertaking_Accepted: {
    type: Boolean
  },
  gender: String,
  additionalBank: [{
    bank_Account_type: {
      type: String,
      required: true
    },
    beneficiar_name: {
      type: String,
      required: true
    },
    bank_account_no: {
      type: String,
      required: true
    },
    bank_name: {
      type: String,
      required: true
    },
    IFSC_code: {
      type: String,
      required: true
    },
  }],
  createdAt: {
    type: Number,
    default: Date.now,
  }
});

const VendorUser = mongoose.model('Vendor', vendorUserSchema);

module.exports = VendorUser;

