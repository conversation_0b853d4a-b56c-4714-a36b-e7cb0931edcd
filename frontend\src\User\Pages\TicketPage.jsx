import React, { useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import LocationModal from "../Components/LocationModal";
import NavBar from "../Components/NavBar";
import SelectTickets from "../Components/SelectTickets";
import PuffLoader from "react-spinners/PuffLoader";

function TicketPage() {
  const { id } = useParams();
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [loading, setLoading] = useState(false); // New state to handle loading
  const location = useLocation();
  const { date_for_booked_event } = location.state;

  // Simulate data fetching or loading process
  // useEffect(() => {
  //     const loadTimeout = setTimeout(() => {
  //         setLoading(false); // Stop loading after 2 seconds (simulate)
  //     }, 2000); // Adjust timing or replace with actual data fetching logic

  //     return () => clearTimeout(loadTimeout); // Cleanup timeout on unmount
  // }, []);

  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    localStorage.setItem("userLocation", location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  // useEffect(() => {
  //   // Scroll to top when the component loads or data is done loading
  //   if (!loading) {
  //     window.scrollTo({
  //       top: 0,
  //       behavior: "smooth", // Adds smooth scrolling
  //     });
  //   }
  // }, [loading]);

  return (
    <div
      // style={{
      //   width: "100%",
      //   display: "flex",
      //   flexDirection: "column",
      //   justifyContent: "start",
      //   alignItems: "center",
      // }}
      style={
        {
          // paddingTop: 68,
        }
      }
    >
      {/* Show LocationModal */}
      {showLocationModal && (
        <LocationModal
          onClose={handleCloseLocationModal}
          onLocationChange={handleLocationChange}
        />
      )}
      {/* <NavBar
        userLocation={userLocation}
        onNext={() => setShowLocationModal(true)}
        disableMargin={true}
      /> */}
      {loading ? (
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ height: "100vh" }}
        >
          <PuffLoader size={28} loading={loading} />
        </div>
      ) : (
        <SelectTickets id={id} date_for_booked_event={date_for_booked_event} />
      )}
    </div>
  );
}

export default TicketPage;
