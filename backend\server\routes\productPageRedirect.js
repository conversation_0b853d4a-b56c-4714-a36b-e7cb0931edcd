const express = require('express');
const router = express.Router();
const Card = require('../models/Event');

// Route to handle product page redirection
router.get('/api/cards/:id', async (req, res) => {
  try {
    const card = await Card.findById(req.params.id);
    console.log('Retrieved card:', card);
    if (!card) {
      return res.status(404).json({ error: 'Card not found' });
    }
    // Instead of redirecting, simply send the card details
    res.status(200).json(card);
  } catch (error) {
    console.error('Error fetching card details:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;

