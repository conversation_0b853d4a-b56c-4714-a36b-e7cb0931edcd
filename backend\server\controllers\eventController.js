const CeremonyModel = require('../models/CeremonyModel')
const AdditionalDataModel = require('../models/AdditionDataModel')
const CategoryModel = require('../models/CategoryModel');
const CitiesModel = require('../models/CitiesModel');
const StateModel = require('../models/StateDataModel');
const TicketModel = require('../models/TicketModel');

const CreateNewEvent = async (req, res) => {
    try {
        // Find the most recent event by creation time
        const lastEvent = await CeremonyModel.findOne().sort({ createdAt: -1 }).exec();
        let event_id;

        // If no events exist, start from MFS00001
        if (!lastEvent || !lastEvent.unique_id) {
            event_id = 'MFS00001';
        } else {
            // Extract the numerical part of the last unique_id
            const lastId = lastEvent.unique_id;
            const lastNumber = parseInt(lastId.substring(3), 10); // Get the number after 'MFS'
            const newIdNumber = lastNumber + 1; // Increment the number
            event_id = `MFS${String(newIdNumber).padStart(5, '0')}`; // Format it to MFS00002, MFS00003, etc.
        }

        // Create new event with auto-generated unique_id
        const newEvent = new CeremonyModel({
            unique_id: event_id,
            event_name: req.body.event_name,
            category: req.body.category,
            event_starts_date: req.body.event_starts_date,
            event_starts_Time: req.body.event_starts_Time,
            event_ends_date: req.body.event_ends_date,
            event_ends_Time: req.body.event_ends_Time,
            isEvent_repeat: req.body.isEvent_repeat,
            isAllSession: req.body.isAllSession,
            event_repeats: req.body.event_repeats,
            event_city: req.body.event_city,
            event_venue: req.body.event_venue,
            venue_latitude: req.body.venue_latitude,
            venue_longitude: req.body.venue_longitude,
            event_description: req.body.event_description,
            termsCondition: req.body.termsCondition,
            guidelines: req.body.guidelines,
            cover_image: req.body.cover_image,
            eventCreatedBy: req.body.vendor_id,
            eventForMultiCity: req.body.eventForMultiCity,
            multiCityArray: req.body.multiCityArray.map(city => ({
                event_city: city.event_city,
                event_venue: city.event_venue,
                event_date: city.event_date,
                show_time: city.show_time.map(time => ({
                    time: time.time
                }))
            }))
        });

        // Save the New Event
        await newEvent.save()
            .then(event => {
                res.status(200).send({ message: "Event added successfully", event });
            })
            .catch(error => {
                res.status(201).send({ message: "Unable to add event, please try again", error: error.message });
            });
    } catch (error) {
        res.status(500).send({ message: "Something went wrong, try again after some time", error: error.message });
    }
};

const StoreTicket = async (req, res) => {
    try {
        const { event_id, ticket_id } = req.body;
        const updatedCeremony = await CeremonyModel.findOneAndUpdate(
            { _id: event_id },
            {
                $push: {
                    ticket: ticket_id,
                }
            },
            { new: true }
        );
        res.status(200).send({ message: 'Ticket Add-on stored successfully', updatedCeremony });
    } catch (error) {
        res.status(500).send({ message: 'An error occurred while storing the ticket', error });
    }
};

const StoreAddOn = async (req, res) => {
    const { event_id, addon_id } = req.body;
    try {
        const updatedCeremony = await CeremonyModel.findOneAndUpdate(
            { _id: event_id },
            {
                $push: {
                    Addon: addon_id
                }
            },
            { new: true }
        );
        res.status(200).send({ message: 'Ticket Add-on stored successfully', updatedCeremony });
    } catch (error) {
        res.status(500).send({ message: 'An error occurred while storing the add-on', error });
    }
};

// Post method for Addon data
const AdditionalData = async (req, res) => {
    try {
        const {
            Event_genre,
            Event_lang,
            Age_restriction,
            Instagram_account,
            Spotify_account,
            custom_info,
            picture,
            Venuepicture,
            artists
        } = req.body;

        // Create a new instance of the model with the received data
        const addonData = new AdditionalDataModel({
            Event_genre,
            Event_lang,
            Age_restriction,
            Instagram_account,
            Spotify_account,
            custom_info,
            picture,
            Venuepicture,
            Artists: artists.map(artist => ({
                artist_name: artist.Artist, // Correct field name
                artist_pic: artist.Artist_pic // Assuming this is the filename or URL of the uploaded artist picture
            }))
        });

        // Save the new event
        await addonData.save()
            .then(data => {
                res.status(200).send({ message: "Data added successfully", data });
            })
            .catch(error => {
                res.status(400).send({ message: "Unable to add. Please try again", error });
            });
    } catch (error) {
        res.status(500).send({ message: "Something went wrong, try again after some time", error });
    }
};

// const UpdateAdditionalChanges = async (req, res) => {
//     const { addon_id } = req.params;
//     try {
//         const {
//             Event_genre,
//             Event_lang,
//             Age_restriction,
//             Instagram_account,
//             Spotify_account,
//             custom_info,
//             picture,
//             Venuepicture,
//             artists
//         } = req.body;

//         // Create a new instance of the model with the received data
//         const addonData = await AdditionalDataModel.findByIdAndUpdate(addon_id, {
//             Event_genre,
//             Event_lang,
//             Age_restriction,
//             Instagram_account,
//             Spotify_account,
//             custom_info,
//             picture,
//             Venuepicture,
//             Artists: artists.map(artist => ({
//                 artist_name: artist.artist_name, // Correct field name
//                 artist_pic: artist.artist_pic // Assuming this is the filename or URL of the uploaded artist picture
//             }))
//         },
//             { new: true }
//         );
//         if (addonData) {
//             res.status(200).send({ message: "Data added successfully", addonData });
//         } else {
//             res.status(400).send({ message: "Unable to add. Please try again", error });
//         }
//     } catch (error) {
//         res.status(500).send({ message: "Something went wrong, try again after some time", error: error.message });
//     }
// };

const UpdateAdditionalChanges = async (req, res) => {
    const { addon_id } = req.params;
  
    try {
      const {
        event_genre,
        event_lang,
        age_restriction,
        instagram_account,
        spotify_account,
        custom_info,
        picture,
        venue_picture,
        artists
      } = req.body;
  
      const updatedData = {
        event_genre,
        event_lang,
        age_restriction,
        instagram_account,
        spotify_account,
        custom_info,
        picture,
        venue_picture,
        Artists: artists.map((artist) => ({
          artist_name: artist.artist_name,
          artist_pic: artist.artist_pic
        }))
      };
  
      const addonData = await AdditionalDataModel.findByIdAndUpdate(addon_id, updatedData, { new: true });
  
      if (!addonData) {
        return res.status(404).json({ message: "Add-on not found" });
      }
  
      res.status(200).json({
        message: "Add-on updated successfully",
        data: addonData
      });
    } catch (error) {
      console.error("Update failed:", error);
      res.status(500).json({
        message: "Internal server error",
        error: error.message
      });
    }
  };
  
const UpdatesetIsAllSession = async (req, res) => {
    const { id } = req.params;
    console.log("Received ID:", id);
    console.log("Received isAllSession:", req.body.isAllSession);

    try {
        const updateValue = await CeremonyModel.findOneAndUpdate(
            { _id: id },  // Ensure this matches the correct ID format
            { isAllSession: req.body.isAllSession },
            { new: true }
        );

        console.log("Updated document:", updateValue);

        if (!updateValue) {
            return res.status(404).json({ message: "Category not found or update failed" });
        }

        return res.status(200).json({ message: "Updated successfully", updateValue });

    } catch (error) {
        console.error("Error updating document:", error);
        return res.status(500).json({ message: "Unable to update value", error: error.message });
    }
};

const CitiesName = async (req, res) => {
    try {
        const cities = await StateModel.distinct("City");
        res.status(200).send(cities);
    }
    catch (err) {
        res.status(500).send({ message: "Something went wrong" });
    }
};

const SearchCityName = async (req, res) => {
    const query = req.query.q;
    try {
        const cities = await StateModel.distinct("City");
        const filteredCities = cities.filter(city =>
            city.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 500); // Limit results for performance
        res.json(filteredCities);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Fetch Events and Tickets
const FeatchEvents = async (req, res) => {
    try {
        const tickets = await CeremonyModel.find().populate('ticket');
        if (tickets.length > 0) {
            res.status(200).send(tickets);
        } else {
            res.status(201).send({ message: "No events are available" });
        }
    } catch (err) {
        console.error(err); // Log the error for debugging
        res.status(500).send({ message: "Something went wrong" });
    }
};

// functionality to featch event by Location
const FeatchEventsByCity = async (req, res) => {
    try {
        const events = await CeremonyModel.find({
          $or: [
            { event_city: req.params.city },
            { multiCityArray: { $elemMatch: { event_city: req.params.city } } },
          ],
        }).populate("ticket");

        // Filter events with Addon length > 0
        const filteredEvents = events.filter(e => e.Addon && e.Addon.length > 0);

        // Check if filtered events exist
        if (filteredEvents.length > 0) {
            res.status(200).send(filteredEvents);
        } else {
            res.status(201).send({ message: ` No events are available for ${req.params.city}` });
        }
    } catch (err) {
        res.status(500).send({ message: "Something went wrong", error: err.message });
    }
};

const FeatchEventDetail = async (req, res) => {
    try {
        // Fetch the event and populate related data
        const event = await CeremonyModel.find({ unique_id: req.params.id })
            .populate('ticket')
            .populate('Addon');

        // Filter events with Addon length > 0
        const filteredEvents = event.filter(e => e.Addon && e.Addon.length > 0);

        // Check if filtered events exist
        if (filteredEvents.length > 0) {
            res.status(200).send(filteredEvents);
        } else {
            res.status(201).send({ message: "No events with Addons are available" });
        }
    } catch (err) {
        res.status(500).send({ message: "Something went wrong", error: err.message });
    }
};


const UpdateEventDetail = async (req, res) => {
    const { event_id } = req.params;
    try {
        const updatedEvent = await CeremonyModel.findByIdAndUpdate(
            event_id,  // Correctly pass the ID here, not as an object
            {
                event_name: req.body.event_name,
                category: req.body.category,
                event_starts_date: req.body.event_starts_date,
                event_starts_Time: req.body.event_starts_Time,
                event_ends_date: req.body.event_ends_date,
                event_ends_Time: req.body.event_ends_Time,
                isEvent_repeat: req.body.isEvent_repeat,
                isAllSession: req.body.isAllSession,
                event_repeats: req.body.event_repeats,
                event_city: req.body.event_city,
                event_venue: req.body.event_venue,
                venue_latitude: req.body.venue_latitude,
                venue_longitude: req.body.venue_longitude,
                event_description: req.body.event_description,
                termsCondition: req.body.termsCondition,
                guidelines: req.body.guidelines,
                cover_image: req.body.cover_image,
                eventCreatedBy: req.body.vendor_id,
                eventForMultiCity: req.body.eventForMultiCity,
                eventCreatedBy: req.body.vendor_id,
                multiCityArray: req.body.multiCityArray.map(city => ({
                    event_city: city.event_city,
                    event_venue: city.event_venue,
                    event_date: city.event_date,
                    show_time: city.show_time.map(time => ({
                        time: time.time
                    }))
                }))
            },
            { new: true }  // This option returns the updated document
        );

        if (updatedEvent) {
            res.status(200).send({ message: "Event updated successfully", event: updatedEvent });
        } else {
            res.status(404).send({ message: "Event not found" });
        }
    } catch (error) {
        res.status(500).send({ message: "Something went wrong, try again later", error :error.message });
    }
};



const InvoiceDetails = async (req, res) => {
    const ticket_id = req.query.ticket_id;
    try {
        const event = await CeremonyModel.find({ _id: req.params.id }).populate('ticket').populate('Addon');
        if (event.length > 0) {
            const eventDetails = {
                _id: event[0]._id,
                unique_id: event[0].unique_id,
                event_name: event[0].event_name,
                event_starts_date: event[0].event_starts_date,
                event_starts_Time: event[0].event_starts_Time,
                event_venue: event[0].event_venue,
                event_city: event[0].event_city,
                basic_price: event[0].basic_price,
                cover_image: event[0].cover_image,
                eventCreatedBy: event[0].eventCreatedBy,
            }
            // const ticket = event[0].ticket
            const ticket = event[0].ticket.find(t => t._id.toString() === ticket_id);

            res.status(200).send({ event: eventDetails, ticket: ticket });
        } else {
            res.status(201).send({ message: "No events are available" });
        }
    } catch (err) {
        res.status(500).send({ message: "Something went wrong" });
    }
};

// post New Category
const AddNewEventCategory = async (req, res) => {
    const { category, image } = req.body;
    try {
        const newCategory = new CategoryModel({
            category: category,
            image: image
        });
        await newCategory.save();
        res.status(200).send({ message: "New Category Created successfully", category: newCategory });
    } catch (error) {
        res.status(500).send({ message: "Unable to create category", error });
    }
};


const EventCategory = async (req, res) => {
    try {
        const category = await CategoryModel.find()
        if (category.length > 0) {
            res.status(200).send(category);
        } else {
            res.status(200).send({ message: "No category" });
        }
    } catch (error) {
        res.status(500).send({ message: "Something went wrong" });
    }
};

const FeatchEventByCategory = async (req, res) => {
    try {
        const tickets = await CeremonyModel.find({ category: req.params.category }).populate('ticket');

        // Filter events with Addon length > 0
        const filteredEvents = tickets.filter(e => e.Addon && e.Addon.length > 0);

        // Check if filtered events exist
        if (filteredEvents.length > 0) {
            return res.status(200).send(filteredEvents);
        } else {
            res.status(201).send({ message: ` No events are available for ${req.params.city}` });
        }
    } catch (err) {
        res.status(500).send({ message: "Something went wrong", error: err.message });
    }
};

const DeleteEventRecord = async (req, res) => {
    try {
      // Find the event by ID
      const deleteCompanyEvents = await CeremonyModel.findByIdAndDelete(req.params.event_id);
      console.log("DeleteCompanyEvents : ", deleteCompanyEvents);
  
      if (!deleteCompanyEvents) {
        return res.status(404).json({ message: "Event not found" });
      }
  
      const deletionPromises = [];
  
      // Delete associated tickets if they exist
      if (deleteCompanyEvents.ticket && deleteCompanyEvents.ticket.length > 0) {
        const ticketDeletionPromises = deleteCompanyEvents.ticket.map(ticketId =>
          TicketModel.findByIdAndDelete(ticketId)
        );
        deletionPromises.push(...ticketDeletionPromises);
      }
  
      // Delete associated addons if they exist
      if (deleteCompanyEvents.Addon && deleteCompanyEvents.Addon.length > 0) {
        const addonDeletionPromises = deleteCompanyEvents.Addon.map(addonId =>
            AdditionalDataModel.findByIdAndDelete(addonId)
        );
        deletionPromises.push(...addonDeletionPromises);
      }

      await Promise.all(deletionPromises);
  
      res.status(200).json({ message: "Event deleted successfully!" });
    } catch (error) {
      console.error("Error in deleting event record:", error);
      res.status(500).json({ message: "Unable to delete event record" });
    }
  };
  

module.exports = {
    CreateNewEvent, AdditionalData, CitiesName, 
    FeatchEvents, StoreTicket, StoreAddOn, FeatchEventDetail, 
    SearchCityName, FeatchEventsByCity, EventCategory, FeatchEventByCategory,
    AddNewEventCategory, InvoiceDetails, UpdateEventDetail, UpdateAdditionalChanges, 
    UpdatesetIsAllSession, DeleteEventRecord
};