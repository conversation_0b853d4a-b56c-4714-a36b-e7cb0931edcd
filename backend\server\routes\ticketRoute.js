const express = require('express');
const { CreateNewTicket, FetchTickets, DeleteTicket, 
    SetTicketBookingCount,FetchUserTickets, StoreFreeTickets, 
    FetchTicketByTicket_id, UpdateTicketDetails, Findusermaxticket,
    UpdateTicketSoldOut, FetchSingleBookedEventByBookingId } = require('../controllers/ticketController');
const { SendMail } = require('../controllers/emailController');

const ticketRouter = express.Router();


ticketRouter.post('/createticket',CreateNewTicket);
ticketRouter.get('/gettickets/:id',FetchTickets);
ticketRouter.get('/tickets/:userId',FetchUserTickets);
ticketRouter.delete('/tickets/:id',DeleteTicket);
ticketRouter.get('/get-ticket/:ticket_id',FetchTicketByTicket_id);
ticketRouter.put('/Updateticket/:ticket_id',UpdateTicketDetails);
ticketRouter.put('/update-ticket-sold-out/:ticket_id',UpdateTicketSoldOut);
ticketRouter.post('/booktickets',SetTicketBookingCount);
ticketRouter.post('/bookfreetickets',StoreFreeTickets);
ticketRouter.get('/findusermaxticket/:user_id/:ticket_id/:event_id',Findusermaxticket);
ticketRouter.post('/sendmail',SendMail);
ticketRouter.get('/yourticket/:order_id', FetchSingleBookedEventByBookingId);

module.exports = ticketRouter;