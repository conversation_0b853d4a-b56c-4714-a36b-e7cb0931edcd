import React, { useState } from "react";
import './AboutUsPage.css'
import NavBar from "../Components/NavBar";
import LocationModal from "../Components/LocationModal";
import { useNavigate } from "react-router-dom";
import Footer from "../Components/Footer";
import Crown from '../../Assets/about us_what we offer-01.png'
import Box from '../../Assets/about us_what we offer-02.png'
import Sound from '../../Assets/about us_what we offer-03.png'
import Puzzle from '../../Assets/about us_what we offer-04.png'

const AboutUsPage = () => {
    const [userLocation, setUserLocation] = useState(localStorage.getItem('userLocation') || 'Nagpur');
    const [showLocationModal, setShowLocationModal] = useState(false);

    // Function to handle location change
    const handleLocationChange = (location) => {
        setUserLocation(location);
        localStorage.setItem('userLocation', location);
        setShowLocationModal(false); // Close modal after location change
    };

    // Function to handle closing the location modal
    const handleCloseLocationModal = () => {
        setShowLocationModal(false);
    };

    const Navigate = useNavigate();

    return (
        <div style={{width:"100%",display:'flex',flexDirection:'column',justifyContent:'start',alignItems:'center'}}>
            <NavBar userLocation={userLocation} onNext={() => setShowLocationModal(true)} />
            {/* Render Location component as a modal */}
            {showLocationModal && (
                <LocationModal
                    onClose={() => setShowLocationModal(false)}
                    onLocationChange={handleLocationChange}
                />
            )}
            <div className="about-head">
                    <div className="text-center">
                        <h1 className="about-heading">About Us</h1>
                        <p className="about-sub-heading">Because your story deserves the best view.</p>
                    </div>
                </div>

            {/* Content */}
            <div className="about-content-container">
            <p class="mt-1 mt-sm-5 heading-style">
            Bringing You Closer to the Action with the Best Seats and Hassle Free Booking!
            </p>

                    <p className="para-style">
                        At My Front Seat, we connect you with the events you love and ensure you get the best seat in the house. Whether it's a thrilling concert, an exciting sports match, or an inspiring workshop, we make ticket-buying simple and secure. With our user-friendly platform and top-notch security, you can focus on enjoying the event while we handle the details. Get ready to dive into your favourite experiences effortlessly!
                    </p>


                    <p className="heading-style">
                        Our Story
                    </p>

                    <p className="para-style">
                        To make your event experiences unforgettable. We saw that finding the best seats and securing tickets could be a hassle, so we created My Front Seat to simplify the process. Whether it’s a thrilling concert, an exciting sports game, or an inspiring workshop, we’re here to connect you with the events you love. With a focus on seamless booking and top-notch security, our team is dedicated to making sure you get the best seat and enjoy every moment. Join us as we bring you closer to the action and make every event exceptional.
                    </p>



                    <p className="heading-style">
                        What We Offer
                    </p>

                    <div className="about-offering-container">
                        <div className="text-center">
                            <img className="about_img" src={Crown}></img>
                            <p className="para-style about-offering-text">VIP Experience</p>
                        </div>

                        <div className="text-center">
                            <img className="about_img" src={Puzzle}></img>
                            <p className="para-style about-offering-text">Personalised Picks</p>
                        </div>
                        <div className="text-center">
                            <img className="about_img" src={Sound}></img>
                            <p className="para-style about-offering-text">Marketing Strategy</p>
                        </div>
                        <div className="text-center">
                            <img className="about_img" src={Box}></img>
                            <p className="para-style about-offering-text">Pricing Strategy</p>
                        </div>
                    </div>

                </div>


            <Footer />
        </div>
    )
}

export default AboutUsPage;