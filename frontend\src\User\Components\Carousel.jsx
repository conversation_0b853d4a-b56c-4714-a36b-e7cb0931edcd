import { useEffect, useState } from 'react';
import axios from 'axios';
import { API_URL } from '../../config.js';
import './Carousel.css';

function Carousel() {
  const [caraousalsImages, setCaraousalsImages] = useState([]);

  useEffect(() => {
    const getCarouselImages = async () => {
      try {
        const response = await axios.get(`${API_URL}/admin/carousel_img`);
        if (response.status === 200 && response.data?.carousel_img) {
          setCaraousalsImages(response.data.carousel_img);
        }
      } catch (err) {
        console.error(err);
      }
    };
    getCarouselImages();
  }, []);

  // Manually initialize the carousel
  useEffect(() => {
    if (caraousalsImages.length > 0) {
      const carousel = document.querySelector("#carouselSlidesID");
      if (carousel) {
        new window.bootstrap.Carousel(carousel, {
          interval: 5000, // Ensure the interval is set
          ride: "carousel", // Start automatically
          touch: true,
          pause: "false"
        });
      }
    }
  }, [caraousalsImages]);

  return (
    <div
      id="carouselSlidesID"
      className="carousel slide carousel-main"
      style={{
        padding: "1rem",
        borderRadius: "1.5rem",
        width: "100%",
        overflow: "hidden",
      }}
      data-bs-ride="carousel"
      data-bs-interval="5000"
      data-bs-touch="true"
      data-bs-pause="false"
    >
      <div className="carousel-indicators">
        {caraousalsImages.map((_, index) => (
          <button
            key={index}
            type="button"
            data-bs-target="#carouselSlidesID"
            data-bs-slide-to={index}
            className={index === 0 ? "active" : ""}
            aria-current={index === 0 ? "true" : "false"}
            aria-label={`Slide ${index + 1}`}
          ></button>
        ))}
      </div>
      <div className="carousel-inner innercarouselDiv">
        {caraousalsImages?.map((image, index) => (
          <div
            key={index}
            className={`carousel-item carouselimgDiv ${
              index === 0 ? "active" : ""
            }`}
            data-bs-interval="5000"
            style={{ background: "white" }}
          >
            <img
              src={image.image}
              alt={`Banner ${index + 1}`}
              style={{
                width: "100%",
                borderRadius: "1.5rem",
                display: "block",
              }}
              className="carousel-img"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export default Carousel;
