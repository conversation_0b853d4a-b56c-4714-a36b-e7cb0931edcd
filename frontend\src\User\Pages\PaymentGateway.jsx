// import React, { useEffect, useState } from 'react';
// import './PaymentGateway.css'
// import Logo from '../../Assets/logo_224x61-02.png'
// import axios from 'axios';
// import { API_URL } from '../../config';
// import { BsXCircle } from 'react-icons/bs';
// import { useLocation } from 'react-router-dom';

// function PaymentGateway() {
//     const location = useLocation();
//   const { billing, ticket, eventDetail, ticketids  } = location.state;
//   // Payment Process
//   const [responseId, setResponseId] = useState("");
//   const [responseState, setResponseState] = useState([]);

//   const LoadScript = (src) => {
//     return new Promise((resolve) => {
//       const script = document.createElement("script");
//       script.src = src;
//       script.onload = () => { resolve(true) };
//       script.onerror = () => { resolve(false) };
//       document.body.appendChild(script);
//     })
//   }
//   const CreateOrder = async () => {
//   console.log("ticketids : ", ticketids)
//    const  amt = billing.total_amount*100;
//      let data = JSON.stringify({
//        amount: amt,
//        currency: "INR",
//        user_id : user._id,
//        ticketDetails: ticketids,
//        eventDetails: eventDetail,
//      });

//     let config = {
//       method: "POST",
//       maxBodyLength: Infinity,
//       url: `${API_URL}/pay/payment-order`,
//       headers: {
//         "Content-Type": "application/json",
//       },
//       data: data
//     }
//     // api call
//     const res = await axios.request(config)
//       handleRazorpayScreen(res.data.amount, res.data.order_id)
//   }

//   const handleRazorpayScreen = async (amount,order_id) => {
//     const res = await LoadScript("https://checkout.razorpay.com/v1/checkout.js");
//     if (!res) {
//       alert("Some error at razorpay");
//       return;
//     }
//     const options = {
//      //  "key": "rzp_test_LGorMBgiUWfWP4", // testing key
//      //  "key": "rzp_live_JUrAAD75BEmsB7", // live key
//      "key": "rzp_live_QQmdUNR3XVKX87",
//       "amount": amount,
//       "currency": "INR",
//       "name": "My Front Seat",
//       "description": "Payment to My-Front-Seat",
//       "order_id": order_id,
//       "image": Logo,
//       "handler": function (response) {
//         setResponseId(response.razorpay_payment_id);
//         CheckPayment(order_id);
//       },
//       "prefill": {
//         "name": "My Front Seat",
//         "email": "<EMAIL>"
//       },
//       "theme": {
//         "color": "#04092C"
//       }
//     }
//     const PaymentObject = new window.Razorpay(options);
//     PaymentObject.open();
//   }

//   const CheckPayment = async (order_id)=>{
//    const response = await axios.get(`${API_URL}/pay/payment/status/${order_id}`);
//    if(response.data.status){
//      alert("Payment successful! Details have been saved.");
//      Navigate('/tickeetbooked');
//   }else{
//    alert("Unable to process your payment");
//   }
//  }

//   useEffect(() => {
//     CreateOrder();
//   }, []);
// }

// export default PaymentGateway;