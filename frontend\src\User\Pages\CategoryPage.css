/* Ensures the body and main container take full height */

  
  .category-page-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100vh;
  }
  
  .category-page-container {
    width: 100%;
    max-width: 1260px;
    margin: 0 auto;
    height: 100%;
    min-height: 100vh;
    /* padding: 2rem; */
  }
  
  .category-page-title {
    /* margin-top: 2rem; */
    font-size: 25px;
    font-weight: 600;
    padding: 1rem;
    margin-bottom: 0 !important;
    border-bottom: 1px solid #8f8f8f40;
  }

  @media (max-width: 768px) {
    .category-page-title {
      font-size: 1rem;
    }
    .category-page-container {
      
    }
  }
  

  