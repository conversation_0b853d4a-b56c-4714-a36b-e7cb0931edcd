import React, { useEffect, useState } from "react";
import './userSideBar.css'
import { Link, useNavigate, useLocation } from "react-router-dom";
import femaleProfilePic from '../../Assets/femaleprofile.png';
import maleProfilePic from '../../Assets/maleprofile.png';
import { AiOutlineAudit, AiOutlineUser } from "react-icons/ai";
import { TbArrowLeftFromArc } from "react-icons/tb";
import { BsTicketDetailed } from "react-icons/bs";
import { BiFile } from "react-icons/bi";

const UserSideBar = () => {
    const user = JSON.parse(localStorage.getItem('userData'));
    const navigate = useNavigate();
    const location = useLocation();
    const [activeLink, setActiveLink] = useState(location.pathname);

    const handleLogout = () => {
        localStorage.clear();
        navigate('/');
    };

    const handleLinkClick = (path) => {
        setActiveLink(path);
    };
    return (
      <>
        <div
          className="col-lg-2 col-md-2 col-sm-3 col-3 border-end border-1 "
          id="usersidebar"
        >
          <div className="sideBar w-100 p-2">
            <div className="userProfile">
              {user ? (
                user.gender === "female" ? (
                  <img
                    className="eventImg"
                    src={femaleProfilePic}
                    alt="profilePic"
                  ></img>
                ) : (
                  <img
                    className="eventImg"
                    src={maleProfilePic}
                    alt="profilePic"
                  />
                )
              ) : (
                ""
              )}
              {/* <img className='eventImg' src={SampleImg} alt='profilePic'></img> */}
              <h4 className="username mt-3">
                Hi, {user ? user.firstName : "Guest"}
              </h4>
            </div>
            <div className="side-NavBar">
              <div
                className={`nav-contents pl-2 ${
                  activeLink === "/userprofile" ? "bg-primary text-white" : ""
                }`}
              >
                <Link
                  to="/userprofile"
                  className="link-dark link-underline-opacity-0 fw-semibold p-2 w-100 px-3"
                  onClick={() => handleLinkClick("/userprofile")}
                >
                  <AiOutlineUser /> <span>Profile</span>
                </Link>
              </div>
              <div
                className={`nav-contents pl-2 ${
                  activeLink === "/yourtickets" ? "bg-primary text-white" : ""
                }`}
              >
                <Link
                  to="/yourtickets"
                  className="link-dark link-underline-opacity-0 fw-semibold p-2 w-100 px-3"
                  onClick={() => handleLinkClick("/yourtickets")}
                >
                  <BsTicketDetailed /> <span>Your Tickets</span>
                </Link>
              </div>
              <div
                className={`nav-contents pl-2 ${
                  activeLink === "/reward-points" ? "bg-primary text-white" : ""
                }`}
              >
                <Link
                  to="/reward-points"
                  className="link-dark link-underline-opacity-0 fw-semibold p-2 w-100 px-3"
                  onClick={() => handleLinkClick("/reward-points")}
                >
                  <BiFile /> <span>Reward Points</span>
                </Link>
              </div>
              <div className="nav-contents pl-2">
                <Link
                  to="/"
                  className="link-dark link-underline-opacity-0 fw-semibold p-2 w-100 px-3"
                  onClick={handleLogout}
                >
                  <TbArrowLeftFromArc /> <span>Logout</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </>
    );
}

export default UserSideBar;
