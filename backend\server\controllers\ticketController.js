const BookingModel = require("../models/BookingModel");
const TicketModel = require("../models/TicketModel");
const crypto = require('crypto');
const mongoose = require('mongoose');


// added per day tickets als
const moment = require("moment"); // Import moment for date handling
const CeremonyModel = require("../models/CeremonyModel");
const User = require("../models/User");
 
const CreateNewTicket = async (req, res) => {
  try {
    const { total_Quantity, per_day } = req.body;

    // Initialize daily quantities if `per_day` is true
    let dailyTicketQuantities = {};
    let daysCount = 1;

    const eventDates = await CeremonyModel.findOne({ _id: req.body.event_id });
    if (per_day && eventDates?.event_starts_date && eventDates?.event_ends_date) {
      let currentDate = moment(eventDates.event_starts_date);
      const endDate = moment(eventDates.event_ends_date);

      // Calculate the number of days in the event
      daysCount = endDate.diff(currentDate, 'days') + 1;
      const dailyQuantity = total_Quantity; // Set total quantity per day

      // Distribute the quantity per day in `daily_ticket_quantities`
      while (currentDate.isSameOrBefore(endDate)) {
        dailyTicketQuantities[currentDate.format("YYYY-MM-DD")] = dailyQuantity;
        currentDate.add(1, 'days');
      }
    }

    console.log("TICKET : REQ>BODY ", req.body,"_____________________END_________________")
    
    // Set the `ticket_avability` to the total quantity across the event as usual
    const NewTicket = new TicketModel({
      ticket_Name: req.body.ticket_Name,
      ticket_type: req.body.ticket_type,
      total_Quantity: per_day ? total_Quantity * daysCount : total_Quantity,
      ticket_avability: per_day ? total_Quantity * daysCount : total_Quantity,
      ticket_price: req.body.ticket_price,
      basic_price: req.body.ticket_price * 0.06,
      sale_start: req.body.sale_start,
      sale_start_Time: req.body.sale_start_Time,
      sale_end: req.body.sale_end,
      sale_end_Time: req.body.sale_end_Time,
      min_booking: req.body.min_booking,
      max_booking: req.body.max_booking,
      ticket_description: req.body.ticket_description,
      event_id: req.body.event_id,
      daily_ticket_quantities: dailyTicketQuantities, // Set daily quantities if per_day is true
      available_daily_ticket_quantities: dailyTicketQuantities, // Set daily quantities if per_day is true
      isTicketForPerDay: per_day,
      cover_price: req.body.cover_price,
      isTicket_for_date: req.body.isTicket_for_date,
      ticket_for_Date : req.body.ticket_for,
      ticket_for_city : req.body.ticket_for_city,
      event_show_time : req.body.event_show_time,
      ticket_for_city_venue : req.body.ticket_for_city_venue,
    });

    await NewTicket.save()
      .then(ticketCreated => {
        res.status(200).send({ message: "Ticket Created successfully", ticketCreated });
      })
      .catch(error => {
        res.status(400).send({ message: "Unable to Create Ticket", error });
      });
  }
  catch (error) {
    res.status(500).send({ message: "Something went Wrong, try again later", error: error.message });
  }
};


const UpdateTicketDetails = async (req, res) => {
  const { ticket_id } = req.params;
  try {
    const ticketUpdated = await TicketModel.findByIdAndUpdate(ticket_id, {
      ticket_Name: req.body.ticket_Name,
      ticket_type: req.body.ticket_type,
      $inc: {
        total_Quantity: +req.body.addmoreTicket,
        ticket_avability: +req.body.addmoreTicket,  // Fixed the incorrect curly braces
      },
      ticket_price: req.body.ticket_price,
      basic_price: req.body.ticket_price * 0.06,
      sale_start: req.body.sale_start,
      sale_start_Time: req.body.sale_start_Time,
      sale_end: req.body.sale_end,
      sale_end_Time: req.body.sale_end_Time,
      min_booking: req.body.min_booking,
      max_booking: req.body.max_booking,
      ticket_description: req.body.ticket_description,
      cover_price: req.body.cover_price,  
      // daily_ticket_quantities: dailyTicketQuantities, // Set daily quantities if per_day is true
      // available_daily_ticket_quantities: dailyTicketQuantities, // Set daily quantities if per_day is true
      // isTicketForPerDay: per_day,
      isTicket_for_date: req.body.isTicket_for_date,
      ticket_for_Date : req.body.ticket_for,
      ticket_for_city : req.body.ticket_for_city,
      event_show_time : req.body.event_show_time,
      ticket_for_city_venue : req.body.ticket_for_city_venue,
      showTicketSoldOut :req.body.showTicketSoldOut
    }, { new: true });

    if (ticketUpdated) {
      res.status(200).send({ message: "Ticket Updated successfully", ticketUpdated });
    } else {
      res.status(201).send({ message: "Unable to Update Changes" });
    }
  } catch (error) {
    res.status(500).send({ message: "Something went wrong, try again after some time", error });
  }
};

const UpdateTicketSoldOut = async (req,res)=>{
  try {
    const UpdateSoldOutValue = await TicketModel.findByIdAndUpdate(req.params.ticket_id,{
      showTicketSoldOut : req.body.showTicketSoldOut
    },
  {new : true}
  );
  res.status(200).json({message : "Updated Sucessfully!"})
  } catch (error) {
    res.status(500).json({message: "Soomethin went wrong!"})
  }
}


// Fetch all the tickets from DB
const FetchTicketByTicket_id = async (req, res) => {
  try {
    const ticket = await TicketModel.findOne({ _id: req.params.ticket_id })
    if (ticket) {
      res.status(200).send({ ticket })
    } else {
      res.status(204).send({ message: "No tickets found" })
    }
  } catch (error) {
    res.status(500).send({ message: "Unable to Fetch Tickets", error })
  }
};

// Delete ticket endpoint
const DeleteTicket = async (req, res) => {
  try {
    const result = await TicketModel.deleteOne({ _id: req.params.id });
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

const FetchTickets = async (req, res) => {
  try {
    const all_tickets = await TicketModel.find({ event_id: req.params.id })
    if (all_tickets) {
      res.status(200).send({ all_tickets })
    } else {
      res.status(204).send({ message: "No tickets found" })
    }
  } catch (error) {
    res.status(500).send({ message: "Unable to Fetch Tickets", error })
  }
};


const SetTicketBookingCount = async (req, res) => {
  const { bookingQuantities, date_for_booked_event } = req.body; // Incoming quantities for each ticket
  try {
    // Step 1: Reset all tickets' bookingQuantities to 0
    await TicketModel.updateMany({}, { $set: { bookingQuantities: 0 } });

    // Step 2: Loop through the incoming bookingQuantities and update them
    for (const [ticketId, quantity] of Object.entries(bookingQuantities)) {

      const updatedResponse = await TicketModel.findByIdAndUpdate(
        { _id: ticketId },
        {
          bookingQuantities: quantity,
          reserveDate: date_for_booked_event
        },
        { new: true } // This will return the updated document
      );

    }

    res.status(200).json({ message: 'Tickets booked successfully' });
  } catch (error) {
    res.status(500).send({ message: 'Failed to book tickets' });
  }
};


const FetchUserTickets = async (req, res) => {
  try {
    const userId = req.params.userId;

    // Find all bookings for the user and populate the 'tickets' field
    const bookings = await BookingModel.find({ user: userId })
      .populate('tickets') // Populate tickets field to get detailed ticket data
      .exec();

    if (!bookings || bookings.length === 0) {
      return res.status(404).json({ message: 'No tickets booked' });
    }

    // Extract tickets from the bookings
    const tickets = bookings.reduce((acc, booking) => {
      return acc.concat(booking.tickets);
    }, []);

    res.status(200).json({ tickets });
  } catch (error) {
    console.error('Error fetching tickets:', error);
   return res.status(500).json({ message: 'Server error', error });                              
  }
};

const StoreFreeTickets = async (req, res) => {
  const order_id = crypto.randomBytes(6).toString('hex').toUpperCase();
  console.log("FREETICKET : ", req.body)
  try {
    const freeTicketBooking = new BookingModel(
      {
        user: req.body.user_id,
        order_id: order_id,
        tickets: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)), // Convert IDs to ObjectId
        eventDetails: req.body.eventDetails,
        amount: req.body.amount,
        receipt: "ticket",
        payment_id: null,
        paid_Amount: 0,
        isPaid : true,
        isfree : true,
        status: "Booked",
        bookedTicketQuantities: req.body.quantity,
        available_entry: req.body.quantity,
        eventreserveDate: req.body.eventreserveDate,
        reserveeventcity : req.body.reserveCity
      }
    );

    await freeTicketBooking.save();
    const ticket = await TicketModel.findOne({ _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) });
    const updatedTicket = await TicketModel.findOneAndUpdate(
      { _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) },
      {
        $inc: { ticket_avability: - req.body.quantity } // Decrement availability
      },
      { new: true }
    );
    if (ticket.isTicketForPerDay) {
      const updatedavailable_daily_ticket_quantities = await TicketModel.findOneAndUpdate(
        { _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) },
          {
              $inc: { [`available_daily_ticket_quantities.${req.body.eventreserveDate}`]: -req.body.quantity } // Decrement availability
          },
          { new: true }
      );
  }

    res.status(200).json({ message: "Ticket save successful!", freeTicketBooking });
  } catch (e) {
    return res.status(500).json({ message: "Ticket save failed!", error: e.message });
  }
}

const Findusermaxticket = async (req, res)=>{
  try {
    const {user_id, ticket_id, event_id}= req.params;
    const data = await BookingModel.find(
      {
        user: user_id,
        tickets: ticket_id,
        eventDetails: event_id,
        $or: [
          { amount: "0" },
          { $and: [{ amount: { $gt: "0" } }, { isPaid: true }] }
        ]
      },
      { bookedTicketQuantities: 1, _id: 0 } // Only include `bookedTicketQuantities`, exclude `_id`
    );

    if(data){
      return res.status(200).json({messager:"data", data});
    }else{
      return res.status(201).json({messager:"data not found"});
    }
  } catch (error) {
     return res.status(500).json({message : "Found Some Error!", error:error.message});
  }
}

const FetchSingleBookedEventByBookingId = async (req, res) => {
  const { order_id } = req.params;
  try {
    const bookedEvents = await BookingModel.findOne({ order_id: order_id }).populate('tickets').populate('eventDetails');
    if (bookedEvents) {
      const username = await User.findById(bookedEvents.user)
      res.status(200).json({ message: "Booked Events", data: bookedEvents, username: username.firstName + " " + username.lastName })
    } else {
      res.status(404).json({ message: "No Ticket Booked" })
    }
  }
  catch (err) {
    console.error("Error fetching booked events:", err); // Log error details
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
}
    
module.exports = {
  CreateNewTicket, FetchTickets, UpdateTicketSoldOut,
  DeleteTicket, SetTicketBookingCount, FetchUserTickets,
  StoreFreeTickets, FetchTicketByTicket_id, UpdateTicketDetails,
  Findusermaxticket, FetchSingleBookedEventByBookingId
}; 