import React from 'react'
import './CropNavbarComponent.css'
import crop_logo from '../../Assets/corpLogo.png';
import { TbTransactionRupee } from "react-icons/tb";
import { BsList } from 'react-icons/bs';
import { TbArrowLeftFromArc } from "react-icons/tb";
import { Link} from "react-router-dom";
import { PiGridFour } from "react-icons/pi";
import { VscChecklist } from "react-icons/vsc";
import { AiOutlineDashboard } from "react-icons/ai";
import { BsQrCodeScan } from "react-icons/bs";
import { IoIosAddCircleOutline } from "react-icons/io";
import { CiBellOn } from 'react-icons/ci';

function CropNavbarComponent() {
    return (
        <>
            <div className='nav d-flex justify-content-between align-items-center' id="largesScreenDevice">
                <div className='px-2' style={{width: "181px", height:"50px"}}>
                   <Link to="/admin/dashboard">
                   <img className='w-100 h-100' src={crop_logo}/>
                   </Link>
                </div>
            </div>

            {/* Navbar for smallScreenDevice */}
            <div className='nav' id='SmallScreenDevice'>
                <div className='navbar'>
                    <div className="dropdown togglebtn">
                        <BsList className='dropdown-toggle fs-1 ms-2 fw-bold'
                            data-bs-toggle="dropdown"
                            aria-expanded="false" aria-controls="responsiveNavbar"
                        />
                        <ul className="dropdown-menu collapseNavbar" style={{ height: "100vh", width: "30vh", marginTop: "0.7rem" }}>
                            <li>
                                <div className='sidenavcontent'>
                                    <ul>
                                        <li >
                                            <Link className='link-dark link-underline-opacity-0 fw-semibold' to='/corporate/:company_name/dashbord'>
                                                <AiOutlineDashboard className='sidebar-icon' />
                                                <span className='sidebar-tab'>Dashboard</span>
                                            </Link>
                                        </li>
                                        <li className='nav-content' style={{ marginTop: "23px" }}>
                                            <Link className='link-dark link-underline-opacity-0 fw-semibold' to='#'>
                                                <PiGridFour className='sidebar-icon' />
                                                <span className='sidebar-tab'>Your Events</span>
                                            </Link>
                                        </li>
                                        <li className='nav-content' style={{ marginTop: "23px" }}>
                                            <Link className='link-dark link-underline-opacity-0 fw-semibold' to='#'>
                                                <BsQrCodeScan className='sidebar-icon' />
                                                <span className='sidebar-tab'>
                                                    Ticket Scanner
                                                </span>
                                            </Link>
                                        </li>
                                        <li className='nav-content' style={{ marginTop: "23px" }}>
                                            <Link className='link-dark link-underline-opacity-0 fw-semibold' to='#'>
                                                <IoIosAddCircleOutline className='sidebar-icon' />
                                                <span className='sidebar-tab'>Create Event</span>
                                            </Link>
                                        </li>
                                        <li className='nav-content' style={{ marginTop: "23px" }}>
                                            <Link className='link-dark link-underline-opacity-0 fw-semibold' to='#'>
                                                <TbArrowLeftFromArc className='sidebar-icon' />
                                                <span className='sidebar-tab'>Logout</span>
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div className='searchbtn'>
                        <CiBellOn className="searchIcon" size={30} />
                    </div>
                </div>
            </div>
        </>
    )
}

export default CropNavbarComponent
