import React, { useEffect, useReducer, useState } from "react";
import "./YourBookedTicket.css";
import Ticket from "../../Assets/ticket.png";
import ticket_Image_Templet from "../../Assets/TicketTemplate.png";
import UserSideBar from "../Components/userSideBar";
import NavBar from "../Components/NavBar";
import LocationModal from "../Components/LocationModal";
import { QRCodeSVG } from "qrcode.react";
import { API_URL } from "../../config";
import axios from "axios";
import { Loader } from "react-feather";
import { Link, useNavigate, useParams } from "react-router-dom";
import { IoIosArrowBack } from "react-icons/io";
import PuffLoader from "react-spinners/PuffLoader";
import { TickerCard } from "./TicketCard";

// Initial state for the reducer
const initialState = {
  bookedevent: [],
};

// Reducer function to manage ticket and event detail state
const reducer = (state, action) => {
  switch (action.type) {
    case "SET_RESPONSE_DATA":
      return {
        ...state,
        bookedevent: action.payload.bookedevent,
      };
    default:
      return state;
  }
};

const YourBookedTicket = () => {
  const { booking_id } = useParams();
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const [showAllTickets, setShowAllTickets] = useState(true);
  const [loading, setLoading] = useState(true);

  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    localStorage.setItem("userLocation", location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  // geting all booked tickets
  const user = JSON.parse(localStorage.getItem("userData"));
  const bookedTickets = async () => {
    const user_id = user._id;
    try {
      const ticketResponse = await axios.get(
        `${API_URL}/pay/ticket/${user_id}`
      );

      if (ticketResponse.status === 200 && ticketResponse.data.data) {
        const ticketData = [];
        ticketResponse.data.data.forEach((booking) => {
          booking.tickets.forEach((ticket) => {
            const _b = booking;
            delete _b.tickets;
            ticketData.push({
              ..._b,
              ...ticket,
            });
          });
        });

        console.log("ticketData", ticketData);

        dispatch({
          type: "SET_RESPONSE_DATA",
          payload: {
            bookedevent: ticketData,
          },
        });
      }
    } catch (error) {
      alert("No data available!");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
  };

  const formatTimespanDate = (timestamp) => {
    const date = new Date(timestamp);

    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    };

    return date.toLocaleString("en-US", options);
  };

  useEffect(() => {
    bookedTickets();
  }, []);

  return (
    <>
      <div className="booked-ticket-page">
        {/* Show LocationModal */}
        {showLocationModal && (
          <LocationModal
            onClose={handleCloseLocationModal}
            onLocationChange={handleLocationChange}
          />
        )}
        <NavBar
          userLocation={userLocation}
          onNext={() => setShowLocationModal(true)}
        />
        {loading ? (
          <div
            className="d-flex justify-content-center align-items-center"
            style={{ height: "100vh" }}
          >
            <PuffLoader size={28} loading={loading} />
          </div>
        ) : (
          <>
            <div
              className="d-flex flex-row justify-content-start align-items-stretch w-100 h-100 flex-1"
              style={{ flex: 1, flexGrow: 1 }}
            >
              <UserSideBar />
              <div
                className="pb-5 col-lg-10 col-md-10 col-sm-12 col-12 flex-1 d-flex flex-column h-navbar justify-content-start align-items-center"
                style={{
                  flex: 1,
                  flexGrow: 1,
                  overflowY: "auto",
                }}
              >
                <div className="booked-tickets-heading  w-100">
                  <span>Your Front Seats</span>
                </div>
                <div
                  className="flex-1 d-flex flex-column w-100"
                  style={{ flex: 1, maxWidth: 1280 }}
                >
                  <div className="orderedTicket p-3 gap-3 d-flex flex-column justify-content-start align-items-stretch">
                    {!state.bookedevent ? (
                      <Loader />
                    ) : Array.isArray(state.bookedevent) &&
                      state.bookedevent.length > 0 ? (
                      state.bookedevent.map((ticket, index) => {
                        return (
                          <div key={`${ticket.order_id}-${ticket._id}`}>
                            <TickerCard ticket={ticket} />
                          </div>
                        );
                      })
                    ) : (
                      <div>No tickets available</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default YourBookedTicket;
