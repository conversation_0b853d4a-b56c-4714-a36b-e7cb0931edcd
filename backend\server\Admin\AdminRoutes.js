const express = require('express');
const { 
    AdminSignUp,
    AdminLogin,
    AdminDashBoardMatrices,
    AdminAllEventsList,
    GetVendorData,
    GetAllTicketBuyers,
    GetAllUsersData,
    GetAllVendors,
    GetVendorsEvent,
    GetVendorsDetails,
    GetEnteredGuestOfEvent,
    UpdateBasicPrice,
    EventPayoutsData,
    GetEvent,
    IndividualEventRevenue,
    CustonerQueries,
    SuccessVendorPayment,
    VendorPaymentDetail,
    GetVendorPayment,
    SaveCarousel_Image,
    UpdateCarousel_Image,
    GetCarousel_Image,
    DeleteCarousel_Image,
    UpdateCampaingsData,
    SendWebNotification,
    Save_Fcm_Token,
    GetAllCategory,
    Deletecategory,
    GetCitiesName,
    AddNewCity,
    DeleteCity,
    Coupon_Validation,
    Coupon_Creating,
    GetCoupons,
    DeleteCoupon,
    GetAllSentNotification
} = require('./AdminContoller');
const { AddNewEventCategory } = require('../controllers/eventController');
const adminRouter = express.Router();

adminRouter.post('/signup', AdminSignUp);
adminRouter.post('/login', AdminLogin);
adminRouter.get('/dashboard', AdminDashBoardMatrices);
adminRouter.get('/allevents', AdminAllEventsList);
adminRouter.get('/getVendor/:vendor_id', GetVendorData);
adminRouter.get('/ticketbuyers', GetAllTicketBuyers);
adminRouter.get('/all-users', GetAllUsersData);
adminRouter.get('/all-vendor', GetAllVendors);
adminRouter.get('/vendors-events/:vendor_id', GetVendorsEvent);
adminRouter.get('/vendors-detail/:vendorId', GetVendorsDetails);
adminRouter.get('/guest-entered/:event_id', GetEnteredGuestOfEvent);
adminRouter.put('/update-basicprice/:event_id', UpdateBasicPrice);
adminRouter.get('/events/:event_id', GetEvent);
adminRouter.get('/payouts', EventPayoutsData);
adminRouter.get('/event-revenue/:event_id', IndividualEventRevenue);
adminRouter.get('/queries', CustonerQueries);
adminRouter.post('/vendorpaymentdone', SuccessVendorPayment);
adminRouter.get('/payouts-status/:eventId', VendorPaymentDetail);
adminRouter.get('/vendorpaymentdone/:vendorId/:eventId', GetVendorPayment);
adminRouter.post('/carousel_img', SaveCarousel_Image);
adminRouter.post('/category_img', AddNewEventCategory);
adminRouter.post('/newCity', AddNewCity);
adminRouter.put('/carousel_img/:image_id', UpdateCarousel_Image);
adminRouter.get('/category', GetAllCategory);
adminRouter.get('/cities', GetCitiesName);
adminRouter.get('/carousel_img', GetCarousel_Image);
adminRouter.delete('/delete-carousel/:id', DeleteCarousel_Image);
adminRouter.delete('/delete-category/:id', Deletecategory);
adminRouter.delete('/delete-city/:id', DeleteCity);
adminRouter.put('/updatecampaign', UpdateCampaingsData);
adminRouter.post('/send-notification', SendWebNotification);
adminRouter.put('/save-fcm-token', Save_Fcm_Token);
adminRouter.put('/coupon-validation', Coupon_Validation);
adminRouter.post('/newCoupon', Coupon_Creating);
adminRouter.get('/getcoupons', GetCoupons);
adminRouter.delete('/delete-Coupon/:id', DeleteCoupon);
adminRouter.get('/all-sent-notification', GetAllSentNotification);




module.exports = adminRouter;