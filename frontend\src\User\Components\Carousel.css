.carousel-main {
    max-width: 1280px;
}

.innercarouselDiv,
.carouselimgDiv {
    width: 100%;
    border-radius: 1.5rem;
    overflow: hidden;
}

.carousel-indicators button {
    width: 10px !important; /* Adjust size */
    height: 10px !important;
    border-radius: 50%; /* Makes the indicators circular */
    background-color: rgba(255, 255, 255, 0.5); /* Initial color */
    border: none;
    transition: background-color 0.3s ease; /* Smooth color change */
  }
  
  .carousel-indicators .active {
    background-color: rgba(255, 255, 255, 0.9) !important; /* Active indicator color */
  }
  
  .carousel-indicators button:hover {
    background-color: rgba(255, 255, 255, 0.7); /* Hover color */
  }

@media screen and (max-width: 630px) {

    .innercarouselDiv,.carouselimgDiv {
        width: 100% !important;
        height: 180px;
        border-radius: 1.5rem;
        overflow: hidden;
    }
    .carousel-img {
        height: 180px !important;
        object-fit: cover;
    }

}