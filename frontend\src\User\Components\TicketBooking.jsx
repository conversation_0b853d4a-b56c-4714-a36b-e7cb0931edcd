import React, { useEffect, useState } from "react";
import "./TicketBooking.css";
import successTick from "../../Assets/successTick.png";
import My_Front_Seat_Logo from "../../Assets/My Front Seat Logo.png";
import { useLocation, useNavigate } from "react-router-dom";
import { API_URL } from "../../config";
import { SlArrowDown } from "react-icons/sl";
import { BsArrowLeft } from "react-icons/bs";
import axios from "axios";
import ReduceText from "../Interface";
import LoginModal from "./LoginModal";
import SyncLoader from "react-spinners/SyncLoader";
import { PiSealCheckFill } from "react-icons/pi";
import "./SelectTickets.css";

function TicketBooking({ ticket, eventDetail, ticketids, onLogin }) {
  // const [selectedOption, setSelectedOption] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isCouponError, setIsCouponError] = useState(false);
  const [CouponError, setCouponError] = useState("");
  const user = JSON.parse(localStorage.getItem("userData"));
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [isLoadCoupon, setIsLoadCoupon] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [isCouponValidate, setIsCouponValidate] = useState(false);
  const [payable_amount, setPayable_amount] = useState(0);
  const [givendiscount, setGivendiscount] = useState(0);
  const location = useLocation();
  let [billing, SetBilling] = useState({
    ticketQuantity: 0,
    subTotal: 0,
    bookingFee: 0,
    gst: 0,
    basic_price: 0,
    total_amount: 0,
  });

  // const handleCheckboxChange = (option) => {
  //   setSelectedOption(option);
  // };

  // const toggleDropdown = () => {
  //   setDropdownOpen(!dropdownOpen);
  // };

  const formatTime = (timeString) => {
    if (timeString == null || timeString === undefined || timeString === "") {
      timeString = "00:00:00";
    }

    const dateObj = new Date(`1970-01-01T${timeString}Z`);
    let hours = dateObj.getUTCHours();
    const minutes = String(dateObj.getUTCMinutes()).padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12 || 12;

    const formattedTime = `${hours}:${minutes} ${ampm}`;
    return formattedTime;
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()]; // Get month name
    const year = String(dateObj.getFullYear()).slice(-2); // Get last two digits of the year
    return `${day} ${month} '${year}`;
  };

  const handleBilling = () => {
    let count = 0;
    let amount = 0;
    let basic_rate = 1;

    count = ticket.bookingQuantities;
    basic_rate = basic_rate * ticket.basic_price * ticket.bookingQuantities;
    amount = ticket.bookingQuantities * ticket.ticket_price;

    console.log("::::AMOUNT::::", amount);
    // for (let i = 0; i < ticket.length; i++) {
    //   count = count + ticket[i].bookingQuantities;
    //   basic_rate = basic_rate + ticket[i].basic_price;
    //   amount = amount + (ticket[i].bookingQuantities * ticket[i].ticket_price);
    // }
    const gst9 = (Math.ceil(basic_rate * 0.09 * 100) / 100).toFixed(2);
    const fee = Number(basic_rate) + 2 * Number(gst9);
    const totalAmount = amount + fee;

    SetBilling({
      ticketQuantity: count,
      subTotal: amount,
      bookingFee: fee,
      basic_price: basic_rate,
      gst: Number(gst9),
      // total_amount: Number(totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })),
      total_amount: Number(totalAmount),
    });
  };

  useEffect(() => {
    console.log("TICKET: ", ticket, billing);
    if (ticket) {
      handleBilling();
    }
  }, [ticket, eventDetail]);

  //  Payment Process
  const [orderId, setOrderId] = useState("");

  const LoadScript = (src) => {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };
  const CreateOrder = async (_user) => {
    // apply check here to  validate ticket avaibility
    console.log("ticketids : ", ticketids, _user);
    const amt = isCouponValidate
      ? payable_amount * 100
      : billing.total_amount * 100;
    let data = JSON.stringify({
      amount: amt,
      currency: "INR",
      user_id: _user._id,
      ticketDetails: ticketids,
      eventDetails: eventDetail,
      quantity: billing.ticketQuantity,
      eventreserveDate: ticket.reserveDate,
      reserveCity: location.state.selectedCity
        ? location.state.selectedCity
        : eventDetail.event_city,
    });

    let config = {
      method: "POST",
      maxBodyLength: Infinity,
      url: `${API_URL}/pay/payment-order`,
      headers: {
        "Content-Type": "application/json",
      },
      data: data,
    };
    // api call
    const res = await axios.request(config);
    handleRazorpayScreen(res.data.amount, res.data.order_id);
  };

  const handleRazorpayScreen = async (amount, order_id) => {
    const res = await LoadScript(
      "https://checkout.razorpay.com/v1/checkout.js"
    );
    if (!res) {
      alert("Some error at razorpay");
      return;
    }
    const options = {
      key: "rzp_live_QQmdUNR3XVKX87",
      amount: amount,
      currency: "INR",
      name: "My Front Seat",
      description: "Payment to My-Front-Seat",
      order_id: order_id,
      image: My_Front_Seat_Logo,
      handler: function (response) {
        setOrderId(order_id);
        CheckPayment(order_id);
      },
      prefill: {
        name: "My Front Seat",
        email: "<EMAIL>",
      },
      theme: {
        color: "#04092C",
      },
    };
    const PaymentObject = new window.Razorpay(options);
    PaymentObject.open();
  };

  const CheckPayment = async (order_id) => {
    const response = await axios.get(
      `${API_URL}/pay/payment/status/${order_id}`
    );
    if (response.data && response.status === 200) {
      alert("Payment successful!");
      axios.post(`${API_URL}/ticket/sendmail`, {
        orderId: order_id,
        email: user.email,
        event_name: eventDetail.event_name,
        userName: user.firstName + user.lastName,
        ticket_link: `https://www.myfrontseat.in/yourtickets/${order_id}`,
        vendor_id: eventDetail.eventCreatedBy,
        ticketName: ticket.ticket_Name,
        bookingfee: billing.bookingFee.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        basicRate: ticket.basic_price.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        gst: billing.gst.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        totalBasicPrice: billing.basic_price.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        cover_image: eventDetail.cover_image,
        phone_no: user.phone,
        ticket_Price: billing.subTotal.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        event_date: formatDate(eventDetail.event_starts_date),
        event_city: eventDetail.event_city,
        event_time: formatTime(eventDetail.event_starts_Time),
        event_venue: eventDetail.event_venue
          ? eventDetail.event_venue + "," + eventDetail.event_city
          : eventDetail.event_city,
      });
      navigate(`/ticketbooked/${eventDetail.event_name.replace(/\s+/g, "_")}`);
    } else {
      alert("Unable to process your payment");
    }
  };

  const [showLoginModal, setShowLoginModal] = useState(false);

  const handleClose = () => setShowLoginModal(false);
  const handleShow = () => setShowLoginModal(true);

  const handleProceedToPay = async () => {
    setLoading(true); // Show loader
    if (localStorage.getItem("userData")) {
      const user = JSON.parse(localStorage.getItem("userData"));
      console.log("USER: ", user);
      await CreateOrder(user);
    } else {
      handleShow();
    }
    setLoading(false); // Hide loader after process completes
  };

  // function to store non-payable tickets (Free Tickets)
  const handleSaveData = async (_user) => {
    const body = {
      amount: billing.total_amount,
      user_id: _user ? _user._id || _user.uid : user._id || user.uid,
      ticketDetails: ticketids,
      eventDetails: eventDetail,
      quantity: billing.ticketQuantity,
      eventreserveDate: ticket.reserveDate,
      reserveCity: location.state.selectedCity
        ? location.state.selectedCity
        : eventDetail.event_city,
    };

    const saveTicketResponse = await axios.post(
      `${API_URL}/ticket/bookfreetickets`,
      body
    );
    if (saveTicketResponse.status === 200) {
      axios.post(`${API_URL}/ticket/sendmail`, {
        orderId: saveTicketResponse.data.freeTicketBooking.order_id,
        email: _user ? _user.email : user.email,
        event_name: eventDetail.event_name,
        userName: _user
          ? _user.firstName + _user.lastName
          : user.firstName + user.lastName,
        ticket_link: `https://www.myfrontseat.in/yourtickets/${saveTicketResponse.data.freeTicketBooking.order_id}`,
        vendor_id: eventDetail.eventCreatedBy,
        ticketName: ticket.ticket_Name,
        bookingfee: billing.bookingFee,
        basicRate: ticket.basic_price,
        gst: billing.gst,
        totalBasicPrice: billing.basic_price,
        cover_image: eventDetail.cover_image,
        phone_no: _user ? _user.phone : user.phone,
        ticket_Price: billing.subTotal.toLocaleString("en-IN", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        event_date: formatDate(eventDetail.event_starts_date),
        event_city: eventDetail.event_city,
        event_time: formatTime(eventDetail.event_starts_Time),
        event_venue: eventDetail.event_venue
          ? eventDetail.event_venue + "," + eventDetail.event_city
          : eventDetail.event_city,
      });
    } else {
      alert("Failed to book tickets");
    }
  };
  const handleGetTicket = () => {
    setLoading(true);
    if (localStorage.getItem("userData")) {
      handleSaveData();
      navigate(`/ticketbooked/${eventDetail.event_name.replace(/\s+/g, "_")}`);
    } else {
      handleShow();
    }
    setLoading(false);
  };

  const handleLoginSuccess = async (user) => {
    onLogin(user);
    setShowLoginModal(false);
    if (ticket.ticket_type === "free") {
      setLoading(true);
      await handleSaveData(user);
      setLoading(false);
      navigate(`/ticketbooked/${eventDetail.event_name.replace(/\s+/g, "_")}`);
    } else {
      setLoading(true);
      await CreateOrder(user);
      setLoading(false);
    }
    // setIsLoggedIn(true); // Update the login state
  };

  const handleApplyCoupon = async () => {
    setIsLoadCoupon(true);
    const body = {
      couponCode: couponCode,
      event_id: eventDetail.event_id,
      totalBillAmount: Number(billing.total_amount),
    };
    try {
      const couponResponse = await axios.put(
        `${API_URL}/admin/coupon-validation`,
        body
      );
      if (couponResponse.status === 200) {
        console.log(couponResponse.data);
        setIsCouponValidate(true);
        const coupon_discount = couponResponse.data.discount;
        const discountAmt = couponResponse.data.discountedAmount;
        const modalElement = document.getElementById("couponAppliedModal");
        if (modalElement) {
          const modal = new window.bootstrap.Modal(modalElement);
          modal.show();
          setTimeout(() => {
            if (modalElement.classList.contains("show")) {
              // Ensure it's still open
              modal.hide();
            }
          }, 2000);
        }
        setPayable_amount(discountAmt);
        setGivendiscount(coupon_discount);
        setIsLoadCoupon(false);
      } else {
        setIsCouponError(true);
        setCouponError("Coupon expired");
        setIsLoadCoupon(false);
      }
    } catch (error) {
      setIsCouponError(true);
      setCouponError("Invalid Code");
      setIsLoadCoupon(false);
    }
  };

  const handleRemoveCoupon = () => {
    setIsCouponValidate(false);
    setCouponCode("");
    setPayable_amount(0);
    setGivendiscount(0);
  };

  useEffect(() => {
    // Scroll to top when the component loads or data is done loading
    window.scrollTo({
      top: 0,
      behavior: "smooth", // Adds smooth scrolling
    });

    console.log("BOOKING TICKET : ", ticket);
  }, []);

  return (
    <>
      {/* Show LocationModal */}
      {showLoginModal && (
        <LoginModal
          showModal={showLoginModal}
          onLoginSuccess={handleLoginSuccess}
        />
      )}

      <div className="bookingticketmainDiv" id="largeScreenViewPort">
        <div className="bookingticket">
          <div
            className="d-flex flex-row justify-content-between align-items-center m-0"
            id="largedeviceview"
          >
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center">
              <span
                className="backarrowlink h-100 link-dark "
                onClick={() => navigate(-1)}
              >
                <BsArrowLeft size={30} />
              </span>
            </div>
            <div className="col-10 col-10">
              <div className="headingdiv">
                <span className="heading">{eventDetail.event_name}</span>
                <span className="subheading">
                  {eventDetail.event_venue} {eventDetail.event_city} |{" "}
                  {formatDate(ticket.reserveDate)
                    ? formatDate(ticket.reserveDate)
                    : formatDate(eventDetail.event_starts_date)}{" "}
                  | {formatTime(eventDetail.event_starts_Time)}
                </span>
              </div>
            </div>
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center"></div>
          </div>
          <div className="row">
            <p className="bookinghead">Booking Summary</p>
            <div className="col-lg-6 col-md-6 col-sm-6">
              <div className="eventimg">
                <img
                  className="booking-img rounded-4 w-100 h-100"
                  src={`${eventDetail.cover_image}`}
                  alt="booking"
                />
                <span className="ticketname">
                  {/* {
                    ticket && ticket.length > 0 ? (
                      ticket.map((tic, index) => (
                        <span key={tic._id}>
                          {
                            tic.bookingQuantities > 0 &&
                            (
                              <>
                                {index > 0 && ","} {tic.ticket_Name}
                              </>
                            )
                          }
                        </span>
                      ))
                    ) : (
                      ""
                    )
                  } */}
                  <span>{ticket.ticket_Name}</span>
                </span>
                <br />
                <span className="quantity">
                  {billing.ticketQuantity} Tickets
                </span>
              </div>
            </div>
            <div className="col-lg-6 col-md-6 col-sm-6">
              <div className="bookingbifargation rounded-4 border-0 shadow">
                <div className="d-flex justify-content-between">
                  <span className="summaryValue">Sub-total</span>
                  <span className="summaryValue">
                    ₹{" "}
                    {billing.subTotal.toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div>
                  <div
                    className="d-flex justify-content-between border-bottom border-secondary-subtle pb-1"
                    data-bs-toggle="collapse"
                    data-bs-target="#collapseExample"
                    aria-expanded="false"
                    aria-controls="collapseExample"
                  >
                    <span className="summaryValue">
                      Convenience Fee <SlArrowDown className="arrowdown ms-3" />
                    </span>
                    <span className="summaryValue">
                      ₹{" "}
                      {billing.bookingFee.toLocaleString("en-IN", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </span>
                  </div>
                  {/* Collapse div */}
                  <div
                    className="collapse w-100 border-bottom border-secondary-subtle mt-1"
                    id="collapseExample"
                  >
                    <div className="d-flex justify-content-between mb-1">
                      <span className="summarybiValue">Base Price</span>
                      <span className="summarybiValue">
                        ₹{" "}
                        {Number(billing.basic_price).toLocaleString("en-IN", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between mb-1">
                      <span className="summarybiValue">
                        Central GST(CGST) @9%
                      </span>
                      <span className="summarybiValue">
                        ₹{" "}
                        {Number(billing.gst).toLocaleString("en-IN", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span className="summarybiValue">
                        State GST(SGST) @9%
                      </span>
                      <span className="summarybiValue">
                        ₹{" "}
                        {Number(billing.gst).toLocaleString("en-IN", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
                {!isCouponValidate ? (
                  <div className="mt-2 d-flex align-items-end justify-content-between ">
                    <span className="summaryValue fw-bold">Total Amount</span>
                    <span className="summaryValue fw-bold">
                      ₹{" "}
                      {Number(billing.total_amount).toLocaleString("en-IN", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </span>
                  </div>
                ) : (
                  <div>
                    <div className="d-flex justify-content-between border-bottom border-secondary-subtle pb-1">
                      <span className="summarybiValue">Coupon discount</span>
                      <span className="summarybiValue">
                        ₹{" "}
                        {givendiscount.toLocaleString("en-IN", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    <div className="mt-2 d-flex align-items-end justify-content-between ">
                      <span className="summaryValue fw-bold">Total Amount</span>
                      <span className="summaryValue fw-bold">
                        ₹{" "}
                        {Number(payable_amount).toLocaleString("en-IN", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="row mt-3">
            <div className="col-6 d-flex flex-column"></div>
            <div className="col-6">
              {isCouponValidate ? (
                <div className="card">
                  <div className="card-body">
                    <div className="couponCode">Coupon Code</div>
                    <div className="d-flex justify-content-between text-center">
                      <span className="couponCode-2">
                        You saved ₹ {givendiscount} with '{couponCode}'
                      </span>{" "}
                      <span
                        className="couponCode removeCouponCode"
                        onClick={handleRemoveCoupon}
                      >
                        Remove
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="have-discount">Have a discount code?</div>
                  <div className="d-flex">
                    <input
                      type="text"
                      className="code w-100 rounded-start-3"
                      placeholder="Enter code"
                      onChange={(e) => {
                        setCouponCode(e.target.value);
                        setCouponError("");
                        setIsCouponError(false);
                      }}
                    />
                    <div
                      className="apply rounded-end-3"
                      onClick={handleApplyCoupon}
                      disabled={isLoadCoupon}
                    >
                      {isLoadCoupon ? (
                        <SyncLoader
                          animation="border"
                          color="#FFFF"
                          size="7"
                          speedMultiplier={1}
                          margin={4}
                        />
                      ) : (
                        "Apply"
                      )}
                    </div>
                  </div>
                  {isCouponError ? (
                    <span className="have-discount text-danger fw-semibold">
                      * {CouponError}
                    </span>
                  ) : (
                    ""
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="footerPart proceedpay-div">
          {ticket.ticket_type === "free" ? (
            <button
              className="proceedpay-btn"
              onClick={handleGetTicket}
              disabled={loading}
            >
              {loading ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Get your Ticket"
              )}
            </button>
          ) : (
            <button
              className="proceedpay-btn"
              onClick={handleProceedToPay}
              disabled={loading}
            >
              {loading ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed to Pay"
              )}
            </button>
          )}
        </div>
      </div>

      <div
        id="smallScreenViewPort"
        style={{ position: "relative", height: "100vh", overflowY: "hidden" }}
      >
        <div className="d-flex flex-row justify-content-center align-items-center shadow-sm py-3">
          <div className="col-2 d-flex justify-content-center align-items-center">
            <span className=" link-dark" onClick={() => navigate(-1)}>
              <BsArrowLeft size={24} />
            </span>
          </div>

          <div className="col-8 d-flex justify-content-center align-items-center">
            <span className="topheading fw-semibold">Confirm Your Details</span>
          </div>
          <div className="col-2 d-flex justify-content-center align-items-center"></div>
        </div>
        <div
          className="overflow-y-scroll"
          style={{ height: "100vh", overflowY: "auto" }}
        >
          <div className="ticketbody pb-2">
            <p className="summaryheading fw-semibold">Booking Summary</p>
          </div>
          <div className="ticket-Details mt-0 card pt-2 pb-2 px-0 mx-3 border-0 shadow gap-1">
            <div className="ticketNAme border-0 px-3">
              <span>{ReduceText(eventDetail?.event_name || "")}</span>
              <span className="ticketprice">
                ₹{" "}
                {billing.subTotal.toLocaleString("en-IN", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </span>
            </div>
            <hr className="m-0" style={{ borderColor: "#B4B4B4FF" }} />
            <div className="ticketdatetime border-0 px-3">
              <span>
                {" "}
                {formatDate(eventDetail.event_starts_date)}{" "}
                {formatTime(eventDetail.event_starts_Time)}
              </span>
              <span>{eventDetail.event_city} </span>
            </div>
            <hr className="m-0" style={{ borderColor: "#B4B4B4FF" }} />
            {/* <hr /> */}
            {/* <hr className="m-0" /> */}
            <div className="d-flex justify-content-between px-3">
              <span className="ticketNAme">
                {/* {
                ticket && ticket.length > 0 ? (
                  ticket.map((tic, index) => (
                    <span key={tic._id}>
                      {index > 0 ? "," : ''} {tic.ticket_Name}
                    </span>
                  ))
                ) : (
                  ""
                )
              } */}

                <span>{ticket.ticket_Name}</span>
              </span>
              <span className="quantity">{billing.ticketQuantity} Tickets</span>
            </div>
          </div>
          <div className=" mt-3 mx-3">
            {isCouponValidate ? (
              <div className="card">
                <div className="card-body">
                  <div className="couponCode">Coupon Code</div>
                  <div className="d-flex justify-content-between text-center">
                    <span className="couponCode-2">
                      You saved ₹ {givendiscount} with '{couponCode}'
                    </span>{" "}
                    <span
                      className="couponCode removeCouponCode"
                      onClick={handleRemoveCoupon}
                    >
                      Remove
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <div className="ticketname mb-2">Have a discount code?</div>
                <div className="d-flex">
                  <input
                    type="text"
                    className="code w-100 rounded-start-3"
                    placeholder="Enter code"
                    onChange={(e) => setCouponCode(e.target.value)}
                  />

                  <div
                    className="apply rounded-end-3"
                    onClick={handleApplyCoupon}
                    disabled={isLoadCoupon}
                  >
                    {isLoadCoupon ? (
                      <SyncLoader
                        animation="border"
                        color="#FFFF"
                        size="7"
                        speedMultiplier={1}
                        margin={4}
                      />
                    ) : (
                      "Apply"
                    )}
                  </div>
                </div>
                {isCouponError ? (
                  <span className="have-discount text-danger fw-semibold">
                    * {CouponError}
                  </span>
                ) : (
                  ""
                )}
              </div>
            )}
          </div>
          <div className="bookingDetails card mx-3 p-3 py-2 border-0 shadow">
            <div className="d-flex justify-content-between">
              <p className="ticketprice">Sub-total</p>
              <p className="ticketprice">
                ₹{" "}
                {billing.subTotal.toLocaleString("en-IN", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </p>
            </div>
            <div>
              <div
                className="d-flex justify-content-between"
                data-bs-toggle="collapse"
                data-bs-target="#collapseExample"
                aria-expanded="false"
                aria-controls="collapseExample"
              >
                <p className="ticketprice">
                  Convenience Fee <SlArrowDown className="arrowdown ms-3" />
                </p>
                <p className="ticketprice">
                  ₹{" "}
                  {billing.bookingFee.toLocaleString("en-IN", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </p>
              </div>
              {/* Collapse div */}
              <div
                className="collapse w-100 border-top border-bottom py-2 border-secondary-subtle mt-1"
                id="collapseExample"
              >
                <div className="d-flex justify-content-between mb-1">
                  <span className="ticketprice fw-normal">Base Price</span>
                  <span className="ticketprice fw-normal">
                    ₹{" "}
                    {Number(billing.basic_price).toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div className="d-flex justify-content-between mb-1">
                  <span className="ticketprice fw-normal">
                    Central GST(CGST) @9%
                  </span>
                  <span className="ticketprice fw-normal">
                    ₹{" "}
                    {Number(billing.gst).toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div className="d-flex justify-content-between">
                  <span className="ticketprice fw-normal">
                    State GST(SGST) @9%
                  </span>
                  <span className="ticketprice fw-normal">
                    ₹{" "}
                    {Number(billing.gst).toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
              </div>
            </div>
            {isCouponValidate ? (
              <div>
                <div className="d-flex justify-content-between border-bottom border-secondary-subtle mt-2">
                  <span className="ticketprice fw-normal">Coupon Discount</span>
                  <span className="ticketprice fw-normal">
                    ₹{" "}
                    {givendiscount.toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div className="d-flex justify-content-between mt-1">
                  <span className="total">Total Amount</span>
                  <span className="total">
                    ₹{" "}
                    {payable_amount.toLocaleString("en-IN", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
              </div>
            ) : (
              <div className="d-flex justify-content-between mt-1">
                <p className="total fw-bold">Total Amount</p>
                <p className="total fw-bold">
                  ₹{" "}
                  {billing.total_amount.toLocaleString("en-IN", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </p>
              </div>
            )}
          </div>
          <div style={{ height: "100px" }}></div>
        </div>

        <div className="smallfooterPart py-2">
          <div className="text-center">
            {ticket.ticket_type === "free" ? (
              <button
                className="proceedpay-btn btn-booking text-white"
                onClick={handleGetTicket}
                disabled={loading}
              >
                {loading ? (
                  <SyncLoader
                    animation="border"
                    color="#FFFF"
                    size="7"
                    speedMultiplier={1}
                    margin={4}
                  />
                ) : (
                  "Get your Ticket"
                )}
              </button>
            ) : (
              <button
                className="proceedpay-btn"
                onClick={handleProceedToPay}
                disabled={loading}
              >
                {loading ? (
                  <SyncLoader
                    animation="border"
                    color="#FFFF"
                    size="7"
                    speedMultiplier={1}
                    margin={4}
                  />
                ) : (
                  "Proceed to Pay"
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* backto Product page confirmation modal */}
      {/* <!-- Modal --> */}
      <div
        className="modal fade"
        id="exampleModal"
        tabindex="-1"
        aria-labelledby="exampleModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body">
              <span>Are you sure you want to exit?</span>
            </div>
            <div className="modal-footer d-flex justify-content-center">
              <button
                type="button"
                className="btn primary_background text-white"
                data-bs-dismiss="modal"
              >
                <span className="text-white"></span>Cancel
              </button>
              <button
                type="button"
                className="btn text-primary"
                onClick={() =>
                  navigate(
                    `/${
                      eventDetail.event_city
                    }/${eventDetail.event_name.replace(/\s+/g, "_")}/${
                      eventDetail.unique_id
                    }`
                  )
                }
                data-bs-dismiss="modal"
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Coupon Applied view */}
      <div
        className="modal fade"
        id="couponAppliedModal"
        tabindex="-1"
        aria-labelledby="couponAppliedModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-sm modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body text-center">
              {/* <img src={successTick} alt='successTick' /> */}
              <PiSealCheckFill style={{ color: "#ff2975", fontSize: "64px" }} />

              <div className="text-center mt-2">
                <div className="couponCode">'{couponCode}' applied</div>
                <div className="couponAmt">You saved ₹ {givendiscount}</div>
                <div className="congr">with this coupon code</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default TicketBooking;
