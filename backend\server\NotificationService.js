const admin = require('./firebase'); 

class NotificationService {
    static async sendNotification(deviceToken, title, body) {
        const message = {
            notification: {
                title, 
                body
            },
            token: deviceToken, 
        };

        try {
            // Send the notification using Firebase Admin SDK
            const response = await admin.messaging().send(message);
            console.log('Notification sent successfully:', response);
            return response;
        } catch (error) {
            console.error('Error sending notification:', error);
            throw error; // Ensure you're throwing the caught error, not `undefined`
        }
    }
}

module.exports = NotificationService;
