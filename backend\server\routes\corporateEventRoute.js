const express = require('express');
const corporateRouter = express.Router();
const multer = require('multer');
const { Storage } = require('@google-cloud/storage');
const path = require('path');
const { corporateEventImages, corporateEventCreation, corporateEventFetching,
    GetCorporateEvent, GetGeneratedGateScannerLink, NoOfTicketScanned,
    GenerateGateScannerLink, GenerateEmpLogin, CorporateDashBoardMatrices,
    IndividualCorporateEventData, CreateCorporateTicket,GetCorporateTicket,
    CorporateEventBookInvoiceDetails, SendMailToCorporate, FetchUserDataByEvent,
    StoreFreeCorporateTickets, FetchCorporateBookedEvent, StoreExcelSheetInDB,
    SaveNewCorporateCompanyDetail, CompanyName, GetCorporateComapny, GetEventCounts,
    GetCompanyDetailById, FetchCorporateBookedEventByBookingId, GetCorporateAllEvent,
    PocLoginVerification, DeleteCompanyRecord, DeleteEventRecord, FetchCorporateBookedTicket,
    UpdateValidateCorporateEntry
 } = require('../controllers/corporateController');

// Instantiating a Google Cloud Storage client
const storage = new Storage({
  keyFilename: '../googleCloudBucketConfig.json', // replace with the path to your service account key file
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your Google Cloud Storage bucket name

// Multer configuration to store files in memory
const uploadImg = multer({
  storage: multer.memoryStorage(), // Store files in memory (for direct upload to GCS)
});
const upload = multer({ dest: 'uploads/' });

// Routes for handling uploads
corporateRouter.post('/corporate-images', uploadImg.fields([
  { name: 'cover_image', maxCount: 1 },
  { name: 'venueLayout', maxCount: 1 },
  { name: 'artist_picture', maxCount: 1 },
  { name: 'emp_data_excelSeet', maxCount: 1 }
]), corporateEventImages);

corporateRouter.post(`/upload`,upload.fields([
  { name: 'cover_image', maxCount: 1 },
  { name: 'venueLayout', maxCount: 1 },
  { name: 'artist_picture', maxCount: 1 },
  { name: 'emp_data_excelSeet', maxCount: 1 }
]),StoreExcelSheetInDB)

corporateRouter.post(`/upload/excel-sheet`,upload.fields([
  { name: 'emp_data_excelSeet', maxCount: 1 }
]),StoreExcelSheetInDB)


corporateRouter.post('/create-corporate-event',corporateEventCreation);
corporateRouter.post('/emp-login',GenerateEmpLogin);
corporateRouter.post('/poc-login',PocLoginVerification);
corporateRouter.post('/sendcorporatemail',SendMailToCorporate);
corporateRouter.post('/create-corporate-event-ticket',CreateCorporateTicket);
corporateRouter.get('/get-created-tickets/:ticket_id',GetCorporateTicket);
corporateRouter.get('/getall-corporate-events',corporateEventFetching);
corporateRouter.get('/getall-corporate-companies',GetCorporateComapny);
corporateRouter.get('/get-corporate-event/:id',GetCorporateEvent);
corporateRouter.get('/get-corporate-all-event-list/:id',GetCorporateAllEvent);
corporateRouter.post('/generate-scanner-link',GenerateGateScannerLink);
corporateRouter.get('/get-scanner-link/:id', GetGeneratedGateScannerLink);
corporateRouter.get('/corporate-event-detail/:event_id', IndividualCorporateEventData);
corporateRouter.get('/corporate-event-summary/invoice/:id', CorporateEventBookInvoiceDetails);
corporateRouter.get('/events/:event_id', FetchUserDataByEvent);
corporateRouter.get('/dashboard', CorporateDashBoardMatrices);
corporateRouter.get('/no-of-ticket-scan/:event_id', NoOfTicketScanned);
corporateRouter.post('/bookfreecorporatetickets',StoreFreeCorporateTickets);
corporateRouter.get('/ticket/:user_id', FetchCorporateBookedEvent);
corporateRouter.get('/yourticket/:order_id', FetchCorporateBookedEventByBookingId);
corporateRouter.post('/create-new-corporate-company', SaveNewCorporateCompanyDetail);
corporateRouter.get('/company', CompanyName);
corporateRouter.get('/event-count/:company_id', GetEventCounts);
corporateRouter.get('/company-detail/:company_id', GetCompanyDetailById);
corporateRouter.delete('/delete-company-record/:company_id', DeleteCompanyRecord);
corporateRouter.delete('/delete-event-record/:event_id', DeleteEventRecord);
corporateRouter.get('/ticket/:booking_id/:ticket_id', FetchCorporateBookedTicket);
corporateRouter.put('/update-entries/:id', UpdateValidateCorporateEntry);


module.exports = corporateRouter;
