const express = require('express');
const AdditionalDataModel = require('../models/AdditionDataModel');
const CeremonyModel = require('../models/CeremonyModel');

const SearchController = async (req, res) => {
    const { query } = req.query;
    if (!query) {
        return res.status(400).json({ message: 'Query is required' });
    }

    try {
        // Search in CeremonyModel
        const eventResults = await CeremonyModel.find({
            $or: [
                { event_name: { $regex: query, $options: 'i' } },
                { event_venue: { $regex: query, $options: 'i' } },
                { event_city: { $regex: query, $options: 'i' } },
            ],
        });

        // Search in AdditionalDataModel
        const artistResults = await AdditionalDataModel.find({
            Artist: { $regex: query, $options: 'i' },
        });

        // Combine results
        const combinedResults = [...eventResults, ...artistResults];

        res.json(combinedResults);
    } catch (error) {
        res.status(500).json({ message: 'Server error', error });
    }
};


// const SearchController = async (req, res) => {
//     const query = req.query.q;
//     try {
//       const filteredEvent = CeremonyModel.filter(event =>
//         event.event_name.toLowerCase().includes(query.toLowerCase())
//       ).slice(0, 10); // Limit results for performance
//       res.json(filteredEvent);
//     } catch (error) {
//       res.status(500).json({ message: error.message });
//     }
// };

module.exports = { SearchController };
