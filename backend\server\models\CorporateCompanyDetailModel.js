const MongoDB = require('mongoose');

const CorporateCompanyDetailSchema = new MongoDB.Schema({
    company_name: { type: String, required: true},
    industry_type: { type: String, required: true},
    poc_name: { type: String, required: true},
    office_address: { type: String, required: true},
    office_city: { type: String, required: true},
    office_pin_code: { type: String, required: true},
    poc_contact_number: { type: String, required: true, unique: true},
    poc_Email_id: { type: String, required: true, unique: true},
    company_size: { type: String, required: true},
    office_billing_address: { type: String, required: true},
    company_gst_number: { type: String, required: true},
    passcode : {type: String},
    role : {type : String, defalut : "corporate"},
    createdAt: {
        type: Number,
        default: Date.now,
      },
});

const CorporateCompanyDetailModel = MongoDB.model("CorporateCompanyDetail", CorporateCompanyDetailSchema);
module.exports = CorporateCompanyDetailModel;