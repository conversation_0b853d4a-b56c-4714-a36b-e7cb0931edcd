body {
  margin: 0;
  /* font-family: "Poppins", sans-serif !important; */
  font-family: "Nunito Sans", sans-serif !important;
  height: 100vh !important;
}
/* Hide the spinners in WebKit browsers */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.primary_background{
  background: #2C9CF0 !important;
}
.secondary_background{
  color: #fff;
  background: #6A6A6A !important;
}

.secondary_background:hover{
  color: #fff;
  background: #6A6A6A !important;
}

.fontstylePoppin {
}

code {
}

/* Common Css */
.link {
  color: white;
  text-decoration: none;
}

.main-PageHeading{
  font-size: 22px;
}
.mainSub-PageHeading{
  font-size: 20px;
}

@media screen and (max-width :578px) {
  #hideforSmallDevices {
    display: none !important;
  }

  .main-PageHeading{
    font-size: 20px;
  }
  .mainSub-PageHeading{
    font-size: 18px;
  }

  .smallscreen{
    display: none;
  }
}

/* AddEventDetails Css */
.addeventdetailDiv {
  width: 80%;
  margin: auto;
  margin-bottom: 10%;
}

.subnavbar {
  width: 70%;
  margin: auto;
  margin-top: 32px;
}

.subnavitem {
  font-size: 23px;
  color: #808080;
}

.isActiveForm {
  color: rgb(28, 184, 246);
}

.defalut {
  color: #808080;
}

.poppins20 {
  font-size: 18px;
}

.eveVisibilitybtn {
  width: 20% !important;
  height: 47px;
}

.adminfooterDiv {
  height: 80px !important;
  width: 100%;
  background: #ffff !important;
  box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.2);
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: end;
  align-items: center;
}

.nextbtn {
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  height: 45px !important;
  color: #ffff !important;
  margin-right: 19% !important;
}
.basicnextbtn{
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  height: 45px !important;
  color: #ffff !important;
  margin-right: 11.5% !important;
}
.locationnextbtn{
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  height: 45px !important;
  color: #ffff !important;
  margin-right: 11.5% !important;
}

.backbtn {
  background: #6A6A6A !important;
  width: 124px !important;
  height: 45px !important;
  color: #ffff !important;
  margin-right: 10px !important;
}

.backbtn:hover {
  background: #259bf4 !important;
}

.backbtn:hover~.nextbtn {
  background: #6A6A6A !important;
}

.coverImg_style {
  background-color: rgb(244, 247, 248);
  border: 2px dashed rgb(188, 199, 203);
  width: 388px;
  height: 202px;
}

.uploadFile {
  position: absolute;
  width: 388px;
  height: 202px;
}

.selectFileStyle {
  position: relative;
  top: 0;
  Z-index: 1;
  opacity: 0;
}

.poppins16 {
  font-size: 16px;
  font-weight: bold;
}

.poppins12 {
  font-size: 12px;
  color: #676767;
}

.uploadIcon {
  font-size: 50px !important;
  margin-top: 10%;
}

.uploadbtn {
  width: 388px !important;
  background: #2C9CF0 !important;
  color: #ffff !important;
}

.tickettypebtn {
  width: 45% !important;
  height: 47px;
  margin-left: -1% !important;
  border: 2px solid #2C9CF0 !important;
}

.primary {
  background: #2C9CF0 !important;
}

@media screen and (max-width: 1115px) {
  .subnavbar {
    width: 60%;
    margin: auto;
    margin-top: 32px;
  }

  .subnavitem {
    
    font-size: 20px;
    color: #808080;
  }

  .poppins20 {
    
    font-size: 18px;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 368px;
    height: 202px;

  }

  .uploadFile {
    position: absolute;
    width: 368px;
    height: 202px;

  }

  .uploadbtn {
    width: 368px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }
}

@media screen and (max-width: 768px) {
  .subnavbar {
    width: 60%;
    margin: auto;
    margin-top: 30px;
    margin-bottom: -5%;
  }

  .subnavitem {
    
    font-size: 15px;
    color: #808080;
  }

  .poppins20 {
    
    font-size: 12px;
  }

  .adminfooterDiv {
    height: 45px !important;
    background: #D9D9D9 !important;
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: end;
    align-items: center;
  }

  .nextbtn {
    background: #6A6A6A !important;
    width: 104px !important;
    height: 35px !important;
    color: #ffff !important;
    margin-right: 20% !important;
  }

  .backbtn {
    background: #6A6A6A !important;
    width: 104px !important;
    height: 35px !important;
    color: #ffff !important;
    margin-right: 10px !important;
  }

  .eveVisibilitybtn {
    width: 25% !important;
    height: 37px;
  }

  .form-control {
    font-size: 12px !important;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 308px;
    height: 195px;

  }

  .uploadFile {
    position: absolute;
    width: 308px;
    height: 195px;

  }

  .uploadbtn {
    width: 308px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }
}

@media screen and (max-width: 545px) {
  .addeventdetailDiv {
    width: 90%;
    margin: auto;
  }

  .subnavbar {
    width: 80%;
    margin: auto;
    margin-top: 30px;
    margin-bottom: -5%;
  }

  .subnavitem {
    
    font-size: 12px;
    color: #808080;
  }

  .poppins20 {
    
    font-size: 10px;
  }

  .adminfooterDiv {
    height: 45px !important;
    background: #D9D9D9 !important;
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: end;
    align-items: center;
  }

  .nextbtn {
    background: #6A6A6A !important;
    width: 104px !important;
    height: 35px !important;
    color: #ffff !important;
    margin-right: 20% !important;
  }

  .eveVisibilitybtn {
    width: 40% !important;
    height: 37px;
  }

  .form-control {
    font-size: 10px !important;
    border-color: #2C9CF0 !important;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 255px;
    height: 195px;

  }

  .uploadFile {
    position: absolute;
    width: 255px;
    height: 195px;

  }

  .uploadbtn {
    width: 220px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }

  .poppins16 {
    font-size: 12px;
  }

  .poppins12 {
    font-size: 10px;
  }

  .tickettypebtn {
    width: 20% !important;
    height: 32px;
    font-size: 10px !important;
  }
}




/* Css For Admin Part */

/* Style For Admin Part */
/* Admin EventList Page CSS */
.eventHeader {
  background-color: rgba(87, 36, 195, 0.877);
}

.viewbtn {
  border: 1px solid gray;
  border-radius: 20px;
  background-color: rgb(254, 252, 252);
  width: 80px;
}

.eventList {
  width: 98%;
}

.eventList:hover {
  width: 100%;
  box-shadow: 2px 5px 10px rgb(185, 183, 183);
}


.isActiveForm {
  color: rgb(28, 184, 246);
}

.border-line {
  border-top: 2px solid gray;
  margin-top: 12px;
}

.form-control {
  border: 2px solid rgb(28, 184, 246);
}

.activebtn {
  width: 70% !important;
}
.ticketactivebtn{
  width: 80% !important;
  background: #D5FACF;
  color: #30912E;
  padding: 0.2rem 0.7rem;
}

.addticketbtn {
  border: 1.5px solid #2C9CF0 !important;
  color: #2C9CF0 !important;
}

.file-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.file-input-container input[type="file"] {
  display: none;
}

.file-input-container label.file-input-label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 38px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  padding: 0 10px;
  box-sizing: border-box;
}

.file-input-container input[type="text"] {
  width: calc(100% - 40px);
  border: none;
  padding: 0;
  margin: 0;
}

.upload-icon {
  font-size: 20px;
  margin-left: auto;
  margin-right: 10px;
}

.upload-text {
  flex-grow: 1;
}

.file-input-container label.file-input-label:hover {
  border-color: #2C9CF0;
}

.file-input-container label.file-input-label:focus-within {
  border-color: #2C9CF0;
}

body {
  margin: 0;
}

/* Hide the spinners in WebKit browsers */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.fontstylePoppin {
  
}

code {
}

.edit-btn {
  background: #2C9CF0 !important;
  font-size: 14px !important;
  
  border: none;
  border-radius: 1rem;
  color: #ffff !important;
  padding: 0.2rem 1rem;
  margin-top: 0.2rem;
}

/* Common Css */
.link {
  color: white;
  text-decoration: none;
}

button {
  border: none;
}


/* AddEventDetails Css */
.addeventdetailDiv {
  width: 70%;
  margin: auto;
  margin-bottom: 10%;
}

.subnavbar {
  width: 45%;
  margin: auto;
  margin-top: 32px;
}

.compDetailsubnavbar {
  width: 70%;
  margin: auto;
  margin-top: 32px;
}

.subnavitem {
  
  font-size: 22px !important;
  color: #808080;
  font-weight: bold;
  white-space: nowrap;
}

.isActiveForm {
  color: rgb(28, 184, 246);
}

.defalut {
  color: #808080;
}

.poppins20 {
  
  font-size: 18px;
}

.eveVisibilitybtn {
  width: 20% !important;
  height: 47px;
}

.venuelatitude {
  display: flex;
  justify-content: space-between;
}

.adminfooterDiv {
  height: 60px !important;
  width: 100%;
  background: #ffff !important;
  /* box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.2); */
  border: 0.2px solid #C3C3C3;
  position: fixed;
  bottom: 0;
}
.fotterBtn{
  width: 85%;
  height: 100%;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}
.nextbtn {
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  
  height: 45px !important;
  color: #ffff !important;
  margin-right: 19% !important;
}

.basicnextbtn {
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  
  height: 45px !important;
  color: #ffff !important;
}

.locationnextbtn {
  background: #2C9CF0 !important;
  width: 124px !important;
  font-size: 20px !important;
  
  height: 45px !important;
  color: #ffff !important;
  margin-right: 11.5% !important;
}

.backbtn {
  background: #6A6A6A !important;
  width: 124px !important;
  height: 45px !important;
  color: #ffff !important;
  margin-right: 10px !important;
}

.backbtn:hover {
  background: #259bf4 !important;
}

.backbtn:hover~.nextbtn {
  background: #6A6A6A !important;
}

.coverImg_style {
  background-color: rgb(244, 247, 248);
  border: 2px dashed rgb(188, 199, 203);
  width: 388px;
  height: 202px;
}

.uploadFile {
  position: absolute;
  width: 388px;
  height: 202px;
}

.selectFileStyle {
  position: relative;
  top: 0;
  Z-index: 1;
  opacity: 0;
}

.poppins16 {
  
  font-size: 12px;
}

.poppins12 {
  
  font-size: 12px;
  color: #676767;
}

.uploadIcon {
  font-size: 50px !important;
  margin-top: 10%;
}

.uploadbtn {
  width: 388px !important;
  background: #2C9CF0 !important;
  color: #ffff !important;
}

.ticket-type-btn {
  width: 45%;
  border-radius: 0.5rem;
  height: 47px;
  border: 1px solid #2C9CF0 !important;
}

.fulllength {
  width: 50%;
}

.primary {
  width: 45%;
  border-radius: 0.5rem;
  background: #2C9CF0 !important;
}

.ticketfooterDiv {
  height: 80px !important;
  background: #FFFFFF !important;
  position: sticky;
  bottom: 0;
  display: flex;
  border: 0.2px solid #C3C3C3;
  justify-content: center;
  align-items: center;
}

.createticketbtn {
  background: #2C9CF0 !important;
  width: 35% !important;
  font-size: 18px !important;
  
  height: 45.33px !important;
  color: #ffff !important;
  border-radius: 0.8rem !important;
}

.save-cop-eventbtn{
  background: #2C9CF0 !important;
  width: 35% !important;
  font-size: 18px !important;
  
  height: 45.33px !important;
  color: #ffff !important;
  border-radius: 0.8rem !important;
}

.dateSpanStyle{
  border: 1px solid #2C9CF0;
  border-radius: 0.5rem;
  padding: 0.5rem;
  margin: 1%;
  cursor: pointer;
  outline: none;     /* Removes the focus outline */
  user-select: none; /* Prevents text selection */
  -webkit-user-select: none; /* For Safari */
  -moz-user-select: none; /* For Firefox */
}
.selectedDate{
  border: 1px solid #2C9CF0;
  border-radius: 0.5rem;
  color: white;
  background:#2C9CF0;
}
.addticket_btn {
  border: 1.5px solid #2C9CF0 !important;
  background-color: #2C9CF0 !important;
  color: #FFFFFF !important;
  padding: 0.3rem 1.5rem 0.3rem 1.5rem;
}

.calenderStyle{
 width: 90%;
 margin: auto;
 padding-top: 20px;
}

.eventDisplay{
align-items: center;
}

/* ✅ Force Calendar Container to Exact Size */
.small-calendar-container {
  height: 300px !important; /* ✅ Fixed Height */
  overflow-y: hidden !important; /* ✅ Hide scrollbars */
}

/* ✅ Adjust Day Grid for Compact Layout */
.small-calendar-container .fc .fc-daygrid-day-number {
  font-size: 8px !important; /* ✅ Smaller day numbers */
  height: 10px !important;
}

.fc .fc-daygrid-day-number {
 color: #000000;
 text-decoration: none;
}

/* ✅ Reduce Cell Padding for No Extra Space */
.small-calendar-container .fc .fc-daygrid-day {
  padding: 1px !important;
}

.calenderDayViewStyle .fc .fc-daygrid-day {
  background-color: #FFFFFF;
}
/* ✅ Small Event Text */
.small-calendar-container .fc .fc-event-title {
  font-size: 7px !important;
  padding: 1px;
}

.small-calendar-container .fc .fc-col-header-cell {
  font-size: 10px !important;
  color: #000 !important;
  text-decoration: none !important;
}

.fc .fc-col-header-cell-cushion {
  color: #000 !important; /* Change text color */
  text-decoration: none !important;
  font-weight: 500 !important;

}
 

.fc .fc-button {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  color: black !important; /* Adjust the text color if needed */
}

.fc .fc-button:hover {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

.fc-timegrid-slot {
  background-color: white !important; /* Makes each time slot white */
}

.fc-timegrid-slot-alternate {
  background-color: white !important; /* Ensures alternating slots are also white */
}


/* Css For Admin Part */

/* Style For Admin Part */
/* Admin EventList Page CSS */
.eventHeader {
  background-color: rgba(87, 36, 195, 0.877);
}

.viewbtn {
  border: 1px solid gray;
  border-radius: 20px;
  background-color: rgb(254, 252, 252);
  width: 80px;
}

.eventList {
  width: 98%;
}

.eventList:hover {
  width: 100%;
  box-shadow: 2px 5px 10px rgb(185, 183, 183);
}


.isActiveForm {
  color: rgb(28, 184, 246);
}

.border-line {
  border-top: 2px solid gray;
  margin-top: 12px;
}

.form-control,
.file-input-label {
  border: 0.5px solid rgb(28, 184, 246);
  height: 38px !important;
}

.activebtn {
  width: 70% !important;
}

.ticketactivebtn {
  width: 80% !important;
  background: #D5FACF;
  color: #30912E;
  padding: 0.2rem 0.7rem;
}

.addticketbtn {
  border: 1.5px solid #2C9CF0 !important;
  color: #2C9CF0 !important;
}

.file-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.file-input-container input[type="file"] {
  display: none;
}

.file-input-container label.file-input-label {
  display: flex;
  align-items: center;
  width: 100%;
  height: 38px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  padding: 0 10px;
  box-sizing: border-box;
}

.file-input-container input[type="text"] {
  width: calc(100% - 40px);
  border: none;
  padding: 0;
  margin: 0;
}

.upload-icon {
  font-size: 20px;
  margin-left: auto;
  margin-right: 10px;
}

.upload-text {
  flex-grow: 1;
}

.file-input-container label.file-input-label:hover {
  border-color: #2C9CF0;
}

.file-input-container label.file-input-label:focus-within {
  border-color: #2C9CF0;
}

.addMore-Btn {
  background: none;
  border: none;
  color: #2C9CF0;
}



/* Common Css For Pages */
.vender-heading {
  
  font-size: 25px;
  font-weight: bold;
}

.dashboard-summary {
  color: #202224;
  
  font-size: 14px;
}

.tableHead {
  background-color: #979797 !important;
}

.vendor-event-status-active {
  background: #00B69B;
  color: #FFFFFF;
  padding: 0.3rem 1.3rem;
  border-radius: 1rem;
}

.vendor-event-status-complete {
  background: #FCBE2D;
  color: #FFFFFF;
  padding: 0.3rem 1.3rem;
  border-radius: 1rem;
}

.tableDataRow:hover {
  cursor: pointer;
}

.createnewbtn {
  padding: 0.3rem 1rem 0rem 1rem;
  text-decoration: none;
  background: #2C9CF0;
  border-radius: 10rem;
  
  color: #ffff;
  margin-bottom: 0.2rem;
}

.addbank-btn {
  background-color: #2C9CF0;
  color: #ffff;
  padding: 0.3rem 1rem 0.3rem 1rem;
  border-radius: 0.5rem;
  
  border: none;
}

.btn-close-modal {
  background-color: #6A6A6A !important;
  color: #FFFFFF;
  padding: 0.3rem 1rem 0.3rem 1rem;
  border-radius: 0.5rem;
  
  border: none;
}

.btn-save-modal {
  background-color: #2C9CF0 !important;
  color: #FFFFFF;
  padding: 0.3rem 1rem 0.3rem 1rem;
  border-radius: 0.5rem;
  
  border: none;
}

.addbank-head {
  font-size: 22px;
  
}

#small-device-View {
  display: none;
}




/* EventDetailComponent Css */
.event-detail-head {
  background-color: #F1F4F9;
}

.event-name-heading {
  font-size: 24px;
  
  font-weight: bold;
}

.event-detail-heading {
  font-size: 14px;
  
  font-weight: bold;
  margin-right: 1.5rem;
}

.eventMaxtixMainDiv {
  display: flex;
}
.eventMaxtixMainDiv2 {
  display: flex;
  flex-wrap: wrap;
  /* Adjust space between divs */
}

.matrixDiv {
  height: 160px;
  width: 20%;
}
.corporatematrixDiv{
  height: 180px;
  width: 20%;
}

.matrixDiv-last {
  height: 160px;
  width: 22%;
}
.corporatematrixDiv-last {
  height: 180px;
  width: 20%;
}
.corporatematrixcard-last {
  height: 85px !important;
}
.corporatedashboard-summary{
  color: #202224;
  
  font-size: 10px;
}
.note_description{
  color: #979797;
  
  font-size: 10px;
}
.matrixcard-last {
  height: 72px !important;
}

.paymatrixDiv-last {
  height: 160px;
  width: 19%;
}

.sub-mat {
  padding: 0rem 2rem;
  margin-right: 15px;
} 

.sub-mat-head {
  font-size: 24px;
  
  font-weight: bold;
}

.sub-mat-btn {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0rem 2rem;
  border-radius: 1rem;
  border: none !important;
}

.sub-mat-subhead {
  font-size: 15px;
  
  font-weight: 300;
}

.count-style {
  font-size: 28px;
  
  font-weight: bold;
}

.count-style-last {
  font-size: 28px;
  
  font-weight: bold;
  margin-top: -0.5rem;
}

.download-btn {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0rem 1rem;
  border-radius: 5rem;
  border: none !important;
}
.corpoatedownload-btn {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 7px 10px !important;
  border-radius: 5rem;
  border: none !important;
}
.city-btnActive {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 5rem;
  border: none !important;
}
.city-btn {
  background-color:#00B69B;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 5rem;
  border: none !important;
}

.payment_status_paid {
  background-color: #00B69B;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0.3rem 1rem;
  border-radius: 1rem;
}

.payment_status {
  background-color: #FCBE2D;
  color: #FFFFFF;
  font-size: 14px;
  
  font-weight: bold;
  padding: 0.3rem 1rem;
  border-radius: 1rem;
}

.set-base-head {
  font-size: 16px;
  
  font-weight: 500;
}

.cancel-btn {
  background-color: #979797;
  color: #FFFFFF;
  font-size: 14px;
  
  padding: 0.2rem 2rem;
  border-radius: 5rem;
  border: none !important;
}

.update-btn {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  padding: 0.2rem 2rem;
  border-radius: 5rem;
  border: none !important;
}

.field_label {
  font-size: 14px;
  
}

.view_doc_btn {
  background-color: #F41E6D;
  color: #FFFFFF;
  font-size: 14px;
  
  padding: 0.2rem 2rem;
  border-radius: 5rem;
  border: none !important;
}

.replymessage {
  height: 150px !important;
}


/* Toggle switch */
/* Hide the default checkbox appearance */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 25px;
}

/* Hide the default checkbox input */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* Slider (round) style */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

/* Before state (unchecked) */
.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  border-radius: 50%;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

/* Change color when checked */
input:checked+.slider {
  background-color: #34C759;
}

/* Move the slider to the right when checked */
input:checked+.slider:before {
  transform: translateX(36px);
}

.notification-width{
  width: 50%;
}


.create-event-btn{
  background-color: #F41E6D;
  white-space: nowrap;
  font-size: 18px !important;
  padding: 0.5% 12% !important;
  border-radius: 5rem;
  border: none !important;
  color: #FFFF;
}

.corporateEventgeneratebtn{
  background-color: #F41E6D;
  white-space: nowrap;
  font-size: 18px !important;
  padding: 0.5rem 2rem;
  border-radius: 5rem;
  border: none !important;
  color: #FFFF;
}

.date-title{
  font-size: 32px;
  
  font-weight: bold;
}
.dropdown-toggle::after {
  display: none !important;
}
@media screen and (max-width: 600px) {
  .vender-heading {
    
    font-size: 20px;
    font-weight: bold;
  }

  .dashboard-summary {
    color: #202224;
    
    font-size: 10px;
  }

  .dashboard-summary-sm {
    color: #202224 !important;
    font-size: 8px !important;
  }

  .count-style {
    font-size: 18px;
    
    font-weight: bold;
  }

  .count-style-last {
    font-size: 18px;
    
    font-weight: bold;
    margin-top: -0.5rem;
  }

  .graphDimension {
    height: 200px !important;
  }

  .table-field {
    font-size: 13px;
    
  }

  .table-value {
    font-size: 12px;
    
  }

  .table-field,
  .table-value {
    white-space: nowrap;
  }

  .btn-text {
    font-size: 10px !important;
    
    white-space: nowrap;
    /* Prevents text wrapping */
    display: inline-flex;
    /* Keeps content aligned properly */
    align-items: center;
    /* Centers text vertically */
    justify-content: center;
  }

  .vendor-event-status-active {
    background: #00B69B;
    color: #FFFFFF;
    padding: 0.3rem 0.5rem;
    border-radius: 1rem;
    font-size: 12px;
    
  }

  .vendor-event-status-complete {
    background: #FCBE2D;
    color: #FFFFFF;
    padding: 0.3rem 0.5rem;
    border-radius: 1rem;
    font-size: 12px;
    
  }

  .event-name-heading {
    font-size: 20px;
    
    font-weight: bold;
  }

  .event-detail-heading {
    font-size: 10px; 
    
    font-weight: bold;
    margin-right: 1.5rem;
  }

  .eventMaxtixMainDiv {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    /* Adjust space between divs */
  }

  .eventMaxtixMainDiv2 {
    display: flex;
    flex-wrap: wrap;
    /* Adjust space between divs */
  }

  .matrixDiv {
    height: 160px;
    width: 45%;
    margin-right: 0px !important;
  }
  .corporatematrixDiv{
    height: 130px;
    width: 45%;
    margin-right: 0px !important;
  }

  .matrixDiv-last {
    height: 160px;
    width: 50%;
  }

  .corporatematrixDiv-last{
    height: 160px;
    width: 45%;
  }
  .smallViewSettopMargin{
    margin-top: 4% !important;
  }

  .download-btn {
    background-color: #F41E6D;
    color: #FFFFFF;
    font-size: 10px;
    
    font-weight: bold;
    padding: 0rem 0.5rem;
    border-radius: 5rem;
    border: none !important;
  }

  .city-btnActive {
    background-color: #F41E6D;
    color: #FFFFFF;
    font-size: 10px;
    
    font-weight: bold;
    padding: 0rem 0.5rem;
    border-radius: 5rem;
    border: none !important;
  }
  
  .city-btn {
    background-color:#00B69B;
    color: #FFFFFF;
    font-size: 10px;
    
    font-weight: bold;
    padding: 0rem 0.5rem;
    border-radius: 5rem;
    border: none !important;
  }
  

  .notification-width{
    width: 80%;
  }
  .submat-div{
    width: 48%;
    margin-bottom: 10px;
    margin-right: 5px;
  }
  .sub-mat {
    height: 150px !important;
    padding: 0rem 0rem;
    margin-right: 0px;
  } 
  .sub-mat-head {
    font-size: 16px;
    
    font-weight: bold;
  }
  .field_label {
    font-size: 12px;
    
  }
  .sub-mat-btn {
    background-color: #F41E6D;
    color: #FFFFFF;
    font-size: 12px;
    
    font-weight: bold;
    padding: 0rem 1rem;
    border-radius: 1rem;
    border: none !important;
  }
  .sub-mat-subhead {
    font-size: 10px;
    
    font-weight: 300;
  }
}


@media screen and (max-width: 1115px) {
  .subnavbar {
    width: 60%;
    margin: auto;
    margin-top: 32px;
  }

  .subnavitem {
    
    font-size: 20px;
    color: #808080;
  }

  .poppins20 {
    
    font-size: 18px;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 368px;
    height: 202px;

  }

  .uploadFile {
    position: absolute;
    width: 368px;
    height: 202px;

  }

  .uploadbtn {
    width: 368px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }
}

@media screen and (max-width: 768px) {
  .subnavbar {
    width: 60%;
    margin: auto;
    margin-top: 30px;
    margin-bottom: -5%;
  }

  .subnavitem {
    
    font-size: 15px;
    color: #808080;
  }

  .poppins20 {
    
    font-size: 12px;
  }

  .adminfooterDiv {
    height: 45px !important;
    background: #D9D9D9 !important;
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: end;
    align-items: center;
  }

  .nextbtn {
    background: #6A6A6A !important;
    width: 104px !important;
    height: 35px !important;
    color: #ffff !important;
    margin-right: 20% !important;
  }

  .backbtn {
    background: #6A6A6A !important;
    width: 104px !important;
    height: 35px !important;
    color: #ffff !important;
    margin-right: 10px !important;
  }

  .eveVisibilitybtn {
    width: 25% !important;
    height: 37px;
  }

  .form-control {
    font-size: 12px !important;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 308px;
    height: 195px;

  }

  .uploadFile {
    position: absolute;
    width: 308px;
    height: 195px;

  }

  .uploadbtn {
    width: 308px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }
}

/* common style */
/* hide large screen code at 568px */
@media (max-width: 580px) {
  #hideforSmallDevices {
    display: none !important;
  }

  #largesScreenDevice {
    display: none;
  }

  #SmallScreenDevice {
    display: block;
  }

  .create-event-btn{
    background-color: #F41E6D;
    white-space: nowrap;
    font-size: 10px !important;
    padding: 1% 4% !important;
    border-radius: 5rem;
    border: none !important;
    color: #FFFF;
  }

  .corporateEventgeneratebtn{
    background-color: #F41E6D;
    white-space: nowrap;
    font-size: 10px !important;
    padding: 0.5rem 1.5rem;
    border-radius: 5rem;
    border: none !important;
    color: #FFFF;
  }

  .calenderStyle{
    width: 100%;
    margin: auto;
    padding-top: 20px;
   }

  /* AddNewEvent Style */
  .subnavbar {
    width: 90%;
    margin: auto;
    margin-top: 18px;
    margin-bottom: -5%;
  }

  .subnavitem {
    
    font-size: 16px !important;
    color: #808080;
  }

  .border-line {
    border-width: 0.2px !important;
  }

  .addeventdetailDiv {
    width: 100%;
    margin: auto;
    height: 100vh;
  }

  .poppins20 {
    
    font-size: 16px;
  }

  .form-control,
  .file-input-label {
    font-size: 14px !important;
    border-color: #2C9CF0 !important;
    height: 40px !important;
  }

  .show {
    font-size: 10px !important;
  }

  .adminfooterDiv {
    height: 60px !important;
    background: #FFFFFF !important;
    position: fixed !important;
    bottom: 0;
    left: 0;
    display: flex;
    border: 0.2px solid #C3C3C3;
    justify-content: center;
    align-items: center;
  }

  .basicnextbtn {
    background: #2C9CF0 !important;
    width: 141px !important;
    font-size: 18px !important;
    
    height: 45.33px !important;
    color: #ffff !important;
    margin-right: 7% !important;
    border-radius: 0.8rem !important;
  }

  .createticketbtn{
    background: #2C9CF0 !important;
    width: 55% !important;
    font-size: 18px !important;
    
    height: 45.33px !important;
    color: #ffff !important;
    margin-right: 7% !important;
    border-radius: 0.8rem !important;
  }

  .save-cop-eventbtn{
    background: #2C9CF0 !important;
    width: 85% !important;
    font-size: 18px !important;
    
    height: 45.33px !important;
    color: #ffff !important;
    margin-right: 7% !important;
    border-radius: 0.8rem !important;
  }

  .venuelatitude {
    display: flex;
    flex-direction: column !important;
  }

  .backbtn {
    width: 141px !important;
    font-size: 18px !important;
    
    height: 45.33px !important;
    color: #ffff !important;
    margin-right: 7% !important;
    background: #6A6A6A !important;
    border-radius: 0.8rem !important;
    display: none !important;
  }

  .des {
    margin-top: 25px !important;
  }

  .fulllength {
    width: 100% !important;
  }

  .ticket-type-btn {
    height: 47px;
    border: 2px solid #2C9CF0 !important;
  }

  .ticketfooterDiv {
    height: 80px !important;
    background: #FFFFFF !important;
    position: fixed;
    width: 100%;
    bottom: 0;
    display: flex;
    border: 0.2px solid #C3C3C3;
    justify-content: center;
    align-items: center;
  }

  .setbottommargin {
    margin-bottom: 15rem !important;
  }

  .createticketbtn {
    background: #2C9CF0 !important;
    width: 50% !important;
    font-size: 18px !important;
    
    height: 45.33px !important;
    border-radius: 0.8rem !important;
  }

  .poppins10-sm {
    font-size: 14px;
  }

  .poppins14-sm {
    font-size: 14px;
  }

  .poppins16-sm {
    font-size: 16px;
  }

  .addticketbtn {
    border: 1.5px solid #2C9CF0 !important;
    color: #2C9CF0 !important;
    width: 70% !important;
    font-size: 12px !important;
  }

  #small-device-View {
    display: block !important;
  }

  #largedeviceView {
    display: none;
  }

  .addbank-head {
    font-size: 18px !important;
    
  }

  .addbank-btn {
    background-color: #2C9CF0;
    color: #ffff;
    padding: 0rem !important;
    border-radius: 0.5rem;
    
    border: none;
    width: 190px;
    height: 60px;
    margin-left: 1rem;
  }

  .coverImg_style {
    background-color: rgb(244, 247, 248);
    border: 2px dashed rgb(188, 199, 203);
    width: 208px;
    height: 145px;

  }

  .uploadFile {
    position: absolute;
    width: 208px;
    height: 145px;

  }
  .poppins16{
    font-size: 12px;
  }
  .poppins12{
    font-size: 10px;
  }
  .uploadbtn {
    width: 228px !important;
    background: #2C9CF0 !important;
    color: #ffff !important;
  }

  /* calender Small Screen */
  .fc-toolbar-title{
    font-size: 14px !important;
  }
  .fc-button-group{
    font-size: 12px !important;
  }
  .eventDisplay{
    font-size: 10px !important;
    white-space: wrap;
  }

  .addticket_btn {
    border: 1.5px solid #2C9CF0 !important;
    background-color: #2C9CF0 !important;
    color: #FFFFFF !important;
    font-size: 12px !important;
    padding: 0.5rem 1rem;
  }
}
