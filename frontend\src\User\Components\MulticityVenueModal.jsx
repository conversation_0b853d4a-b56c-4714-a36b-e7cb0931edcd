import React from "react";
import { TbMapPin } from "react-icons/tb";
import { Link } from "react-router-dom";

// MulticityVenueModal component definition
export const MulticityVenueModal = ({ open, handleClose, multiCityArray }) => {
  // Inline style for the custom primary color
  const primaryColor = "#000";

  return (
    // Bootstrap Modal structure
    <div
      className={`modal ${open ? "d-block fade show" : ""}`}
      tabIndex="-1"
      style={{
        display: open ? "block" : "none",
        backgroundColor: "rgba(0,0,0,0.5)",
      }} // Manual backdrop for simplicity
      aria-labelledby="multicity-modal-title"
      aria-describedby="multicity-modal-description"
    >
      <div className="modal-dialog modal-dialog-centered modal-lg">
        {" "}
        {/* Increased modal size to lg */}
        <div
          className="modal-content border-0 rounded-4 p-3"
          style={{ maxWidth: "600px", margin: "auto" }}
        >
          <div className="modal-header border-0 pb-0 d-flex justify-content-end p-0">
            <button
              type="button"
              className="btn-close"
              aria-label="Close"
              onClick={handleClose}
            ></button>
          </div>
          <div className="modal-body pt-0 p-0">
            <h2
              id="multicity-modal-title"
              className="h3 fw-bold text-dark text-center mb-2"
            >
              Venues
            </h2>

            {/* Dynamic content based on multiCityArray */}
            <div
              id="multicity-modal-description"
              className="d-flex flex-column gap-2 "
            >
              {multiCityArray && multiCityArray.length > 0 ? (
                multiCityArray.map((cityEvent) => (
                  <div key={cityEvent._id} className="p-2 rounded-lg">
                    <h3
                      className=" fs-5 fw-semibold"
                      style={{ borderColor: primaryColor, color: primaryColor }}
                    >
                      {cityEvent.event_city}
                    </h3>
                    {/* Venue */}
                    <p
                      style={{ marginLeft: -2 }}
                      className="text-muted mb-2 d-flex align-items-center fw-semibold"
                    >
                      <TbMapPin className="text-black fs-5 me-1" />
                      {/* <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="me-2"
                        style={{
                          width: "20px",
                          height: "20px",
                          color: primaryColor,
                        }}
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                          clipRule="evenodd"
                        />
                      </svg> */}

                      <span className="fw-medium ms-1 fs-6">
                        {`Venue: ${cityEvent.event_venue}`}
                      </span>
                    </p>
                    {/* Date */}
                    <p
                      style={{ marginLeft: -2 }}
                      className="mb-2 text-muted d-flex align-items-center fw-semibold"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="me-2"
                        style={{
                          width: "20px",
                          height: "20px",
                          color: primaryColor,
                        }}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>

                      <span className="text-base fw-medium fs-6">
                        {`Date: ${new Date(
                          cityEvent.event_date
                        ).toLocaleDateString("en-US", {
                          weekday: "long",
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}`}
                      </span>
                    </p>
                    <Link
                      to={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
                        cityEvent.event_venue + ", " + cityEvent.event_city
                      )}`}
                      className="text-primary text-decoration-none"
                      style={{ fontSize: "0.9rem" }}
                    >
                      Get Direction
                    </Link>
                  </div>
                ))
              ) : (
                <p className="text-muted text-center py-5">
                  No event data available to display.
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
