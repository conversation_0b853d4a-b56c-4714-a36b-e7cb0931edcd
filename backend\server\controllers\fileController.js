const CeremonyModel = require('../models/CeremonyModel');
const path = require('path');
const { Storage } = require('@google-cloud/storage');

const storage = new Storage({
  keyFilename: path.join(__dirname, '../googleCloudBucketConfig.json'),
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your bucket name

// Set Cover Image (single file upload)
const SetCoverImage = async (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).send({ message: 'No file uploaded' });
    }

    const gcsFileName = `ceremony-images/${Date.now()}-${file.originalname}`;
    const fileUpload = bucket.file(gcsFileName);

    // Upload the file to Google Cloud Storage
    await fileUpload.save(file.buffer, {
      metadata: {
        contentType: file.mimetype,
      },
    });

    // const fileUrl = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;
    const fileUrl = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;

    res.send({
      message: 'File uploaded successfully to Google Cloud Storage',
      file: fileUrl,
    });
  } catch (err) {
    console.error('Error uploading file:', err);
    res.status(500).send({ message: 'Error uploading file', error: err.message });
  }
};

// File Controller (multiple file upload)
const FileController = async (req, res) => {
  try {
    const uploadedFiles = {
      Venuepicture: req.files.Venuepicture ? req.files.Venuepicture[0] : null,
      artists: [],
    };

    // Upload venue picture
    if (uploadedFiles.Venuepicture) {
      const venueFile = uploadedFiles.Venuepicture;
      const gcsFileName = `venue-pictures/${Date.now()}-${venueFile.originalname}`;
      const gcsFile = bucket.file(gcsFileName);

      // Upload the file to Google Cloud Storage
      await gcsFile.save(venueFile.buffer, {
        metadata: {
          contentType: venueFile.mimetype,
        },
      });

      uploadedFiles.Venuepicture = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;
    }

    // Loop through artist pictures (if any)
    for (const key in req.files) {
      if (req.files[key].length > 0) {
        for (const file of req.files[key]) {
          const fileName = `${key}-${Date.now()}-${file.originalname}`;
          const gcsFile = bucket.file(fileName);

          // Upload file to Google Cloud Storage
          await gcsFile.save(file.buffer, {
            metadata: {
              contentType: file.mimetype,
            },
          });

          const fileUrl = `https://storage.googleapis.com/${bucket.name}/${fileName}`;
          if (key.startsWith('Artist_pic_')) {
            const index = key.split('_')[2];
            uploadedFiles.artists[index] = fileUrl;
          }
        }
      }
    }

    res.send({
      message: 'Files uploaded successfully',
      files: uploadedFiles,
    });
  } catch (err) {
    console.error('Error uploading files:', err);
    res.status(500).send({
      message: 'Error occurred while uploading files',
      error: err.message,
    });
  }
};

// Get Images (retrieve all cover images)
const GetImages = async (req, res) => {
  try {
    const events = await CeremonyModel.find({}, 'cover_image');
    const images = events.map(event => event.cover_image);
    res.status(200).send(images);
  } catch (error) {
    res.status(500).send({ message: 'Error fetching images', error });
  }
};

module.exports = { FileController, GetImages, SetCoverImage };
