import axios from "axios";
import React, { useEffect, useState } from "react";
import { API_URL } from "../../config";
import "./CategoryPage.css";
import { Link, useParams } from "react-router-dom";
import LocationModal from "../Components/LocationModal";
import NavBar from "../Components/NavBar";
import Footer from "../Components/Footer";
import PuffLoader from "react-spinners/PuffLoader";
import "../Components/Trendings.css";
import { useSelector } from "react-redux";
import { MdEventBusy } from "react-icons/md";
import noevents from "../../Assets/noevents.png";
function CategoryPage() {
  const city = useSelector((state) => state.city);
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [categoryEvents, setCategoryEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { category } = useParams();
  const Month = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const handleLocationChange = (location) => {
    setUserLocation(location);
    localStorage.setItem("userLocation", location);
    setShowLocationModal(false);
  };

  const handleShowLocationModal = (openModal) => {
    setShowLocationModal(openModal);
  };

  const getMonthFromDateString = (dateString) => {
    const date = new Date(dateString);
    return date.getMonth();
  };

  const getDayFromDateString = (dateString) => {
    const date = new Date(dateString);
    return date.getDate();
  };

  const lowestTicket = (ticketPrices) => {
    let lowestPrice = ticketPrices[0].ticket_price;
    for (let i = 1; i < ticketPrices.length; i++) {
      if (
        ticketPrices[i].ticket_price !== 0 &&
        lowestPrice > ticketPrices[i].ticket_price
      ) {
        lowestPrice = ticketPrices[i].ticket_price;
      }
    }
    return lowestPrice;
  };

  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get(`${API_URL}/api/category/${category}`);
        const filterbycity = response.data.filter(
          (citywise) => citywise.event_city === city
        );
        setCategoryEvents(filterbycity);
      } catch (error) {
        console.error("Error fetching categories:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchCategoryData();
  }, [category]);

  useEffect(() => {
    // Scroll to top when the component loads or data is done loading
    window.scrollTo({
      top: 0,
      behavior: "smooth", // Adds smooth scrolling
    });
  }, []);

  return (
    <div
      className="category-page-main"
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "start",
        alignItems: "center",
      }}
    >
      {showLocationModal && (
        <LocationModal
          onClose={() => setShowLocationModal(false)}
          onLocationChange={handleLocationChange}
        />
      )}

      <NavBar userLocation={userLocation} onNext={handleShowLocationModal} />

      <div className="category-page-container">
        <h4 className="category-page-title">Event Category : {category}</h4>

        {isLoading ? (
          <div className="trendingeventlist" style={{ marginTop: "1.5rem" }}>
            {[1, 2, 3].map((item, index) => {
              return (
                <div
                  className="card shadow-sm border-0 h-100"
                  style={{ borderRadius: "0.5rem", overflow: "hidden" }}
                >
                  {/* Image Placeholder */}
                  <div
                    className="card-img-top bg-light position-relative"
                    style={{ height: "180px", overflow: "hidden" }}
                  >
                    <div className="placeholder-glow placeholder-lg w-100 h-100"></div>
                    {/* Optional: Status Alert Placeholder */}
                    {/* <span
                      className="placeholder position-absolute bg-secondary"
                      style={{
                        top: "10px",
                        left: "10px",
                        width: "80px",
                        height: "25px",
                        borderRadius: "5px",
                      }}
                    ></span> */}
                  </div>

                  <div className="card-body p-3">
                    <div className="d-flex align-items-center">
                      {/* Date/Month Placeholder */}

                      {/* Event Details Placeholder */}
                      <div className="flex-grow-1 opacity-30">
                        <h5 className="card-title placeholder-glow mb-2 ">
                          <span className="placeholder col-10 rounded"></span>
                        </h5>
                        <p className="card-text placeholder-glow mb-1">
                          <span className="placeholder col-8 rounded"></span>
                        </p>
                        <p className="card-text placeholder-glow">
                          <span className="placeholder col-5 rounded"></span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : categoryEvents?.length > 0 ? (
          <div className="trendingeventlist" style={{ marginTop: "1.5rem" }}>
            {categoryEvents.map(
              (event, index) =>
                event.Addon &&
                event.Addon.length > 0 && (
                  <Link
                    style={{
                      width: "100%",
                      textDecoration: "none",
                      height: "100%",
                    }}
                    to={`/${event.event_city}/${event.event_name.replace(
                      /\s+/g,
                      "_"
                    )}/${event.unique_id}`}
                  >
                    <div className="trendingCard shadow-sm">
                      <div className="card-img-top trendingcardImg">
                        <img
                          src={`${event?.cover_image}`}
                          className="trendingImg"
                          alt="Event"
                        />
                      </div>
                      {event.ticket.every(
                        (tic) => tic.ticket_avability === 0
                      ) ? (
                        <div className="soldOut fw-semibold">Sold Out</div>
                      ) : new Date(
                          new Date(event.event_ends_date).setHours(0, 0, 0, 0)
                        ) < new Date(new Date().setHours(0, 0, 0, 0)) ? (
                        <div className="soldOut fw-semibold">Sold Out</div>
                      ) : (
                        <div className="statusAlert bg-success text-white fw-semibold">
                          Selling Fast
                        </div>
                      )}
                      <div className="trendingcardContent">
                        <div className="d-flex flex-column text-center me-lg-4 me-sm-2 me-4 ms-3">
                          <span className="month text-primary fw-bold">
                            {
                              Month[
                                getMonthFromDateString(event?.event_starts_date)
                              ]
                            }
                          </span>
                          <span className="date fw-bold">
                            {getDayFromDateString(event?.event_starts_date)}
                          </span>
                        </div>
                        <div className="d-flex flex-column">
                          <div className="eventhead fw-bold">
                            {event?.event_name}
                          </div>
                          <div className="eventvenue text-secondary fw-normal">
                            {event?.event_venue} {event?.event_city}
                          </div>
                          <div className="eventprice text-secondary fw-normal">
                            ₹{" "}
                            {event.ticket.length > 0
                              ? lowestTicket(event.ticket)
                              : "0"}{" "}
                            onwards
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                )
            )}
          </div>
        ) : (
          <div
            style={{
              marginTop: "1.5rem",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <img src={noevents} style={{ width: "200px" }} alt="No Event" />

            <p className="text-center fw-medium text-secondary mt-3">
              No events in this category
            </p>
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
}

export default CategoryPage;
