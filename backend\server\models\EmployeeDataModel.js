const MongoDB = require('mongoose');

const employeeSchema = new MongoDB.Schema({
  Employee_ID : { type: String, required: true, unique: true },
  Employee_Name : { type: String, required: true },
  Employee_Phone_number : { type: String, required: true },
  Employee_Email_ID : { type: String, required: true, unique: true },
  uniqueCode : { type: String, required: true }
});

const EmployeeModel = MongoDB.model("employeeData", employeeSchema);
module.exports = EmployeeModel;
