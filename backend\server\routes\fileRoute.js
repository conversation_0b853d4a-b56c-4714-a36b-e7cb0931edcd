const express = require('express');
const fileRouter = express.Router();
const multer = require('multer');
const { Storage } = require('@google-cloud/storage');
const path = require('path');
const { SetCoverImage, FileController, GetImages } = require('../controllers/fileController');

// Instantiating a Google Cloud Storage client
const storage = new Storage({
  keyFilename: '../googleCloudBucketConfig.json', // replace with the path to your service account key file
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your Google Cloud Storage bucket name

// Multer configuration to store files in memory
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory (for direct upload to GCS)
});

// Routes for handling uploads
fileRouter.post('/upload', upload.single('image'), SetCoverImage);
fileRouter.post('/file', upload.fields([
  { name: 'Venuepicture', maxCount: 1 },
  ...Array.from({ length: 10 }, (_, i) => ({ name: `Artist_pic_${i}`, maxCount: 1 })) // Dynamically support up to 10 artist pics
]), FileController);
fileRouter.get('/', GetImages);

module.exports = fileRouter;
