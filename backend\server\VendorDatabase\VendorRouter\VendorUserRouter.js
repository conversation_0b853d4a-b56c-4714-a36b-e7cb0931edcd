const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { Storage } = require('@google-cloud/storage');
const { SetDocuments } = require('../VendorController/documentsController');
const vendorRouter = express.Router();
const VendorUser = require('../VendorModel/VendorUser');
const { FeatchVendorEvents, GenerateScannerLink,
  DashBoardData, GateKeeperLogin, UpdateValidateEntry,
  GetGeneratedScannerLink, EventSellingData,
  GetattendedCandidates, DashBoardSoldData, UpdateVendorDetails } = require('../VendorController/vendorEventController');
const { DownloadAgreement } = require('../../controllers/emailController');

// Instantiating a Google Cloud Storage client
const storage = new Storage({
  keyFilename: '../googleCloudBucketConfig.json', // replace with the path to your service account key file
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your Google Cloud Storage bucket name

// Multer configuration to store files in memory
const upload = multer({
  storage: multer.memoryStorage(), // Store files in memory (for direct upload to GCS)
});

vendorRouter.post('/image/uploadpan-card', upload.single('image'), SetDocuments);
vendorRouter.post('/image/uploadcancel_cheque', upload.single('image'), SetDocuments);
vendorRouter.post('/document/uploadagreement', upload.single('image'), SetDocuments);
vendorRouter.get('/events/:vendor_id', FeatchVendorEvents);
vendorRouter.post('/generate-link', GenerateScannerLink);
vendorRouter.get('/get-link/:id', GetGeneratedScannerLink);
vendorRouter.get('/get-attended-candidates/:id', GetattendedCandidates);
vendorRouter.post('/login/gatekeeper', GateKeeperLogin);
vendorRouter.get('/dashboard/:vendor_id', DashBoardData);
vendorRouter.get('/solddatadashboard/:event_id', DashBoardSoldData);
vendorRouter.get('/eventbooking/:event_id', EventSellingData);
vendorRouter.put('/update-entries/:id', UpdateValidateEntry);
vendorRouter.post('/downloadpdf/agreement', DownloadAgreement);
vendorRouter.post('/document/uploadCarousel_img', upload.single('image'), SetDocuments);
vendorRouter.post('/document/uploadCategory_img', upload.single('image'), SetDocuments);
vendorRouter.put("/updateUser",UpdateVendorDetails)

vendorRouter.post("/addvendor", async (req, res) => {
  const {
    vendorBasicDetail: {
      _id,
      vendorType,
      name,
      gender,
      pan_no,
      address,
      isGSTIN_No,
      GSTIN_Details,
      userName,
      email,
      phoneNumber,
      bank_Account_type,
      beneficiar_name,
      bank_account_no,
      bank_name,
      IFSC_code,
      ITR_Filed,
      undertaking_Accepted,
      is2YearsITR
    },
    vendorDocumen: {
      pan_card,
      cancel_cheque
    },
    agreement,
  } = req.body;

  // Creating a new VendorUser instance
  const data = new VendorUser({
    _id: _id,
    vendorType: vendorType,
    name: name,
    gender: gender,
    pan_no: pan_no,
    address: address,
    isGSTIN_No: isGSTIN_No,
    GSTIN_Details: GSTIN_Details || [],
    userName: userName,
    email: email,
    phone: phoneNumber,
    bank_Account_type: bank_Account_type,
    beneficiar_name: beneficiar_name,
    bank_account_no: bank_account_no,
    bank_name: bank_name,
    IFSC_code: IFSC_code,
    ITR_Filed: ITR_Filed,
    pan_card: pan_card,
    cancel_cheque: cancel_cheque,
    signed_Agreement: agreement,
    undertaking_Accepted: undertaking_Accepted,
    is2YearsITR: is2YearsITR
  });

  try {
    await data.save();
    res.status(200).json({
      message: "New Vendor Added!",
    });
  } catch (err) {
    res.status(500).json({
      message: err.message,
    });
  }
});


// vendorRouter.put("/updateVendor", async (req, res) => {
//   const { _id } = req.body;

//   if (!_id) {
//     return res.status(400).json({
//       message: "User ID is required",
//       status: 400
//     });
//   }

//   try {
//     const updatedUser = await VendorUser.findByIdAndUpdate(
//       _id,
//       {
//         firstName: req.body.firstName,
//         lastName: req.body.lastName,
//         email: req.body.email,
//         dob: req.body.dob,
//         gender: req.body.gender,
//         address: req.body.address,
//         phone: req.body.phone,
//         city: req.body.city,
//         state: req.body.state,
//         pincode: req.body.pincode
//       },
//       { new: true } // Option to return the updated document
//     );

//     if (!updatedUser) {
//       return res.status(404).json({
//         message: "User not found",
//         status: 404
//       });
//     }

//     res.json({
//       message: "User updated successfully!",
//       status: 200,
//       data: updatedUser
//     });
//   } catch (err) {
//     res.status(400).json({
//       message: err.message,
//       status: 400
//     });
//   }
// });


vendorRouter.get("/getVendorWithID/:phone", async (req, res) => {
  try {
    const doc = await VendorUser.findOne({
      phone: req.params.phone,
    });
    if (!doc) {
      return res.status(201).json({ error: "User not available!" })
    }
    const document = {
      _id: doc._id,
      email: doc.email,
      name: doc.name,
      phone: doc.phone,
      userName: doc.userName,
      vendorType: doc.vendorType
    }
    res.status(200).json({sucess: "userfound",document});
  } catch (error) {
    console.error("Error fetching user data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

vendorRouter.get('/getVendor/:vendor_id', async (req, res) => {
  try {
    const vendor = await VendorUser.findOne({
      _id: req.params.vendor_id
    });
    if (!vendor) {
      return res.status(201).json({ message: "User not available!" })
    }
    const document = {
      bank_Account_type: vendor.bank_Account_type,
      IFSC_code: vendor.IFSC_code,
      beneficiar_name: vendor.beneficiar_name,
      bank_account_no: vendor.bank_account_no,
      bank_name: vendor.bank_name
    }
    res.status(200).json(document);
  } catch (error) {
    console.error("Error fetching user data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
})






module.exports = vendorRouter;
