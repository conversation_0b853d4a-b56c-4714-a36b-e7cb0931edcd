const CustomerQueryModel = require("../models/CustomerQuery");

const SaveCustomerQuery = async (req, res) => {
    try {
        const {
            query_type,
            customer_name,
            customer_email,
            phone,
            message
        } = req.body;

        // Create a new instance of the model with the received data
        const CustomerQuery = new CustomerQueryModel({
            Query_type: query_type,
             queryRaisedBy: customer_name,
             queryEmail:customer_email,
             message: message,
             Contact_No: phone
        });

        // Save the new event
        await CustomerQuery.save()
            .then(data => {
                res.status(200).send({ message: "Data added successfully", data });
            })
            .catch(error => {
                res.status(400).send({ message: "Unable to add. Please try again", error });
            });
    } catch (error) {
        res.status(500).send({ message: "Something went wrong, try again after some time", error });
    }
}

const UpdateQueryStatus = async (req, res) => {
    try {
        const updatedCustomerQuery = await CustomerQueryModel.findByIdAndUpdate(
            req.params.id,
            { Status: req.body.Status },
            { new: true } // Return the updated document
        );

        if (!updatedCustomerQuery) {
            return res.status(404).send({ message: "Query not found" });
        }

        res.status(200).send({
            message: "Updated successfully",
            data: updatedCustomerQuery,
        });
    } catch (error) {
        res.status(500).send({
            message: "Something went wrong, try again after some time",
            error,
        });
    }
};


module.exports ={
    SaveCustomerQuery,
    UpdateQueryStatus
}