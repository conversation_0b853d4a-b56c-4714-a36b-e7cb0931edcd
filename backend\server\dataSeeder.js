const mongoose = require('mongoose');
const Card = require('./models/Event');

const insertCardData = async () => {
  try {
    await Card.insertMany([
      {
        imageUrl: 'https://media.blogto.com/articles/20180222-k-pop-got7.jpg?w=2048&cmd=resize_then_crop&height=1365&quality=70',
        date: 'JAN 18',
        title: 'JYC 2011 JYC Worldwide Concert Barcelona',
        address: 'Socials, WHC Road, Nagpur',
        price: '₹ 1000 onwards'
      },
      {
        imageUrl: 'https://1409791524.rsc.cdn77.org/data/images/full/514212/kpopconcert-jpg.jpg',
        date: 'APR 20',
        title: ' Wonder Girls 2010 World Tour San Francisco',
        address: 'Socials, WHC Road, Nagpur',
        price: '₹ 1000 onwards'
      },
      // Add more card data as needed
    ]);
    console.log('Card data inserted successfully');
  } catch (err) {
    console.error('Error inserting card data:', err);
  }
};

module.exports = insertCardData;
