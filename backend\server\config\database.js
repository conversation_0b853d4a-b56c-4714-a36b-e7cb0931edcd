const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    await mongoose.connect('mongodb+srv://rupam:<EMAIL>/MFS_Database?retryWrites=true&w=majority&appName=Cluster0', {
    // await mongoose.connect('mongodb+srv://pavani:<EMAIL>/MFS_Data?retryWrites=true&w=majority&appName=Cluster0', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (err) {
    console.error('Error connecting to MongoDB:', err);
  }
};

module.exports = connectDB;

