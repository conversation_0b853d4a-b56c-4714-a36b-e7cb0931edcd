const CeremonyModel = require("../../models/CeremonyModel");
const crypto = require('crypto');
const ScannerLinkData = require("../VendorModel/ScannerLinkData");
const BookingModel = require("../../models/BookingModel");


// Fetch Events and Tickets
const FeatchVendorEvents = async (req, res) => {
  const { vendor_id } = req.params
  try {
    const events = await CeremonyModel.find({ eventCreatedBy: vendor_id }).populate('ticket');
    const tickets = events.filter(e => e.Addon && e.Addon.length > 0);
    if (tickets.length > 0) {
      res.status(200).send(tickets);
    } else {
      res.status(201).send({ message: "No events are available" });
    }
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
};

const GenerateScannerLink = async (req, res) => {
  try {
    const password = crypto.randomBytes(4).toString('hex').toUpperCase();
    const uniqueKey = Math.floor(100 + Math.random() * 900).toString();

    const eventName = await CeremonyModel.findOne({ _id: req.body.event_id });

    const event_idFound = await ScannerLinkData.findOne({ event_id: req.body.event_id })
    if (event_idFound) {
      return res.status(201).json({ message: "Link already Created for this event",eventName:eventName.event_name })
    }

    const response = new ScannerLinkData({
      event_id: req.body.event_id,
      login_id: req.body.loginId + "ts" + uniqueKey,
      password: password
    })
    await response.save();
    res.status(200).json({ message: "Scanner link generated", eventName });

  } catch (error) {
    res.status(500).send({ message: "Internal Server Error", error: error.message });
  }
};

const GateKeeperLogin = async (req, res) => {
  try {
    const { login_id, password } = req.body;
    const response = await ScannerLinkData.findOne({ login_id: login_id });
    if (response) {
      if (response.password === password) {
        res.status(200).send({
          message: "user Login sucessfully", user: {
            _id: response._id,
            user: response.login_id,
            event: response.event_id
          }
        });
      } else {
        res.status(401).send("Invalid Password");
      }
    } else {
      res.status(401).send("User not registered!");
    }

  } catch (error) {
    res.status(500).send("Something went wrong")
  }
}


const DashBoardData = async (req, res) => {
  try {
    const { vendor_id } = req.params;
    const events = await CeremonyModel.find({ eventCreatedBy: vendor_id }).populate('ticket');
    const data = events.filter(e => e.Addon && e.Addon.length > 0);

    let event_count = 0;
    let ticket_count = 0;
    let ticket_sell = 0;
    let revenue = 0;


    for (let i = 0; i < data.length; i++) {
      event_count += 1;

      // Check if tickets exist for the event
      if (data[i].ticket && data[i].ticket.length > 0) {
        for (let j = 0; j < data[i].ticket.length; j++) {
          ticket_count += data[i].ticket[j].total_Quantity;
         }
      }

      const soldData = await BookingModel.find({ eventDetails: data[i]._id });
      // console.log("SOLDDATA_DASHBORD : ", soldData)
      if (soldData && soldData.length > 0) {
        for (let k = 0; k < soldData.length; k++) {
          if (soldData[k].isPaid) {
            revenue += (soldData[k].amount) / 100;
            ticket_sell += soldData[k].bookedTicketQuantities;
          }
        }
      }
    }
    if (data && data.length > 0) {
      res.status(200).json({
        event_hosted: event_count,
        ticket_hosted: ticket_count,
        ticket_sold: ticket_sell,
        ticket_revenue: revenue
      });
    } else {
      res.status(404).json({ message: "No data found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).send({ message: "Something went wrong" });
  }
};

const DashBoardSoldData = async (req, res)=>{
  const {event_id} = req.params;
  try {
    const soldData = await BookingModel.find({ eventDetails: event_id });
    if(soldData && soldData.length > 0){
      res.status(200).json(soldData);
    }else{
      res.status(201).json({ message: "No data found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: "Something went wrong" });
  }
}


const UpdateValidateEntry = async (req, res) => {
  try {

    const { id } = req.params;
    const { total_no_of_Guest, no_of_Guest_attended, attended_event_ticket_id } = req.body;


    const updateEntry = await ScannerLinkData.findByIdAndUpdate(
      id,
      {
        total_no_of_Guest, // ES6 shorthand
        no_of_Guest_attended, // ES6 shorthand
        $push: { attended_event_ticket_id }, // Push the ticket ID to the array
        $inc: { total_no_of_Customer_attended: no_of_Guest_attended } // Increment by the number of attended guests
      },
      { new: true, upsert: false } // Return updated document without creating a new one if not found
    );
    const updatePendingGuestEntry = await BookingModel.findOneAndUpdate(
      { order_id: req.body.order_id },
      {
        $inc: { available_entry: -no_of_Guest_attended },
      },
      { new: true, upsert: false }
    );

    if (!updateEntry) {
      return res.status(404).json({ error: 'Entry not found' });
    }


    return res.status(200).json({ message: 'Entry updated successfully', data: updateEntry });

  } catch (error) {
    // Handle errors
    console.error(error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

const GetattendedCandidates = async (req, res) => {
  try {

    const { id } = req.params;


    const findEntry = await ScannerLinkData.findOne({event_id:id});

    if (findEntry) {
      return res.status(200).json({ attendedCandidates: findEntry.total_no_of_Customer_attended });
    } else {
      return res.status(200).json({ attendedCandidates: 0 });
    }
  } catch (error) {
    // Handle errors
    console.error(error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

const GetGeneratedScannerLink = async (req, res) => {
  const { id } = req.params;
  try {
    const eventName = await CeremonyModel.findOne({ _id : id });
    const getLinks = await ScannerLinkData.findOne({ event_id: id });
    if (getLinks) {
      res.status(200).json({ Links: getLinks, eventName:eventName.event_name })
    } else {
      res.status(201).json({ message: "No data found" });
    }

  } catch (error) {
    res.status(500).json({ message: "Server error" })
  }
}


const EventSellingData = async (req, res) => {
  try {
    const { event_id } = req.params;
    const data = await BookingModel.find({ eventDetails: event_id });
    if(data && data.length>0){
      res.status(200).json(data);
    }else{
      res.status(201).json({message:"No data found"})
    }
   
  } catch (err) {
    console.error(err);
    res.status(500).send({ message: "Something went wrong" });
  }
};

const UpdateVendorDetails =  async (req, res) => {
  const { _id } = req.body;

  if (!_id) {
      return res.status(400).json({
          message: "User ID is required",
          status: 400
      });
  }

  try {
      const updatedUser = await VendorUser.findByIdAndUpdate(
          _id,
          {
            Gender: req.body.gender,
          },
          { new: true } // Option to return the updated document
      );

      if (!updatedUser) {
          return res.status(404).json({
              message: "User not found",
              status: 404
          });
      }

      res.json({
          message: "User updated successfully!",
          status: 200,
          data: updatedUser
      });
  } catch (err) {
      res.status(400).json({
          message: err.message,
          status: 400
      });
  }
};

module.exports = {
  FeatchVendorEvents,
  GenerateScannerLink,
  GetGeneratedScannerLink,
  DashBoardData,
  GateKeeperLogin,
  UpdateValidateEntry,
  GetattendedCandidates,
  EventSellingData,
  DashBoardSoldData,
  UpdateVendorDetails
};