const mongoose = require("mongoose");

const CorporateEventSchema = new mongoose.Schema({
    corporate_event_id: { type: String, required: true, unique: true },
    company_name: { type: String, required: true },
    event_name: { type: String, required: true },
    poc_name: { type: String, required: true },
    poc_login_number: { type: String, required: true },
    event_start_date: { type: Date, required: true },
    event_start_Time: { type: String, required: true },
    event_end_date: { type: Date, required: true },
    event_end_Time: { type: String, required: true },
    event_city: { type: String, required: true },
    event_venue: { type: String },
    cover_image: { type: String },
    venueLayout: { type: String },
    event_description: { type: String, required: true },
    termsCondition: { type: String, required: true },
    guidelines: { type: String, required: true },
    artist_picture: { type: String },
    Event_lang: { type: String, required: true },
    Age_rest: { type: Number, required: true },
    custom_info: { type: String },
    isAllSession: { type: Boolean, default: false },
    eventCreatedBy: { type: String},
    tickets: [{ type: mongoose.Schema.Types.ObjectId, ref: "Ticket" }],
    empDataPasscode: {type : String}
}, { timestamps: true });

const CorporateEventModel = mongoose.model("CorporateEvent", CorporateEventSchema);
module.exports = CorporateEventModel;

