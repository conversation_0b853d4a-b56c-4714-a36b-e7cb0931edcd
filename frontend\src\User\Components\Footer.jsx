import React from "react";
import { Link, useNavigate } from "react-router-dom"
import { AiFillInstagram, AiFillFacebook } from "react-icons/ai";
import Logo from '../../Assets/LogoUser.png'
import "./Footer.css";

const Footer = () => {
    const userLoggedIn = JSON.parse(localStorage.getItem('userData'));
    const navigate = useNavigate();
    const handleListUrEvent = (e) => {
        e.preventDefault()
        navigate('/listyourevent')
    }

    const handleticketscan = ()=>{
        window.open('https://www.hostyourfrontseat.in', '_blank');
    }
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "start",
          alignItems: "center",
          width: "100%",
          backgroundColor: "#04092C",
        }}
      >
        <div className="footerDiv footer-container">
          <div className="footerLogo">
            <img className="footerlogoImg" src={Logo} alt="logo" />
          </div>
          <div className="footer-layout">
            <div className="footerCols">
              <div className="footerList">
                <div className="footerHeader">Customers</div>
                <div className="footerContents">
                  <Link className="link" to="/contact">
                    Customer Support
                  </Link>
                </div>
                <div className="footerContents">
                  {userLoggedIn ? (
                    <Link className="link" to="/yourtickets">
                      Your Tickets
                    </Link>
                  ) : (
                    <Link className="link" to="/login">
                      Your Tickets
                    </Link>
                  )}
                </div>
                <div className="footerContents">
                  {userLoggedIn ? (
                    <Link className="link" to="/userprofile">
                      Your Profile
                    </Link>
                  ) : (
                    <Link className="link" to="/login">
                      Your Profile
                    </Link>
                  )}
                </div>
              </div>
            </div>
            <div className="footerCols">
              <div className="footerList">
                <div className="footerHeader">Event Organisers</div>
                <div className="footerContents">
                  <Link className="link" to="#" onClick={handleListUrEvent}>
                    List Your Event
                  </Link>
                </div>
                <div className="footerContents">
                  <Link className="link" to="/contact">
                    Contact Us
                  </Link>
                </div>
                <div className="footerContents">
                  <Link className="link" to="#" onClick={handleticketscan}>
                    Ticket Scanner
                  </Link>
                </div>
                <div className="footerContents">
                  <Link className="link" to="/corporate/login">
                    Corporate Sign In
                  </Link>
                </div>
              </div>
            </div>
            <div className="footerCols">
              <div className="footerList">
                <div className="footerHeader">Company</div>
                <div className="footerContents">
                  <Link className="link" target="blank" to="/aboutus">
                    About Us
                  </Link>
                </div>
                <div className="footerContents">Blog</div>
                <div className="footerContents">
                  <Link className="link" target="blank" to="/policy">
                    Privacy Policy
                  </Link>
                </div>
                <div className="footerContents">
                  <Link className="link" target="blank" to="/terms-condition">
                    Terms & Conditions
                  </Link>
                </div>
              </div>
            </div>
            <div className="footerCols">
              <div className="footerList">
                <div className="footerHeader">Contact Us</div>
                <div className="footerContents"><EMAIL></div>
                <div className="footerContents">+91 *********0</div>
                {/* <div className="footerContents">107, Dighori, Umrer Road, Nagpur 440034 </div> */}
              </div>
            </div>
            <div className="footerCols footerColsm footer-social-lg-main">
              <div className="footerList">
                <div className="footerHeader">Socials</div>
                <div className="footerContentsm">
                  <Link
                    className="link"
                    target="blank"
                    to="https://www.instagram.com/myfrontseatin/"
                  >
                    <AiFillInstagram size={18} /> instagram
                  </Link>
                </div>
                <div className="footerContentsm facebook">
                  <AiFillFacebook size={18} /> facebook
                </div>
              </div>
            </div>
          </div>
          <div className="footerCols footerColsm footer-social-md-main">
            <div
              style={{
                width: "max-content",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                marginTop: "20px",
              }}
            >
              <span className="footerHeader">Socials</span>
              <div className="footer-social-md-ul">
                <div className="footerContentmd">
                  <Link
                    className="link"
                    target="blank"
                    to="https://www.instagram.com/myfrontseatin/"
                  >
                    <AiFillInstagram size={18} /> instagram
                  </Link>
                </div>
                <div className="footerContentmd facebook">
                  <AiFillFacebook size={18} /> facebook
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            background: "#04092C",
            color: "#F2F6F9",
            textAlign: "center",
            paddingBottom: 70,
          }}
        >
          <p className="footer-footer">&copy; 2024 myfrontseat.in</p>
        </div>
      </div>
    );
};

export default Footer;
