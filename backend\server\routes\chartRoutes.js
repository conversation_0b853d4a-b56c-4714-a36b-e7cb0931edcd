const express = require('express');
const {
    getSalesData,
    getRevenueData,
    getProfitData,
    getDashboardSummary
} = require('../controllers/chartController');

const chartRouter = express.Router();

/**
 * @route GET /api/charts/sales
 * @desc Get sales data for charts with time range filtering
 * @query {string} startDate - Start date (ISO string, optional)
 * @query {string} endDate - End date (ISO string, optional)
 * @query {string} period - Aggregation period: hourly, daily, weekly, monthly, yearly (default: daily)
 * @query {string} createdBy - Filter by event creator ID (vendor ID, optional)
 * @access Public (add authentication middleware as needed)
 */
chartRouter.get('/sales', getSalesData);

/**
 * @route GET /api/charts/revenue
 * @desc Get revenue data for charts with time range filtering
 * @query {string} startDate - Start date (ISO string, optional)
 * @query {string} endDate - End date (ISO string, optional)
 * @query {string} period - Aggregation period: hourly, daily, weekly, monthly, yearly (default: daily)
 * @query {string} createdBy - Filter by event creator ID (vendor ID, optional)
 * @access Public (add authentication middleware as needed)
 */
chartRouter.get('/revenue', getRevenueData);

/**
 * @route GET /api/charts/profit
 * @desc Get profit data for charts with time range filtering
 * @query {string} startDate - Start date (ISO string, optional)
 * @query {string} endDate - End date (ISO string, optional)
 * @query {string} period - Aggregation period: hourly, daily, weekly, monthly, yearly (default: daily)
 * @query {string} createdBy - Filter by event creator ID (vendor ID, optional)
 * @access Public (add authentication middleware as needed)
 */
chartRouter.get('/profit', getProfitData);

/**
 * @route GET /api/charts/summary
 * @desc Get dashboard summary with key metrics and period-over-period comparison
 * @query {string} startDate - Start date (ISO string, optional)
 * @query {string} endDate - End date (ISO string, optional)
 * @query {string} createdBy - Filter by event creator ID (vendor ID, optional)
 * @access Public (add authentication middleware as needed)
 */
chartRouter.get('/summary', getDashboardSummary);

module.exports = chartRouter;
