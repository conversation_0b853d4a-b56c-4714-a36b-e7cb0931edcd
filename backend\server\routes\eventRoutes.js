const express = require('express');
const router = express.Router();
const Event = require('../models/Event');
const { CreateNewEvent, AdditionalData,
  CitiesName, FeatchEvents,
  StoreTicket, StoreAddOn,
  FeatchEventDetail, SearchCityName,
  FeatchEventsByCity, InvoiceDetails, DeleteEventRecord,
  EventCategory, FeatchEventByCategory, UpdateEventDetail,
  UpdateAdditionalChanges, UpdatesetIsAllSession} = require('../controllers/eventController');
const { SearchController } = require('../controllers/searchController');


router.get('/getCards', async (req, res) => {
  try {
    const cards = await Event.find();
    console.log('Retrieved cards:', cards);
    res.status(200).json(cards);
  } catch (error) {
    console.error('Error fetching card data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// with ID
router.get('/getCardswithID/:id', async (req, res) => {
  try {
    const id = req.params.id;
    const cards = await Event.find({ _id: id });
    console.log('Retrieved cards:', cards);
    res.status(200).json(cards);
  } catch (error) {
    console.error('Error fetching card data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});


// Route to handle event creation
router.post('/eventDetails', async (req, res) => {
  try {
    console.log("Received event details:", req.body);  // Log the incoming data

    const {
      imageUrl, date, title, address, price, userID, isPromoted, eventName, eventCategory, isPublic,
      startsDate, startsTime, endsDate, endsTime, showEndTime, ECity, EVenue, Latitude, Longitude,
      Description, eventGenre, eventLanguage, artistPerformer, ageRestriction, artistInstagram,
      artistSpotify, artsitPicture, ArtistImg, eventGallery, CustomInformation
    } = req.body;

    const newEvent = new Event({
      imageUrl, date, title, address, price, userID, isPromoted, eventName, eventCategory, isPublic,
      startsDate, startsTime, endsDate, endsTime, showEndTime, ECity, EVenue, Latitude, Longitude,
      Description, eventGenre, eventLanguage, artistPerformer, ageRestriction, artistInstagram,
      artistSpotify, artsitPicture, ArtistImg, eventGallery, CustomInformation
    });

    console.log("Saving event to the database...");
    await newEvent.save();
    console.log("Event saved successfully.");

    res.status(201).json({ message: "Event details saved successfully!" });
  } catch (error) {
    console.error("Error saving event details:", error);
    res.status(500).json({ message: "Internal Server Error" });
  }
});

router.post('/addevent', CreateNewEvent);
router.put('/addeventticket', StoreTicket);
router.put('/addeventaddon', StoreAddOn);
router.post('/addon', AdditionalData);
router.post('/updateaddon/:addon_id', UpdateAdditionalChanges);
router.get('/city', CitiesName);
router.get('/searchcity', SearchCityName);
router.get('/events', FeatchEvents);
router.get('/events/:city', FeatchEventsByCity);
router.get('/eventdetail/:id', FeatchEventDetail);
router.put('/updateaddevent/:event_id', UpdateEventDetail);
router.get('/invoice/:id', InvoiceDetails);
router.get('/search', SearchController);
router.get('/category', EventCategory);
router.get('/category/:category', FeatchEventByCategory);
router.put('/updatesetIsAllSession/:id', UpdatesetIsAllSession);
router.delete('/delete-event-record/:event_id', DeleteEventRecord);





module.exports = router;


