{"name": "backend", "version": "1.0.0", "description": "", "main": "server/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/storage": "^7.15.0", "axios": "^1.7.7", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.19.2", "firebase-admin": "^13.0.2", "moment": "^2.30.1", "mongoose": "^8.2.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "nodemon": "^3.1.4", "number-to-words": "^1.2.4", "puppeteer": "^23.6.1", "qrcode": "^1.5.4", "razorpay": "^2.9.4", "sharp": "^0.33.5", "svg-to-img": "^2.0.9", "xlsx": "^0.18.5"}}