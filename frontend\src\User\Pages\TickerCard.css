.ticket-cardWrap {
  width: 100%;

  color: #fff;
  font-family: sans-serif;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  border-radius: 16px;
}
#ticket-card-large-view {
  display: block;
}
#ticket-card-small-view {
  display: none;
}
.border-dashed {
  border-style: dashed !important;
  border-width: 1px !important;
  border-color: #c0c0c0 !important;
}
.ticket-card-details {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  color: #000;
  border-radius: 10px;
  padding-inline: 1rem;
  font-size: small;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  font-weight: 500;
}
.ticket-card-image {
  width: 200px !important;
  height: 240px !important;
  margin-right: 1rem;
  border-radius: 16px;
  object-fit: cover;
}

.ticket-card {
  background: rgb(255, 255, 255);

  float: left;
  position: relative;
  padding: 1em;
}

.ticket-cardLeft {
  border-top-left-radius: 16px;
}
.ticketStatus-paid {
  padding: 1rem;
  background: #00ac07;
  text-align: center;
  color: white;
  font-weight: 500;
}

.ticketStatus-unpaid {
  padding: 1rem;
  background: #e09900;
  text-align: center;
  color: white;
  font-weight: 500;
}
.ticket-cardRight {
  border-left: 0.18em dashed #fff;
  border-top-right-radius: 16px;

  &:before,
  &:after {
    content: "";
    position: absolute;
    display: block;
    width: 26px;
    height: 26px;
    background: #fff;
    border-radius: 50%;
    left: -13px;
  }
  &:before {
    top: -13px;
    box-shadow: inset 0 -4px 4px rgba(226, 226, 226, 0.6);
    -webkit-mask-image: linear-gradient(to bottom, transparent 0%, black 50%);
    mask-image: linear-gradient(to bottom, transparent 0%, black 50%);
  }
  &:after {
    bottom: -53px;
    box-shadow: inset 0 6px 10px rgba(189, 189, 189, 0.6);
    -webkit-mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
    mask-image: linear-gradient(to bottom, black 50%, transparent 100%);
  }
}

.ticket-card-bottom-right {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  width: 100%;
  height: 40px;
  background-color: #2c9cf0;
}

.ticket-h1 {
  font-size: 1.1em;
  margin-top: 0;
  span {
    font-weight: normal;
  }
}

.ticket-title,
.ticket-name,
.ticket-seat,
.ticket-time {
  text-transform: uppercase;
  font-weight: normal;
  h2 {
    font-size: 0.9em;
    color: #525252;
    margin: 0;
  }
  span {
    font-size: 0.7em;
    color: #a2aeae;
  }
}

.ticket-title {
  margin: 2em 0 0 0;
}

.ticket-name,
.ticket-seat {
  margin: 0.7em 0 0 0;
}

.ticket-time {
  margin: 0.7em 0 0 1em;
}

.ticket-seat,
.ticket-time {
  float: left;
}

.ticket-eye {
  position: relative;
  width: 2em;
  height: 1.5em;
  background: #fff;
  margin: 0 auto;
  border-radius: 1em/0.6em;
  z-index: 1;
  &:before,
  &:after {
    content: "";
    display: block;
    position: absolute;
    border-radius: 50%;
    
  }
  &:before {
    width: 1em;
    height: 1em;
    background: red;
    z-index: 2;
    left: 8px;
    top: 4px;
  }
  &:after {
    width: 0.5em;
    height: 0.5em;
    background: #fff;
    z-index: 3;
    left: 12px;
    top: 8px;
  }
}

.ticket-number {
  text-align: center;
  text-transform: uppercase;
  h3 {
    color: red;
    margin: 0.9em 0 0 0;
    font-size: 2.5em;
  }
  span {
    display: block;
    color: #a2aeae;
  }
}

.ticket-barcode {
  height: 2em;
  width: 0;
  margin: 1.2em 0 0 0.8em;
  box-shadow: 1px 0 0 1px black, 5px 0 0 1px black, 10px 0 0 1px black,
    11px 0 0 1px black, 15px 0 0 1px black, 18px 0 0 1px black,
    22px 0 0 1px black, 23px 0 0 1px black, 26px 0 0 1px black,
    30px 0 0 1px black, 35px 0 0 1px black, 37px 0 0 1px black,
    41px 0 0 1px black, 44px 0 0 1px black, 47px 0 0 1px black,
    51px 0 0 1px black, 56px 0 0 1px black, 59px 0 0 1px black,
    64px 0 0 1px black, 68px 0 0 1px black, 72px 0 0 1px black,
    74px 0 0 1px black, 77px 0 0 1px black, 81px 0 0 1px black;
}
.ticket-card-qr-code-text {
  display: none;
}
@media screen and (max-width: 1024px) {
  .ticket-status-details {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  #ticket-card-large-view {
    display: none !important;
  }
  #ticket-card-small-view {
    display: block;
  }
  .ticket-cardWrap {
    flex-direction: column;
  }
  .ticket-cardLeft {
    border-radius: 0;
  }
  .ticket-cardRight {
    border-radius: 0;
  }
  .ticket-card-bottom-right {
    border-radius: 0;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;
  }
  .ticket-card-details {
    border-radius: 4px;
    margin-top: 0.4rem;
    margin-bottom: 0.1rem;
  }
  .ticket-cardRight {
    border-top: 0.18em dashed #fff;
    border-top-right-radius: 0;
    border-left: 0;

    &:before,
    &:after {
      content: "";
      position: absolute;
      display: block;
      width: 26px;
      height: 26px;
      background: #fff;
      border-radius: 50%;
      top: -13px;
      box-shadow: inset 0 0 10px #f8a100;
    }
    &:before {
      left: -13px;
      right: unset;
      display: none;
    }
    &:after {
      right: -13px;
      left: unset;
      display: none;
    }
  }
  .ticket-cardLeft {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    padding: 0;
  }
  .overlayticketScanner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .ticket-card-left-content {
    flex-direction: column !important;
    align-items: stretch !important;
    justify-content: start !important;
  }
  .ticket-card-image {
    display: none;
  }
  .ticket-card-left-subcontent {
    width: 100%;
    padding: 1rem;
  }
  .ticket-card-qr-code {
    max-width: 250px !important;
  }
  .ticket-card-qr-code-text {
    display: block;
    font-size: 0.7rem;
    font-weight: 500;
    color: #000;
    margin-bottom: 1rem;
  }
}
