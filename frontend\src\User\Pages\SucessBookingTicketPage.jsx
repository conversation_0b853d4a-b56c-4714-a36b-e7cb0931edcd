import React, { useEffect, useState } from "react";
import './SucessBookingTicketPage.css'
import NavBar from "../Components/NavBar";
import LocationModal from "../Components/LocationModal";
import { BsArrowLeft } from "react-icons/bs";
import TickImg from '../../Assets/tick.png'
import { useNavigate } from "react-router-dom";
import <PERSON><PERSON> from "lottie-react";
import successAnimation from "../../Assets/ticket-success.json";
import "../Components/SelectTickets.css";

const SucessBookingTicketPage = () => {
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const user = JSON.parse(localStorage.getItem("userData"));

  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    localStorage.setItem("userLocation", location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  const Navigate = useNavigate();
  const handleViewTicket = () => {
    Navigate("/yourtickets");
  };

  useEffect(() => {
    // Scroll to top when the component loads or data is done loading
    window.scrollTo({
      top: 0,
      behavior: "smooth", // Adds smooth scrolling
    });
  }, []);

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "start",
        alignItems: "center",
      }}
    >
      <NavBar
        userLocation={userLocation}
        onNext={() => setShowLocationModal(true)}
      />
      {/* Render Location component as a modal */}
      {showLocationModal && (
        <LocationModal
          onClose={() => setShowLocationModal(false)}
          onLocationChange={handleLocationChange}
        />
      )}

      {/* Content */}
      <div>
        <div className="ticketlist pt-0">
          <div
            className="d-flex flex-row justify-content-between align-items-center p-3"
            id="largedeviceview"
          >
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center">
              <span
                className="backarrowlink h-100 link-dark "
                onClick={() => Navigate(-1)}
              >
                <BsArrowLeft size={30} />
              </span>
            </div>
            <div className="col-10 col-10">
              <div className="d-flex flex-column justify-content-center">
                <span className="event-name">Ticket Details</span>
              </div>
            </div>
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center"></div>
          </div>
          <div id="smalldeviceview">
            <div className="row shadow-sm py-3">
              <div className="col-2 d-flex justify-content-center align-items-center">
                <span className=" link-dark" onClick={() => Navigate(-1)}>
                  <BsArrowLeft size={24} />
                </span>
              </div>
              <div className="col-8 d-flex flex-column justify-content-center align-items-center">
                <span className="event-name fw-semibold">Ticket Details</span>
              </div>
              <div className="col-2 d-flex justify-content-center align-items-center"></div>
            </div>
          </div>
          <div className="p-3">
            <div className="SuccessMssgDiv shadow text-center p-3">
              <div className="textdiv mt-2 px-3">
                We have sent tickets to <b>{user.email}</b> and{" "}
                <b>{user.phone}</b>. You can also find your tickets in My
                account &gt; My Front Seat
              </div>
              <div className="my-0 d-flex flex-column justify-content-center align-items-center">
                <Lottie
                  animationData={successAnimation}
                  loop={true}
                  autoplay={true}
                  style={{ width: 250, height: 250 }}
                />
              </div>
              <div>
                <p className="fontSizeSeatBook">Front Seats Booked!</p>
              </div>
              <button className="viewticketbtn" onClick={handleViewTicket}>
                View Tickets
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SucessBookingTicketPage;