.m-navbar{
    margin-top: 68px;
}

.h-navbar{
    height: calc(100vh - 68px);
}
@media screen and (max-width: 830px){
    .m-navbar{
        margin-top: 0px;
    }
    .h-navbar{
        min-height:auto;
        height: 100%;
    }
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 100, 0.5);
    border-radius: 10px;
    transition: background 0.3s ease;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 100, 100, 0.8);
  }
  
  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.5) transparent;
  }