const mongoose = require('mongoose');

const cardSchema = new mongoose.Schema({
  imageUrl: String,
  date: String,
  title: String,
  address: String,
  price: String,
  userID: String,
  isPromoted: {
    type: Boolean,
    default: false,
  },
  eventName: String,
  eventCategory: String,
  isPublic: Boolean,
  startsDate: String,
  startsTime: String,
  endsDate: String,
  endsTime: String,
  showEndTime: Boolean,
  ECity: String,
  EVenue: String,
  Latitude: String,
  Longitude: String,
  Description: String,
  eventGenre: String,
  eventLanguage: String,
  artistPerformer: String,
  ageRestriction: String,
  artistInstagram: String,
  artistSpotify: String,
  artsitPicture: String,
  ArtistImg: String,
  eventGallery: String,
  CustomInformation: String,
});

module.exports = mongoose.model('events', cardSchema);



