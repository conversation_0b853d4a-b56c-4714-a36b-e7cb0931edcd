// models/User.js

const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  _id: {
    type: String,
  },

  phone: {
    type: String,
    required: true,
    unique: true
  },
  
  firstName: String,
  lastName: String,
  email: {
    type: String,
    required: true
  },
  gender: String,
  dob: Date,
  address: String,
  city: String,
  state: String,
  pincode: Number,
  FCM_Token: String,
  createdAt: {
    type: Number,
    default: Date.now,
  }
});

const User = mongoose.model('User', userSchema);

module.exports = User;

