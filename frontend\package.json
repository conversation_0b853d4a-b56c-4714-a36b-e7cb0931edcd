{"name": "fe", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/material": "^5.16.5", "@popperjs/core": "^2.11.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.2", "country-flag-icons": "^1.5.13", "firebase": "^10.11.0", "firebase-admin": "^12.2.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lottie-react": "^2.4.1", "moment": "^2.30.1", "otp-input-react": "^0.3.0", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-bootstrap": "^2.10.9", "react-datepicker": "^7.2.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-icons": "^5.2.1", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.3", "react-redux": "^9.2.0", "react-router-dom": "^6.25.1", "react-scripts": "5.0.1", "react-spinners": "^0.14.1", "redux": "^5.0.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}