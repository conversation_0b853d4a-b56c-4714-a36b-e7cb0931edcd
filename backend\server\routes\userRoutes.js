const express = require('express');
const router = express.Router();
const User = require('../models/User');

// ... existing imports and code

router.post("/addUser", async (req, res) => {
  const data = new User({
    _id : req.body._id,
    phone:req.body.phone,
    firstName: req.body.firstName,
    lastName: req.body.lastName,
    email: req.body.email,
    gender: req.body.gender,
    dob: req.body.dob,
  });
  try {
    await data.save();
    res.json({
      message: "New User Added!",
      status: 200,
      data: data
    });
  } catch (err) {
    res.json({
      message: err,
      status: 400,
    });
  }
});

router.put("/updateUser", async (req, res) => {
  const { _id } = req.body;

  if (!_id) {
      return res.status(400).json({
          message: "User ID is required",
          status: 400
      });
  }

  try {
      const updatedUser = await User.findByIdAndUpdate(
          _id,
          {
              firstName: req.body.firstName,
              lastName: req.body.lastName,
              email: req.body.email,
              dob: req.body.dob,
              gender: req.body.gender,
              address: req.body.address,
              phone: req.body.phone,
              city: req.body.city,
              state: req.body.state,
              pincode: req.body.pincode
          },
          { new: true } // Option to return the updated document
      );

      if (!updatedUser) {
          return res.status(404).json({
              message: "User not found",
              status: 404
          });
      }

      res.json({
          message: "User updated successfully!",
          status: 200,
          data: updatedUser
      });
  } catch (err) {
      res.status(400).json({
          message: err.message,
          status: 400
      });
  }
});

router.get("/getUserWithID/:phone", async (req, res) => {
  try {
    const doc = await User.findOne({
      phone: req.params.phone,
    });
    res.status(200).json(doc);
  } catch (error) {
    console.error("Error fetching user data:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});




        

module.exports = router;
