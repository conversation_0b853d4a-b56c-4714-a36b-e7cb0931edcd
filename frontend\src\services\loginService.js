// src/services/loginService.js

export const loginUser = async ({ _id, mobileNum }) => {
    try {
      const response = await fetch('/api/user/addUser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ _id, mobileNum }),
      });
  
      const data = await response.json();
  
      if (response.ok) {
        return { success: true, message: data.message };
      } else {
        throw new Error(data.message || 'Login failed');
      }
    } catch (error) {
      return { success: false, message: error.message };
    }
  };
  
  