.singlebookedticketpage {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.singlebookedticket-container {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
}
.singlebookedticket-header {
  position: relative;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.singlebookedticket-info {
  font-size: 14px;
}

.singlebookedticket-info h2 {
  font-size: 16px;
  margin: 0 0 6px 0;
  font-weight: 700;
  color: #111;
}

.singlebookedticket-info p {
  margin: 4px 0;
  color: #555;
  font-weight: 400;
}

.singlebookedticket-actions {
  background: #f5f5f5;
  text-align: center;
  padding: 12px;
  font-size: 14px;
  color: #777;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
}

.singlebookedticket-middle {
  text-align: center;
  padding: 16px 12px;
  font-size: 14px;
  color: #333;
}

.singlebookedticket-middle h3 {
  font-size: 18px;
  margin: 8px 0;
  font-weight: 600;
}

.singlebookedticket-middle p {
  margin: 4px 0;
  font-weight: 400;
}

.singlebookedticket-qr-image {
  width: 180px;
  height: 180px;
  object-fit: contain;
  border-radius: 8px;
}

.singlebookedticket-note {
  background: #f6f6f6;
  text-align: center;
  padding: 10px 14px;
  font-size: 13px;
  color: #888;
  border-top: 1px solid #eee;
  cursor: pointer;
}

.singlebookedticket-footer {
  display: flex;
  justify-content: space-between;
  padding: 14px 16px;
  font-size: 14px;
  border-top: 1px solid #eaeaea;
  font-weight: 500;
  color: #fff;
  background-color: #2c9cf0;
}

.singlebookedticket-flip-container {
  /* perspective: 1000px; */
  width: 100%;
  max-width: 400px;
  margin: auto;
}

.singlebookedticket-flipper {
  position: relative;
  transition: transform 0.8s ease;
  transform-style: preserve-3d;
}

.singlebookedticket-flip-container.singlebookedticket-flip
  .singlebookedticket-flipper {
  transform: rotateY(180deg);
}

.singlebookedticket-front,
.singlebookedticket-back {
  backface-visibility: hidden;
  /* position: absolute; */
  /* top: 0; */
  /* left: 0; */
  width: 100%;
}

.singlebookedticket-back{
    position: absolute;
  top: 0;
  left: 0;
}

.singlebookedticket-front {
  z-index: 2;
}

.singlebookedticket-back {
  transform: rotateY(180deg);
  z-index: 1;
}

.singlebookedticket-back .singlebookedticket-back-content {
  padding: 20px;
  text-align: center;
}

.singlebookedticket-back .singlebookedticket-back-content h3 {
  margin-bottom: 10px;
  color: #111;
}

.singlebookedticket-back .singlebookedticket-back-content p {
  margin: 6px 0;
  color: #444;
  font-size: 14px;
}

.singlebookedticket-back .singlebookedticket-note {
  background-color: #f0f0f0;
  text-align: center;
  padding: 12px;
  font-size: 13px;
  color: #777;
  border-top: 1px solid #ddd;
}

.singlebookedticket-heading {
  
  font-size: 25px !important;
  color: #2c9cf0;
  padding:1rem;
}
