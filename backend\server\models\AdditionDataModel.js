const MongoDB = require('mongoose');


const AddOnSchema = new MongoDB.Schema({
    Event_genre: { type: String },
    Event_lang: { type: String,
        required: true
     },
    Age_restriction: { type: Number,
        required: true
     },
    Artists: [{
        artist_name: { type: String },
        artist_pic: { type: String }
    }],
    // Instagram_account: { type: String },
    // Spotify_account: { type: String },
    // picture: { type: String },
    Venuepicture: { type: String },
    // custom_info: { type: String },
});

const AdditionalDataModel = MongoDB.model("AdditionalData", AddOnSchema);
module.exports = AdditionalDataModel;