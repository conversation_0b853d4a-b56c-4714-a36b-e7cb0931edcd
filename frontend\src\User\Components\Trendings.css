.trendingeventDiv{
    max-width: 1280px;
  width:100%;
  padding: 1rem;
}

.trendingHeading {
    margin-top: 2rem;
    font-size: 20px;
    font-weight: 600;
  }
.trendingeventlist{
    width: 100%;
    display:grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    height: max-content;
}
.trendingCard{
    height: max-content !important;
    width:100%;
    border-radius: 1rem !important;
}

.trendingcardImg{
    width: 100%;
    border-radius: 1rem 0rem 1rem 0rem;
}
.trendingImg{
    width: 100%;
    border-radius: 1rem 1rem 0rem 0rem;
}
.trendingcardContent{
    /* min-height: 80px !important; */
    display: flex;
    align-items: center;
    padding: 0.5rem;
    color: #000;
}
.month, .date, .eventhead, .eventvenue,.eventprice{
    
}

.month{
    font-size: small !important;
    text-transform: uppercase;
} .date{
    font-size: 28.43px !important;
    margin-top: -10px;
} .eventhead{
    font-size: 16px !important;
} .eventvenue,.eventprice{
    font-size: 14px !important;
    
}
.soldOut{
    
    font-size: 14px;
    background: #FF0000;
    color: #fff;
    padding: 0.2rem;
    padding-inline: 0.5rem;
}

.statusAlert{
    
    font-size: 14px;
    background: #FFE600;
    color: #fff;
    padding: 0.2rem;
    padding-inline: 0.5rem;
}

@media screen and (max-width:1260px) {
    .trendingeventlist{
        grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    }
    
}

@media screen and (max-width:1024px) {
    .trendingeventlist{
        grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    }
}
@media screen and (max-width:768px) {
    .trendingeventlist{
        grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    }
}

@media screen and (max-width:640px) {

    .trendingHeading {
        
        margin-top: 0.5rem;
        font-size: 20px;
      }
    .trendingeventlist{
        grid-template-columns: repeat(1, 1fr);
    gap: 1rem;
    }
    .trendingcardImg{
        width: 100%;
        border-radius: 1rem 0rem 1rem 0rem;
    }
    .trendingImg{
        width: 100%;
        border-radius: 1rem 1rem 0rem 0rem;
    }
    .trendingcardContent{
        /* min-height: 99.42px !important; */
        display: flex;
        /* align-items: start; */
    }
    .month{
        padding-top: 0.2rem;
        /* font-size: 20px !important; */
        text-transform: uppercase;
    } .date{
        font-size: 25px !important;
        margin-top: -5px;
    } .eventhead{
        font-size: 18px !important;
    } .eventvenue,.eventprice{
        font-size: 12px !important;
        
    }
}
@media screen and (max-width:350px) {

    
    .trendingcardImg{
        height: 141.42px;
        border-radius: 1rem 0rem 1rem 0rem;
    }.trendingImg{
        width: 100%;
        border-radius: 1rem 1rem 0rem 0rem;
    }
    .trendingcardContent{
        min-height: 98.58px;
    }
    .month{
        padding-top: 0.2rem;
        font-size: 20px !important;
        text-transform: uppercase;
    } .date{
        font-size: 25px !important;
        margin-top: -5px;
    } .eventhead{
        font-size: 18px !important;
    } .eventvenue,.eventprice{
        font-size: 12px !important;
        
    }
}

/* Base shimmer styles */
.trending-skeleton-box {
    position: relative;
    background: #e0e0e0;
    overflow: hidden;
    border-radius: 4px;
    animation: shimmer 1.5s infinite linear;
  }
  
  .trending-skeleton-box::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150px;
    height: 100%;
    width: 150px;
    background: linear-gradient(to right, transparent, #f5f5f5, transparent);
    animation: loading 1.2s infinite;
  }
  
  @keyframes loading {
    0% {
      left: -150px;
    }
    100% {
      left: 100%;
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: -1000px 0;
    }
    100% {
      background-position: 1000px 0;
    }
  }
  .trending-skeleton-box {
    background: #e0e0e0;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    animation: shimmer 1.5s infinite linear;
  }
  .trending-skeleton-img {
    background: #e0e0e0;
  }
  /* Trending-specific placeholder styles */
  .trending-skeleton-trending-img {
    width: 100%;
    height: 180px;
    border-radius: 0.5rem 0.5rem 0 0;
  }
  
  .trending-skeleton-line {
    height: 16px;
    margin-bottom: 0.5rem;
  }
  
  .trending-skeleton-date-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
  
  .trending-skeleton-date-line {
    width: 20px;
    height: 10px;
    margin-top: 5px;
    border-radius: 4px;
  }