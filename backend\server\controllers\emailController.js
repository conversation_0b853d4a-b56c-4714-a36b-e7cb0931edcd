const puppeteer = require('puppeteer');
const nodemailer = require('nodemailer');
const ejs = require('ejs');
const fs = require('fs');
const QRCode = require('qrcode');
const { toWords } = require('number-to-words');
const BookingModel = require('../models/BookingModel');
const VendorUser = require('../VendorDatabase/VendorModel/VendorUser');
const StateModel = require('../models/StateDataModel');

// Import `fetch` dynamically if node-fetch is an ESM
const loadFetch = async () => {
  const fetchModule = await import('node-fetch');
  return fetchModule.default;
};


const formatTimespanDate = (timestamp) => {
  const date = new Date(timestamp);

  const options = {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  };

  return date.toLocaleString("en-US", options);
};

const encodeBase64 = async (filePath) => {
  const fetch = await loadFetch(); // Load fetch dynamically
  if (filePath.startsWith("http")) {
    const response = await fetch(filePath);
    const buffer = await response.buffer();
    return buffer.toString("base64");
  } else {
    const fileData = fs.readFileSync(filePath);
    return fileData.toString("base64");
  }
};

const fetchEjsTemplate = async (url) => {
  const fetch = await loadFetch(); // Load fetch dynamically
  const response = await fetch(url);
  return response.text();
};

function formatAmountInWords(amount) {
  const [rupees, paise] = amount.toFixed(2).split('.');

  const rupeeWords = toWords(Number(rupees));
  const paiseWords = toWords(Number(paise));

  return `${capitalizeWords(rupeeWords)} Rupees and ${capitalizeWords(paiseWords)} Paise Only`;
}

function capitalizeWords(str) {
  return str.replace(/\b\w/g, char => char.toUpperCase());
}

function formatEventDate(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString('en-US', { month: 'short' }).toUpperCase();
  const year = `'${String(date.getFullYear()).slice(-2)}`;
  return `${day} ${month} ${year}`;
}

const SendMail = async (req, res) => {
  try {
    console.log("Fetching ticket details...");
    const ticket = await BookingModel.findOne({ order_id: req.body.orderId });

    if (!ticket) {
      return res.status(404).send("Ticket not found.");
    }

    // let invoiceId = "MFSGST00000";
    // const lastNumber = parseInt(invoiceId.substring(6));
    // const newIdNumber = lastNumber + 1;
    // invoiceId = `MFS${String(newIdNumber).padStart(5, "0")}`;

    // console.log("Invoice ID:", invoiceId);

    let invoiceId = `MFS${Date.now()}`;

    const baseUrl =
      process.env.RAILWAY_ENVIRONMENT_NAME === "production"
        ? "https://my-front-seat-backend-production-3e06.up.railway.app"
        : "http://localhost:3001";

    const antsLogoPath = `${baseUrl}/Ejs/antsLogo.png`;
    const mfsLogoPath = `${baseUrl}/Ejs/MFSLogo.png`;
    const SignPath = `${baseUrl}/Ejs/sign.png`;

    console.log("Encoding images to base64...");
    const antsLogoBase64 = await encodeBase64(antsLogoPath);
    const mfsLogoBase64 = await encodeBase64(mfsLogoPath);
    const SignPathBase64 = await encodeBase64(SignPath);

    const bookingFeeInWords = formatAmountInWords(Number(req.body.bookingfee));

    const Organizer = await VendorUser.findById(req.body.vendor_id);

    const eventCity = req.body.event_city.trim();

    console.log("Processing event_city:", eventCity);

    const state = await StateModel.findOne({ City: eventCity });

    console.log("STATE  : ", state);

    const invoiceData = {
      invoiceNo: invoiceId,
      date: ticket.eventreserveDate,
      dateofBooking: formatTimespanDate(ticket.createdAt),
      order_id: ticket.order_id,
      UserName: req.body.userName,
      Email: req.body.email,
      eventName: req.body.event_name,
      basicRate: req.body.basicRate,
      totalBasicPrice: req.body.totalBasicPrice,
      booking_fee: req.body.bookingfee,
      ticketQuantity: ticket.bookedTicketQuantities,
      GST: req.body.gst,
      state: state.State || " ",
      stateCode: state.GSTIN_Code || " ",
      phoneNo: req.body.phone_no,
      amount: req.body.ticket_Price,
      transaction: ticket.payment_id || " ",
      payment_mode: ticket.payment_mode || " ",
      paid_amount: ticket.amount / 100,
      antsLogoBase64,
      mfsLogoBase64,
      SignPathBase64,
      bookingFeeInWords,
      Organizer_Name: Organizer.name,
    };

    console.log("Rendering invoice template...");
    const invoiceHtml = await fetchEjsTemplate(
      `${baseUrl}/invoice/Invoice.ejs`
    ).then((template) => ejs.render(template, invoiceData));

    console.log("Generating PDF with Puppeteer...");
    const browser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();
    await page.setContent(invoiceHtml);
    const pdfBuffer = await page.pdf({
      width: "250mm", // Increased width from default A4 (210mm)
      height: "297mm",
      margin: {
        top: "10mm",
        bottom: "10mm",
        left: "3mm",
        right: "3mm",
      },
    });
    await browser.close();

    const transporter = nodemailer.createTransport({
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: "ldzo fyqf ddnz nunr", // Use an App Password if using Gmail
      },
    });

    console.log("Generating QR Code...");
    const qrCodeData = `${ticket.tickets[0]}_${req.body.orderId}`;
    const qrCodeSVG = await QRCode.toString(qrCodeData, { type: "svg" });

    console.log("Preparing ticket data...");
    const ticketData = {
      cover_image: `${baseUrl}/uploads/${req.body.cover_image}`,
      order_id: req.body.orderId,
      ticket_id: ticket.tickets[0],
      amount: (ticket.amount / 100).toLocaleString("en-IN", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }),
      eventName: req.body.event_name,
      bookings: ticket.bookedTicketQuantities,
      ticketName: req.body.ticketName,
      venue: req.body.event_venue + req.body.event_city,
      time: req.body.event_time,
      date: formatEventDate(ticket.eventreserveDate),
      status: ticket.isPaid ? "Amount paid" : "Free",
      QRCodeSVG: qrCodeSVG,
      UserName: req.body.userName,
    };

    console.log("Rendering ticket template...");
    const ticketHtml = await fetchEjsTemplate(
      `${baseUrl}/invoice/ticketview.ejs`
    ).then((template) => ejs.render(template, ticketData));

    console.log("Generating ticket image with Puppeteer...");
    const ticketBrowser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const ticketPage = await ticketBrowser.newPage();
    await ticketPage.setContent(ticketHtml);
    const ticketImageBuffer = await ticketPage.screenshot({ type: "png" });
    await ticketBrowser.close();

    const emailHtml = `
      <div>
        <h3>Hi ${req.body.userName},</h3>
        <p>Thank you for booking with <b>My Front Seat!</b> Your tickets for ${req.body.event_name} are confirmed. Below are your ticket details:</p>
        <h5>Event Details:</h5>
        <ul>
          <li><b>Event: ${req.body.event_name}</b></li>
          <li><b>Date: ${req.body.event_date}</b></li>
          <li><b>Time: ${req.body.event_time}</b></li>
          <li><b>Venue: ${req.body.event_venue}</b></li>
          <li><b>Seats: ${ticket.bookedTicketQuantities}</b></li>
        </ul>
        <p>Access tickets: ${req.body.ticket_link}</p>
        <p>Please login with: <b>${req.body.phone_no}</b></p>
        <p>Please present the attached <b>QR code(s)</b> at the event entrance for quick and easy entry.</p>
        <span><b>Need Help</b></span>
        <p>If you have any questions, please contact support at <b>+91 9226773937, <EMAIL></b></p>
        <span><b>Reminder:</b></span>
        <ul>
          <li>Plan to arrive at least 60 minutes before the event start time.</li>
          <li>Check any event-specific guidelines on our website www.myfrontseat.in.</li>
        </ul>
        <p>Thank you for choosing <b>My Front Seat</b>. We look forward to seeing you at the event!</p>
      </div>
    `;

    console.log("Sending email...");
    await transporter.sendMail({
      from: '"My Front Seat" <<EMAIL>>',
      to: req.body.email,
      subject: `Your Ticket For ${req.body.event_name}`,
      text: "View Your Ticket",
      html: emailHtml,
      attachments: [
        {
          filename: `Invoice_${invoiceId}.pdf`,
          content: pdfBuffer,
          contentType: "application/pdf",
        },
        {
          filename: "ticket.png",
          content: ticketImageBuffer,
          contentType: "image/png",
        },
      ],
    });

    console.log("Mail sent successfully.");
    res.send("Mail sent successfully with invoice and ticket image");
  } catch (error) {
    console.error("Error in SendMail:", error);
    res.status(500).send("Failed to send mail: " + error.message);
  }
};


const DownloadAgreement = async (req, res) => {
  const agreementData = {
    Name_of_Organiser: req.body.Name_of_Organiser,
    Address_of_Organiser_Company: req.body.address,
    Email_of_Organiser: req.body.email,
    Address_of_Organiser: req.body.address,
    GST: req.body.gst,
    panNo: req.body.pan,
    bankName: req.body.bankname,
    account_type: req.body.acc_type,
    account_number: req.body.acc_no,
    accountHolderName: req.body.holder_name,
    IFSC: req.body.ifsc,
    date: new Date().toLocaleDateString('en-GB').replace(/\//g, '-')
  };
  try {
    const baseUrl = process.env.RAILWAY_ENVIRONMENT_NAME === 'production'
      ? 'https://my-front-seat-backend-production-3e06.up.railway.app'
      : 'http://localhost:3001';

    const agreementHtml = await fetchEjsTemplate(`${baseUrl}/invoice/Agreement.ejs`)
      .then((template) => ejs.render(template, agreementData));

    console.log("Generating PDF with Puppeteer...");
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(agreementHtml, { waitUntil: "domcontentloaded" });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '15mm',
        bottom: '10mm',
        left: '5mm',
        right: '10mm',
      },
    });

    await browser.close();

    // Set headers and send PDF as a response
    res.setHeader("Content-Disposition", 'attachment; filename="Agreement.pdf"');
    res.setHeader("Content-Type", "application/pdf");
    res.end(pdfBuffer);  // Explicitly end the response
  } catch (error) {
    console.error('Error generating agreement PDF:', error);
    res.status(500).send('Failed to generate PDF');
  }
};

module.exports = {
  SendMail,
  DownloadAgreement
};
