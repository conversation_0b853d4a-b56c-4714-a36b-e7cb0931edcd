import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_URL } from '../../config.js';
import './BrowseByCategory.css';
import CategoryImages from '../../Assets/CategoryImages.js'
import { IoChevronBackOutline, IoChevronForwardOutline } from "react-icons/io5";

function BrowseByCategory() {
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [isLoading, setIsLoading] = useState(true);

  const handleEventCategory = (cat) => {
    navigate(`/category/${cat}`);
  };

  useEffect(() => {
    const updateItemsPerPage = () => {
      if (window.innerWidth < 560) {
        setItemsPerPage(3);
      }
      else if (window.innerWidth < 915) {
        setItemsPerPage(3);
      } else {
        setItemsPerPage(4);
      }
    };

    updateItemsPerPage();
    window.addEventListener('resize', updateItemsPerPage);

    return () => {
      window.removeEventListener('resize', updateItemsPerPage);
    };
  }, []);

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      try {
        const listofCategory = await axios.get(`${API_URL}/api/category`);
        setCategories(listofCategory.data);
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
      }

    })();
  }, [setIsLoading, setCategories]);

  const nextSlide = () => {
    if (currentIndex + itemsPerPage < categories.length) {
      setCurrentIndex((prevIndex) => prevIndex + 2);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prevIndex) => prevIndex - 2);
    }
  };

  const visibleCategories = categories?.slice(currentIndex, currentIndex + itemsPerPage);

  return (
    <>
      <div className="category-list-main">
        <h4 className="browseHeading">Browse by Category</h4>
        <hr className="horizontalLine" />
        <div
          className="browseCarousel"
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "start",
            alignItems: "center",
          }}
        >
          <button
            onClick={prevSlide}
            disabled={currentIndex === 0}
            className="category-control-button"
          >
            <IoChevronBackOutline size={20} />
          </button>
          <div className="carousel-container">
            {isLoading
              ? [1, 2, 3, 4].slice(0, itemsPerPage).map((_, index) => (
                  <div
                    className="category-type-card browse-category-skeleton-box"
                    key={index}
                  >
                    <div className="browse-category-skeleton-img browse-category-skeleton-category-img mb-2"></div>
                    <div className="browse-category-skeleton-box browse-category-skeleton-line w-75 mx-auto"></div>
                  </div>
                ))
              : visibleCategories?.map((category, index) => (
                  <div
                    className=" category-type-card"
                    key={index}
                    onClick={() => handleEventCategory(category.category)}
                  >
                    <img
                      src={category.image}
                      alt={category.category}
                      style={{ width: "100%", height: "auto" }}
                    />
                    <span className="category-title">{category.category}</span>
                  </div>
                ))}
          </div>
          <button
            className="category-control-button"
            onClick={nextSlide}
            disabled={currentIndex + itemsPerPage >= categories.length}
          >
            <IoChevronForwardOutline className="nextIcon" size={20} />
          </button>
        </div>
      </div>
    </>
  );
}

export default BrowseByCategory;
