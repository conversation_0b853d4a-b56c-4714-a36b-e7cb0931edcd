import axios from "axios";
import { useEffect, useState } from "react";
import { IoSearch } from "react-icons/io5";
import { useDispatch } from "react-redux";
import { API_URL } from "../../config";
import "./LocationModal.css";
import mumbai from "../../Assets/cities/mumbai.png";
import delhi from "../../Assets/cities/delhi.png";
import lucknow from "../../Assets/cities/lucknow.png";
import jaipur from "../../Assets/cities/jaipur.png";
import hydrabad from "../../Assets/cities/hyderabad.png";
import kolkata from "../../Assets/cities/kolkata.png";
import bengaluru from "../../Assets/cities/bengaluru.png";
import chennai from "../../Assets/cities/chennai.png";
import ahmedabad from "../../Assets/cities/ahmedabad.png";
import pune from "../../Assets/cities/pune.png";

const POPULAR_CITIES = [
  {name: 'Mumbai',icon:mumbai},
  {name: 'Delhi',icon:delhi},
  {name: 'Lucknow',icon:lucknow},
  {name: 'Jaipur',icon:jaipur},
  {name: 'Hyderabad',icon:hydrabad},
  {name: 'Kolkata',icon:kolkata},
  {name: 'Bengaluru',icon:bengaluru},
  {name: 'Chennai',icon:chennai},
  {name: 'Ahmedabad',icon:ahmedabad},
  {name: 'Nagpur',icon:pune},
  
];

function LocationModal({ onClose, onLocationChange }) {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (query.length > 0) {
        try {
          const response = await axios.get(`${API_URL}/api/searchcity`, {
            params: { q: query },
          });
          setSuggestions(response.data);
        } catch (error) {
          console.error("Error fetching suggestions:", error);
        }
      } else {
        setSuggestions([]);
      }
    };

    const debounceTimeout = setTimeout(() => {
      fetchSuggestions();
    }, 300); // Debounce API calls for better performance

    return () => clearTimeout(debounceTimeout);
  }, [query]);

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      if (query) {
        // Only dispatch if there's a query
        onLocationChange(query);
        handleLocation(query); // Also set city in Redux
        onClose(); // Close the modal
      }
    }
  };

  const handleSuggestionClick = (city) => {
    setQuery(city); // Set the input field to the selected city
    handleLocation(city); // Dispatch the selected city to Redux
    onLocationChange(city); // Pass the selected city to parent
    setSuggestions([]); // Clear suggestions after selection
    onClose(); // Close the modal
  };

  const handleLocation = (city) => {
    dispatch({
      type: "SetCity",
      payload: { city: city },
    });
  };

  return (
    <div
      className="location-modal"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100vw",
        height: "100vh",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1050, // Ensure it's above other content
      }}
    >
      <div
        className="location-content"
        style={{
          backgroundColor: "white",
          borderRadius: "10px",
          padding: "20px",
          maxWidth: "768px", // Max width for larger screens
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          position: "relative",
        }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            top: "10px",
            right: "10px",
            background: "none",
            border: "none",
            fontSize: "1.5rem",
            cursor: "pointer",
            color: "#666",
          }}
        >
          &times;
        </button>

        <div style={{ textAlign: "start", marginBottom: 10, marginTop: 10 }}>
          <span
            style={{
              fontSize: "1.2rem",
              color: "#333",
              fontWeight: "600",
              marginLeft: 4,
            }}
          >
            Select Your City
          </span>
        </div>

        <div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              border: "1px solid #2C9CF0",
              borderRadius: "100px",
              marginTop: "10px",
              padding: "8px 15px", // Adjusted padding
            }}
          >
            <div
              style={{
                backgroundColor: "transparent",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
            >
              <IoSearch
                className="SearchInputIcon text-primary fs-4"
                style={{ color: "#2C9CF0" }}
              />
            </div>
            <input
              style={{
                border: "none",
                outline: "none",
                width: "100%",
                fontSize: "1rem",
                paddingLeft: "10px", // Padding after icon
              }}
              type="text"
              placeholder="Search your city"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
            />
          </div>

          {suggestions.length > 0 && (
            <div
              style={{
                marginTop: "10px",
                border: "1px solid #eee",
                borderRadius: "8px",
                boxShadow: "0 2px 5px rgba(0,0,0,0.1)",
                maxHeight: "200px",
                overflowY: "auto",
                backgroundColor: "#fff",
              }}
            >
              <ul style={{ listStyle: "none", padding: "0", margin: "0" }}>
                {suggestions.map((city, index) => (
                  <li
                    key={index}
                    onClick={() => handleSuggestionClick(city)}
                    style={{
                      padding: "10px 15px",
                      cursor: "pointer",
                      borderBottom:
                        index < suggestions.length - 1
                          ? "1px solid #eee"
                          : "none", // Add separator
                      backgroundColor: "#fff",
                      transition: "background-color 0.2s ease",
                      fontSize: "0.95rem",
                      color: "#333",
                    }}
                    onMouseEnter={(e) =>
                      (e.currentTarget.style.backgroundColor = "#f0f0f0")
                    }
                    onMouseLeave={(e) =>
                      (e.currentTarget.style.backgroundColor = "#fff")
                    }
                  >
                    {city}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Popular Cities Section */}
        <div style={{ marginTop: "20px" }}>
          <h3
            style={{
              fontSize: "1.1rem",
              color: "#333",
              fontWeight: "600",
              marginBottom: "15px",
              marginLeft: 4,
            }}
          >
            Popular Cities
          </h3>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fill, minmax(120px, 1fr))", // Responsive grid
              gap: "10px",
              justifyItems: "center", // Center items in their grid cells
            }}
          >
            {POPULAR_CITIES.map((city, index) => (
              <div
                key={index}
                onClick={() => handleSuggestionClick(city.name)} // Reusing the same handler
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: "10px",
                  // border: "1px solid #ddd",
                  borderRadius: "8px",
                  cursor: "pointer",
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
                  transition: "all 0.2s ease",
                  width: "100%", // Take full width of grid cell
                  boxSizing: "border-box", // Include padding and border in element's total width and height
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = "#2C9CF0";
                  e.currentTarget.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
                  e.currentTarget.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = "#ddd";
                  e.currentTarget.style.boxShadow =
                    "0 2px 4px rgba(0,0,0,0.05)";
                  e.currentTarget.style.transform = "translateY(0)";
                }}
              >
                {/* You can add icons here if you want, similar to BookMyShow */}
                {/* For example, a simple placeholder icon: */}
                <img
                  src={city.icon}
                  alt={city.name}
                  style={{
                    width: "50px",
                    height: "50px",
                    marginBottom: "5px",
                    filter: "grayscale(100%)",
                  }}
                />
                <span
                  style={{
                    fontSize: "0.9rem",
                    fontWeight: "500",
                    color: "#555",
                  }}
                >
                  {city.name}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* End of Popular Cities Section */}
      </div>
    </div>
  );
}

export default LocationModal;