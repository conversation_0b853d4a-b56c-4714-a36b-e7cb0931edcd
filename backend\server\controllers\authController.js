// // controllers/authController.js

// const User = require('../models/User');

// exports.sendOTP = async (req, res) => {
//   const { mobileNumber } = req.body;

//   try {
//     // Check if the user already exists
//     let user = await User.findOne({ mobileNumber });

//     // If the user doesn't exist, create a new user
//     if (!user) {
//       user = new User({ mobileNumber });
//       await user.save();
//     }

//     // Generate OTP
//     const otp = otpService.generateOTP(); // You'll need to implement this function in your otpService

//     // Save OTP in user record
//     user.otp = otp;
//     await user.save();

//     // Send OTP to the user (e.g., via SMS or email)
//     // You'll need to implement this functionality based on your application's requirements

//     res.status(200).json({ success: true, message: 'OTP sent successfully' });
//   } catch (error) {
//     console.error('Error sending OTP:', error);
//     res.status(500).json({ success: false, message: 'Server error' });
//   }
// };


// //VERIFY OTP
// exports.verifyOTP = async (req, res) => {
//   const { mobileNumber, otp } = req.body;

//   try {
//     // Retrieve the user with the provided mobile number
//     let user = await User.findOne({ mobileNumber });

//     // Check if the user exists
//     if (!user) {
//       return res.status(404).json({ success: false, message: 'User not found' });
//     }

//     // Check if the OTP matches the one stored in the user's record
//     if (user.otp !== otp) {
//       return res.status(400).json({ success: false, message: 'Invalid OTP' });
//     }

//     // Clear the OTP field in the user's record
//     user.otp = null;
//     await user.save();

//     res.status(200).json({ success: true, message: 'OTP verified successfully', user });
//   } catch (error) {
//     console.error('Error verifying OTP:', error);
//     res.status(500).json({ success: false, message: 'Server error' });
//   }
// };


