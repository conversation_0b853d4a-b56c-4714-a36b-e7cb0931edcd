import React, { useEffect, useState } from "react";
import "./Trendings.css";
import axios from "axios";
import { API_URL } from "../../config";
import { Link } from "react-router-dom";
import { MdEventBusy } from "react-icons/md";

function Trending({ userLocation }) {
  const [trendingEvent, SetTrendingEvent] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const Month = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const getEventsByCity = async () => {
    try {
      setIsLoading(true);
      const receivedevent = await axios.get(
        `${API_URL}/api/events/${userLocation}`
      );
      const sortedData = receivedevent.data.sort(
        (a, b) => new Date(b.event_starts_date) - new Date(a.event_starts_date)
      );
      console.log(sortedData);
      SetTrendingEvent(sortedData);
      setIsLoading(false);
    } catch (error) {
      SetTrendingEvent([]);
      setIsLoading(false);
      console.log("Error fetching images");
    }
  };

  useEffect(() => {
    getEventsByCity();
  }, [userLocation]);

  // Function to find the lowest price
  const lowestTicket = (ticketprices) => {
    let lowestPrice = ticketprices[0].ticket_price;
    for (let i = 1; i < ticketprices.length; i++) {
      if (
        ticketprices[i].ticket_price !== 0 &&
        lowestPrice > ticketprices[i].ticket_price
      ) {
        lowestPrice = ticketprices[i].ticket_price;
      }
    }
    return lowestPrice;
  };

  // Function to extract month from date string
  const getMonthFromDateString = (dateString) => {
    const date = new Date(dateString);
    return date.getMonth();
  };

  // Function to extract day from date string
  const getDayFromDateString = (dateString) => {
    const date = new Date(dateString);
    return date.getDate().toString().padStart(2, "0");
  };

  // Adjust card heights to match the tallest card
  useEffect(() => {
    const cards = document.querySelectorAll(".trendingCard");
    let maxHeight = 0;

    cards.forEach((card) => {
      const cardHeight = card.offsetHeight;
      if (cardHeight > maxHeight) {
        maxHeight = cardHeight;
      }
    });

    cards.forEach((card) => {
      card.style.height = `${maxHeight}px`;
    });
  }, [trendingEvent]);

  return (
    <>
      <div className="trendingeventDiv">
        <h4 className="trendingHeading">
          Trending Events {userLocation ? `in ${userLocation}` : ""}
        </h4>
        <hr className="horizontalLine" />
        {isLoading ? (
          <div className="trendingeventlist" style={{ marginTop: "1.5rem" }}>
            {[1, 2, 3].map((item, index) => {
              return (
                <div
                  className="card shadow-sm border-0 trendingCard skeleton-trending-card"
                  key={index}
                >
                  {/* Image Placeholder */}
                  <div className="trending-skeleton-img trending-skeleton-trending-img"></div>

                  <div className="card-body p-3">
                    <div className="d-flex align-items-start gap-3">
                      {/* Date Placeholder */}
                      <div className="d-flex flex-column align-items-center">
                        <div className="trending-skeleton-box trending-skeleton-date-circle mb-1"></div>
                        <div className="trending-skeleton-box trending-skeleton-date-line"></div>
                      </div>

                      {/* Event Details Placeholder */}
                      <div className="flex-grow-1">
                        <div className="trending-skeleton-box trending-skeleton-line mb-2 w-75"></div>
                        <div className="trending-skeleton-box trending-skeleton-line mb-2 w-50"></div>
                        <div className="trending-skeleton-box trending-skeleton-line w-25"></div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : trendingEvent.length > 0 ? (
          <div className="trendingeventlist" style={{ marginTop: "1.5rem" }}>
            {trendingEvent.map(
              (event, index) =>
                event.Addon &&
                event.Addon.length > 0 && (
                  <Link
                    style={{
                      width: "100%",
                      textDecoration: "none",
                      height: "100%",
                    }}
                    to={`/${event.event_city}/${event.event_name.replace(
                      /\s+/g,
                      "_"
                    )}/${event.unique_id}`}
                  >
                    <div className="trendingCard shadow-sm">
                      <div className="card-img-top trendingcardImg">
                        <img
                          src={`${event?.cover_image}`}
                          className="trendingImg"
                          alt="Event"
                        />
                      </div>
                      {event.ticket.every(
                        (tic) => tic.ticket_avability === 0
                      ) ? (
                        <div className="soldOut fw-semibold">Sold Out</div>
                      ) : new Date(
                          new Date(event.event_ends_date).setHours(0, 0, 0, 0)
                        ) < new Date(new Date().setHours(0, 0, 0, 0)) ? (
                        <div className="soldOut fw-semibold">Sold Out</div>
                      ) : (
                        <div className="statusAlert bg-success text-white fw-semibold">
                          Selling Fast
                        </div>
                      )}
                      <div className="trendingcardContent">
                        {event?.event_starts_date == event?.event_ends_date ? (
                          <div className="d-flex flex-column text-center me-lg-4 me-sm-2 me-4 ms-3">
                            <span className="month text-primary fw-bold">
                              {
                                Month[
                                  getMonthFromDateString(
                                    event?.event_starts_date
                                  )
                                ]
                              }
                            </span>
                            <span className="date fw-bold">
                              {getDayFromDateString(event?.event_starts_date)}
                            </span>
                          </div>
                        ) : (
                          <div
                            style={{ height: "100%" }}
                            className="d-flex flex-column justify-content-between align-items-start h-100 text-center me-lg-4 me-sm-2 me-4 ms-3"
                          >
                            <div className="d-flex flex-row text-center justify-content-start align-items-center">
                              <span className="month text-primary fw-bold fs-6">
                                {
                                  Month[
                                    getMonthFromDateString(
                                      event?.event_starts_date
                                    )
                                  ]
                                }
                              </span>
                              <span className="fw-bold fs-6 ms-1">
                                {getDayFromDateString(event?.event_starts_date)}
                              </span>
                            </div>
                            <span
                              className="text-center w-100"
                              style={{ marginTop: -4, marginBottom: -3 }}
                            >
                              to
                            </span>
                            <div className="d-flex flex-row text-center justify-content-start align-items-center">
                              <span className="month text-primary fw-bold fs-6">
                                {
                                  Month[
                                    getMonthFromDateString(
                                      event?.event_ends_date
                                    )
                                  ]
                                }
                              </span>
                              <span className="fw-bold fs-6 ms-1">
                                {getDayFromDateString(event?.event_ends_date)}
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="d-flex flex-column">
                          <div className="eventhead fw-bold">
                            {event?.event_name}
                          </div>
                          <div className="eventvenue text-secondary fw-normal">
                            {event?.event_venue} {event?.event_city}
                          </div>
                          <div className="eventprice text-secondary fw-normal">
                            ₹{" "}
                            {event.ticket.length > 0
                              ? lowestTicket(event.ticket)
                              : "0"}{" "}
                            onwards
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                )
            )}
          </div>
        ) : (
          <div
            style={{
              marginTop: "1.5rem",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              width: "100%",
              color: "#6A6A6A",
            }}
          >
            <MdEventBusy size={50} style={{ marginBottom: "1rem" }} />
            <p className="text-center fw-bold">No events in this category</p>
          </div>
        )}
      </div>
    </>
  );
}

export default Trending;
