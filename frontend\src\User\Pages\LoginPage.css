.login-modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  z-index: 9999;
  /* Ensure the modal stays on top */
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  /* Semi-transparent background */
}

.input-border-primary {
  border: 1px solid #aaaaaa !important;
}

.input-border-primary:focus {
  border: 1px solid #2C9CF0 !important;
}

/* .containerr {
  width: 100%;
  margin: auto;
  background-color: none;
  text-align: center;
  display: flex;
  justify-content: center;
  border-radius: 20px;
  border: 2px solid yellow !important;
} */
.containerSize{
  width: 50%;
}
.logincontainer {
  width: 90%;
  height: 426px;
  border-radius: 20px;
  margin: auto;
  background-color: white;
  text-align: center;
  position: relative;
  padding: 8%;
}

.book {
  display: inline-block;
  margin: 0;
  font-size: 21px;
  font-weight: 500;
  
}

.your-fs {
  display: inline-block;
  margin: 0;
  font-size: 21px;
  
  color: rgba(44, 156, 240, 1);
}

.login {
  text-align: start;
  width: 107px;
  height: 60px;
  font-size: 40px;
  font-weight: 500;
  
  margin-bottom: 18px;
  margin-top: 25px;
}

.close {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  /* Circular box */
  position: absolute;
  top: -10px;
  /* Adjust position */
  right: -10px;
  /* Adjust position */
  cursor: pointer;
  padding: 5px;
  /* Adjust padding as needed */
  border: solid 1.5px black;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  /* Ensure it's above other content */
  font-weight: 500;
  font-size: 25px;
  padding-bottom: 5px;
}

.div-num {
  justify-content: start;
}

.enter-number {
  
  display: flex;
  justify-content: start;
  height: 24px;
  font-weight: 500;
  font-size: 16px;
}

.input-number[type="tel"] {
  width: calc(100% - 40px);
  padding: 10px;
  margin-bottom: 30px;
  border-radius: 9px;
  box-sizing: border-box;
  width: 451px;
  height: 57px;
  border: solid 1.7px rgba(44, 156, 240, 1);
  font-size: 14px;
  
}

.getOtpBtn {
  width: 214px;
  height: 45px;
  padding: 10px;
  margin-bottom: 20px;
  border: none;
  border-radius: 5px;
  background-color: rgba(44, 156, 240, 1);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  font-size: 16px;
  
}

.resend-otp {
  display: flex;
  justify-content: start;
  margin-top: 20px;
}

.resend-text {
  font-size: 13px;
  
}

.isResendDisabled {
  color: rgb(122, 186, 235);
}

.isResendEnabled {
  color: rgb(6, 132, 228);
}

.countdown {
  margin-left: 0.3rem;
  font-size: small;
}

.seconds {
  font-size: 13px;
  
  margin-left: 5px;
  font-weight: 500;
}


.login-btn {
  width: 214px;
  height: 45px;
  padding: 10px;
  margin-bottom: 20px;
  margin-top: 33px;
  border: none;
  border-radius: 5px;
  background-color: rgba(44, 156, 240, 1);
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  font-size: 16px;
  
}

.input-otp {
  width: 451px;
  height: 57px;
  border: solid 1.7px rgba(44, 156, 240, 1);
  border-radius: 9px;

}

.closebtn {
  position: absolute;
  top: -10px;
  right: -10px;
  cursor: pointer;
  font-size: x-large;
}

.closebtn:hover {
  border: 2px solid gray;
  background-color: navy !important;
  color: white;
}

.btn {
  width: 40%;
  margin: 0rem 1rem;
}

@media screen and (max-width: 980px) {
  .login-modal {
    display: flex;
    justify-content: center;
    align-items: start;
    padding-top: 25%;
    position: fixed;
    z-index: 9999;
    /* Ensure the modal stays on top */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff
    /* Semi-transparent background */
  }
  .login-container {
    width: 120%;
    height: 406px;
    border-radius: 20px;
    margin: auto;
    margin-left: -10%;
    background-color: white;
    text-align: center;
    position: relative;
    padding: 8%;

  }

  .containerSize{
    width: 70%;
  }
}
@media screen and (max-width: 698px){
  .login-modal {
    display: flex;
    justify-content: center;
    align-items: start;
    padding-top: 25%;
    position: fixed;
    z-index: 9999;
    /* Ensure the modal stays on top */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff
    /* Semi-transparent background */
  }

  .logincontainer {
    width: 90%;
    height: 356px;
    border-radius: 20px;
    margin: auto;
    background-color: white;
    text-align: center;
    position: relative;
    padding: 8%;
  }
  .closebtn {
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
    font-size: xx-large;
  }
  .mobileinput{
    height: 50px;
    font-size: 14px !important;
  }
  .countryCode{
    font-size: 14px !important;
  }
  .enter-number {
    
    display: flex;
    justify-content: start;
    height: 20px;
    font-weight: 500;
    font-size: 16px;
  }
  
  .containerSize{
    width: 80%;
  }
  .login {
    text-align: start;
    width: 90px;
    height: 30px;
    font-size: 25px;
    font-weight: 500;
    
    margin-bottom: 18px;
    margin-top: 25px;
  }
}
@media screen and (max-width: 460px){
  .login-modal {
    display: flex;
    justify-content: center;
    align-items: start;
    padding-top: 25%;
    position: fixed;
    z-index: 9999;
    /* Ensure the modal stays on top */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff
    /* Semi-transparent background */
  }
  .logincontainer {
    width: 90%;
    height: 326px;
    border-radius: 20px;
    margin: auto;
    background-color: white;
    text-align: center;
    position: relative;
    padding: 8%;
  }
  .closebtn {
    position: absolute;
    top: -10px;
    right: -10px;
    cursor: pointer;
    font-size: xx-large;
  }
  .containerSize{
    width: 100%;
  }
  .mobileinput{
    height: 50px;
    font-size: 14px !important;
  }
  .countryCode{
    font-size: 14px !important;
  }
  .enter-number {
    
    display: flex;
    justify-content: start;
    height: 20px;
    font-weight: 500;
    font-size: 16px;
  }
  .countryflag{
    width: 70px;
  }
  .indian_flag{
    width:20px;
    margin-right: -5px !important;
  }
  
.login {
  text-align: start;
  width: 90px;
  height: 30px;
  font-size: 25px;
  font-weight: 500;
  
  margin-bottom: 18px;
  margin-top: 25px;
}
}