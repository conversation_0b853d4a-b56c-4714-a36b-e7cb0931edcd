// server.js

const express = require('express');
const cors = require('cors');
const connectDB = require('./config/database');
const userRoutes = require('./routes/userRoutes');
const eventRoutes = require('./routes/eventRoutes');
const insertCardData = require('./dataSeeder');
const productPageRedirectRouter = require('./routes/productPageRedirect');
const fileRouter = require('./routes/fileRoute');
const ticketRouter = require('./routes/ticketRoute');
const path = require('path');
const paymentRouter = require('./routes/PaymentRouter');
const vendorRouter = require('./VendorDatabase/VendorRouter/VendorUserRouter');
const adminRouter = require('./Admin/AdminRoutes');
const customerRouter = require('./routes/CustomerRoutes');
const corporateRouter = require('./routes/corporateEventRoute');
// require('./config/cronJobs');
const fs = require('fs');
const uploadsDir = path.join(__dirname, 'uploads');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}


const app = express();
const PORT = process.env.PORT || 3001;


// Middleware to parse JSON bodies
app.use(express.json());
app.use(cors());

// Connect to MongoDB
connectDB();

app.use('/api', eventRoutes);  // Use event routes
app.use('/products',productPageRedirectRouter);
app.use('/user', userRoutes);
app.use('/vendor', vendorRouter);
app.use('/image',fileRouter);
app.use('/ticket',ticketRouter);
app.use('/pay', paymentRouter);
app.use('/admin', adminRouter);
app.use('/support', customerRouter);
app.use('/corporate', corporateRouter);

app.use('/uploads', express.static(path.join(__dirname, '..', 'uploads')));
app.use('/invoice', express.static(path.join(__dirname, '..', 'invoice')));
app.use('/Ejs', express.static(path.join(__dirname, 'Ejs', 'Images')));


// Insert card data
// insertCardData();

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});



