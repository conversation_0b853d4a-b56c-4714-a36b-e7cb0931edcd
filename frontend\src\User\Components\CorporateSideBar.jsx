import React from "react";
import './SidebarComponent.css'
import { TbArrowLeftFromArc } from "react-icons/tb";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { PiGridFour } from "react-icons/pi";
import { VscChecklist, VscOrganization } from "react-icons/vsc";
import { AiOutlineDashboard } from "react-icons/ai";
import { BsQrCodeScan } from "react-icons/bs";
import { IoIosAddCircleOutline } from "react-icons/io";
import { TbTransactionRupee } from "react-icons/tb";
import { MdAirplaneTicket, MdOutlineManageHistory } from "react-icons/md";
import { HiArrowUpOnSquareStack } from "react-icons/hi2";
import { IoCreate, IoNotificationsCircleOutline } from "react-icons/io5";
import { GiPayMoney } from "react-icons/gi";
import { TiMessages } from "react-icons/ti";



const CorporateSideBar = () => {
    const navigate = useNavigate();
    const location = useLocation(); // to get the current location
    const user = JSON.parse(localStorage.getItem('corporateCompanyData'));

    const handleLogout = () => {
        localStorage.removeItem('corporateCompanyData');
        navigate('/corporate/login');
    };

    // Helper function to check if a route is active
    const isActive = (path) => location.pathname === path;

    return (
        <div className="col-lg-2 col-md-2 border-end" id="hideSidebar" style={{ minHeight: "100vh" }}>
            <div className={`mt-2 sidebar-list ${isActive(`/corporate/${user.company_name}/dashboard`) ? 'active' : ''}`}>
                <Link className='sidebar-link' to={`/corporate/${user.company_name}/dashboard`}>
                    <AiOutlineDashboard className='sidebar-icon' />
                    <span className='sidebar-tab'>Dashboard</span>
                </Link>
            </div>
            <div className={`sidebar-list ${isActive(`/admin/${user.company_name}/allevents`) ? 'active' : ''}`}>
                <Link className='sidebar-link' to={`/admin/${user.company_name}/allevents`}>
                    <PiGridFour className='sidebar-icon' />
                    <span className='sidebar-tab'>Your Events</span>
                </Link>
            </div>
          
            <div className={`sidebar-list ${isActive(`/admin/${user.company_name}/organisers`) ? 'active' : ''}`}>
                <Link className='sidebar-link' to={`/admin/${user.company_name}/organisers`} >
                    <VscOrganization  className='sidebar-icon' />
                    <span className='sidebar-tab'>Ticket Scanner</span>
                </Link>
            </div>
            <div className={`sidebar-list ${isActive(`/admin/${user.company_name}/buyers`) ? 'active' : ''}`}>
                <Link className='sidebar-link' to={`/admin/${user.company_name}/buyers`} >
                    <MdAirplaneTicket  className='sidebar-icon' />
                    <span className='sidebar-tab'>Create Event</span>
                </Link>
            </div>
            <div className={`sidebar-list ${isActive('/admin/create-corporate-event') ? 'active' : ''}`}>
                <Link className='sidebar-link' to='/corporate/login' onClick={handleLogout}>
                    <TiMessages    className='sidebar-icon' />
                    <span className='sidebar-tab'>Logout</span>
                </Link>
            </div>
        </div>
    );
}

export default CorporateSideBar;
