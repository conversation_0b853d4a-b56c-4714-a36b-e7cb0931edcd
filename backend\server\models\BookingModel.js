const MongoDB = require('mongoose');

const BookingSchema = new MongoDB.Schema({
    order_id:{
        type: String,
        required: true
    },
    payment_id: {
        type: String,
    },
    user:  { 
        type: String, 
        required: true
     },
     receipt:{
        type: String, 
     },
    tickets: [{
        type: MongoDB.Schema.Types.ObjectId,
        ref: "Ticket"
    }],
    eventDetails: {
        type: MongoDB.Schema.Types.ObjectId,
        ref: 'CeremonyModel',
        required: true,
    },
    isPaid: {
        type: Boolean,
        default: false
    },
    isfree : {
        type : Boolean,
        default: false
    },
    amount: {
        type: Number,
        default: 0
    },
    paid_Amount:{
        type: Number,
        default: 0
    },
    status:{
        type: String,
    },
    bookedTicketQuantities : {
        type: Number, defaultValue: 0
    },
    payment_mode:{
        type: String
    },
    eventreserveDate : {type: String},
    reserveeventcity : {type: String},
    available_entry:{
        type:Number,
        default: 0
    },
    createdAt: {
        type: Number,
        default: Date.now,
      },

});

const BookingModel = MongoDB.model("BookingData", BookingSchema);
module.exports = BookingModel;