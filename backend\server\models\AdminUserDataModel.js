// models/User.js

const mongoose = require('mongoose');

const AdminSchema = new mongoose.Schema({
    login_id: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true,
        unique: true
    },
    role: {
        type: String,
        required: true,
        defalut: "Admin"
    }
});

const Admin = mongoose.model('Admin', AdminSchema);

module.exports = Admin;

