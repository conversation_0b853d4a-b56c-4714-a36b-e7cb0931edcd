import axios from "axios";
import React, { useState } from "react";
import './CorporateUserLogin.css'
import { Button } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import SyncLoader from "react-spinners/SyncLoader";
import { API_URL } from "../config";

const CorporateUserLogin = () => {
    const [Email, setEmail] = useState("");
    const [Employee_Id, setEmployee_Id] = useState("");
    const [isResendDisabled, setIsResendDisabled] = useState(true);
    const navigate = useNavigate();

    const [loading, setLoading] = useState(false);

    const handleLogin = async (e) => {
        e.preventDefault();
        setLoading(true);
        try {
            const body = {
                Email, Employee_Id
            };
            const response = await axios.post(`${API_URL}/corporate/login`, body);
            if (response.status === 200) {
                    localStorage.setItem("Corporate_User", JSON.stringify(response.data.user));
                    navigate('/corporate/profile');
            }
        } catch (error) {
            alert("Invalid Credentials")
        } finally{
            setLoading(false);
        }
    }

    return (
        <>
            <div className="login-modal">
                <div className="w-50">
                    <div className="login-container shadow">
                        <h1 className="book">
                            <span className="your-fs"> Admin Login</span>
                        </h1>
                        <>
                            <form>
                                <div className="div-num mb-3">
                                    <p className="enter-number">Email Id:</p>
                                    <input
                                        className="form-control mobileinput py-3 border-primary"
                                        type="text"
                                        name="Email"
                                        placeholder="Enter your email id"
                                        value={Email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                    />
                                </div>
                                <div className="div-num mb-5">
                                    <p className="enter-number">Employee_Id:</p>
                                    <input
                                        className="form-control mobileinput py-3 border-primary"
                                        type="text"
                                        name="Employee_Id"
                                        placeholder="Enter your employee id"
                                        value={Employee_Id}
                                        onChange={(e) => setEmployee_Id(e.target.value)}
                                        required
                                    />
                                </div>
                                <Button onClick={handleLogin} className="btn getotpbtn btn-primary w-50" disabled={loading}>
                                    {loading ? <SyncLoader animation="border" color="#FFFF" size="10" speedMultiplier={1} margin={4} /> : "Login"}
                                </Button>
                            </form>
                        </>
                    </div>
                </div>
            </div>
        </>
    );
}

export default CorporateUserLogin; 