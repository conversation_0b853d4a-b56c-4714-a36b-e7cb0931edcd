import axios from "axios";
import "./SelectTickets.css";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { API_URL } from "../../config";
import { BsArrowLeft } from "react-icons/bs";
import { CiCircleMinus, CiCirclePlus } from "react-icons/ci";
import PuffLoader from "react-spinners/PuffLoader";
import SyncLoader from "react-spinners/SyncLoader";
import { SlArrowDown } from "react-icons/sl";
import "./SelectTickets.css";
import NavBar from "./NavBar";

function SelectCity() {
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [expandedCityId, setExpandedCityId] = useState(null);
  const [expandedVenueId, setExpandedVenueId] = useState(null);
  const user = JSON.parse(localStorage.getItem("userData"));
  const [cityList, setCityList] = useState([]);
  const [eventData, SetEventData] = useState([]);
  const [loading, setLoading] = useState(true); // Loading state added
  const [bookingQuantities, setBookingQuantities] = useState({});
  const [ticketbookedbyuser, setTicketbookedbyuser] = useState({});
  const [selectedcity, setSelectedcity] = useState(null);
  const [selectedcityVenue, setSelectedcityVenue] = useState(null);
  const [isProceeding, setIsProceeding] = useState(false);
  const Navigate = useNavigate();
  const location = useLocation();
  const [showLocationModal, setShowLocationModal] = useState(false);
  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  useEffect(() => {
    if (location.state && location.state.event) {
      SetEventData(location.state.event);
      setLoading(false);
    }

    console.log("EVENT", eventData);
  }, [location.state, eventData]);

  const handleNextProcess = () => {
    setLoading(true);
    if (
      eventData.isAllSession ||
      eventData.event_starts_date === eventData.event_ends_date
    ) {
      Navigate(
        `/ticketpage/${eventData.event_name.replace(/\s+/g, "_")}/${
          eventData.unique_id
        }`,
        {
          state: {
            date_for_booked_event: eventData.event_starts_date,
            selectedCity: selectedcity,
            selectedVenue: selectedcityVenue,
            event: eventData,
          },
        }
      );
      setLoading(false);
    } else {
      Navigate(
        `/dateselect/${eventData.event_name.replace(/\s+/g, "_")}/${
          eventData.unique_id
        }`,
        {
          state: {
            event: eventData,
            selectedCity: selectedcity,
            selectedVenue: selectedcityVenue,
          },
        }
      );
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day} ${month} '${year}`;
  };

  const handleCityClick = (city) => {
    // Toggle expand/collapse
    setExpandedCityId((prev) => (prev === city._id ? null : city._id));

    // Set selected city and venue
    setSelectedcity(city.event_city);
    setSelectedcityVenue(city.event_venue);
  };
  return (
    <>
      <div className="select-ticket">
        <div id="hideforSmallDevices">
          <NavBar
            userLocation={userLocation}
            onNext={() => setShowLocationModal(true)}
            disableMargin={true}
          />
        </div>
        <div className="ticketlist">
          <div
            className="d-flex flex-row justify-content-between align-items-center m-0"
            id="largedeviceview"
          >
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center">
              <span
                className="backarrowlink h-100 link-dark "
                onClick={() => Navigate(-1)}
              >
                <BsArrowLeft size={30} />
              </span>
            </div>
            <div className="col-10 col-10">
              <div className="d-flex flex-column justify-content-center">
                <span className="event-name">{eventData.event_name}</span>
                <span className="event-detail">
                  &nbsp; {formatDate(eventData.event_starts_date)}
                </span>
              </div>
            </div>
            <div className="col-1 col-1 flex flex-col justify-content-center align-content-center"></div>
          </div>
          <div id="smalldeviceview">
            <div className="d-flex flex-row align-items-center m-0 shadow-sm py-1">
              <div className="col-2 d-flex justify-content-center align-items-center">
                <span className=" link-dark" onClick={() => Navigate(-1)}>
                  <BsArrowLeft size={24} />
                </span>
              </div>
              <div className="col-8 d-flex flex-column justify-content-center align-items-center">
                <span className="event-name fw-semibold">
                  {eventData.event_name}
                </span>
                <span className="event-detail">
                  {formatDate(eventData.event_starts_date)} &nbsp;
                </span>
              </div>
              <div className="col-2 d-flex justify-content-center align-items-center"></div>
            </div>
          </div>
          {loading ? (
            <div
              className="d-flex justify-content-center align-items-center"
              style={{ height: "230px" }}
            >
              <PuffLoader size={28} loading={loading} />
            </div>
          ) : (
            <>
              <div className="pt-3">
                {eventData && eventData.multiCityArray.length > 0 ? (
                  <>
                    {eventData.multiCityArray.map((city) => (
                      <React.Fragment key={city._id}>
                        <div
                          className={`listofcity rounded shadow-sm p-2 transition-all `}
                          onClick={() => handleCityClick(city)}
                        >
                          <span className="fw-bold fs-6">
                            {city.event_city}
                          </span>
                          <span>
                            <SlArrowDown
                              size={16}
                              className={`me-1 ${
                                expandedCityId === city._id ? "rotate-180" : ""
                              }`}
                              style={{
                                transition: "transform 0.3s ease",
                                transform:
                                  expandedCityId === city._id
                                    ? "rotate(180deg)"
                                    : "rotate(0deg)",
                              }}
                            />
                          </span>
                        </div>
                        <div
                          className={`dropdown-container  shadow-sm rounded ${
                            expandedCityId === city._id
                              ? "expanded highlightSelection-city"
                              : ""
                          }`}
                        >
                          <div className="d-flex flex-column">
                            <span className="fw-semibold fs-6 p-2 pb-0">
                              {city.event_venue
                                ? city.event_venue + ", " + city.event_city
                                : city.event_city}
                            </span>
                            <span
                              className="fw-medium px-2 pb-2"
                              style={{ fontSize: "12px", fontWeight: 400 }}
                            >
                              {formatDate(city.event_date)}
                            </span>
                          </div>
                        </div>
                      </React.Fragment>
                    ))}
                  </>
                ) : (
                  <p>No City Available Currently</p>
                )}
              </div>
            </>
          )}
        </div>
        <div style={{ height: "200px" }}></div>
        {/* Footer Part */}
        <div className="footerPart py-2 mt-5" id="largedeviceview">
          <div className="footer-content">
            <button
              className={`booking-btn fs-5 rounded-3 text-white ${
                selectedcity && selectedcityVenue
                  ? "btnisEabled"
                  : "btnisDisabled"
              }`}
              disabled={!(selectedcity && selectedcityVenue)}
              onClick={handleNextProcess}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>

        <div className="smallfooterPart py-2" id="smalldeviceview">
          <div className="footer-content px-3">
            <button
              className={`btn-booking text-white ${
                selectedcity && selectedcityVenue
                  ? "btnisEabled"
                  : "btnisDisabled"
              }`}
              disabled={!(selectedcity && selectedcityVenue)}
              onClick={handleNextProcess}
            >
              {isProceeding ? (
                <SyncLoader
                  animation="border"
                  color="#FFFF"
                  size="7"
                  speedMultiplier={1}
                  margin={4}
                />
              ) : (
                "Proceed"
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default SelectCity;
