.booked-ticket-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow-y: hidden;
}

.booked-tickets-heading {
  font-size: 25px !important;
  color: #2c9cf0;
  border-bottom: 1px solid #2c9cf0;
  font-weight: 600;
  padding: 1rem;
}

.ticketStatus-paid {
  padding: 1rem;
  background: #00ac07;
  text-align: center;
  color: white;
  font-weight: 500;
}

.ticketStatus-unpaid {
  padding: 1rem;
  background: #e09900;
  text-align: center;
  color: white;
  font-weight: 500;
}

.ticketCard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ticket-container {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
}

.ticket-header {
  position: relative;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.ticket-info {
  font-size: 14px;
}

.ticket-info h2 {
  font-size: 16px;
  margin: 0 0 6px 0;
  font-weight: 700;
  color: #111;
}

.ticket-info p {
  margin: 4px 0;
  color: #555;
  font-weight: 400;
}

.pickup-label {
  position: absolute;
  top: 0;
  right: -28px;
  transform: rotate(90deg);
  background: #f1f1f1;
  color: #555;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 0 0 4px 4px;
}

.ticket-actions {
  background: #f5f5f5;
  text-align: center;
  padding: 12px;
  font-size: 14px;
  color: #777;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
}

.ticket-middle {
  text-align: center;
  padding: 16px 12px;
  font-size: 14px;
  color: #333;
}

.ticket-middle h3 {
  font-size: 18px;
  margin: 8px 0;
  font-weight: 600;
}

.ticket-middle p {
  margin: 4px 0;
  font-weight: 400;
}

.qr-image {
  width: 180px;
  height: 180px;
  object-fit: contain;
  border-radius: 8px;
}

.ticket-note {
  background: #f6f6f6;
  text-align: center;
  padding: 10px 14px;
  font-size: 13px;
  color: #888;
  border-top: 1px solid #eee;
}

.ticket-footer {
  display: flex;
  justify-content: space-between;
  padding: 14px 16px;
  font-size: 14px;
  border-top: 1px solid #eaeaea;
  font-weight: 500;
  color: #000;
}

.flip-container {
  perspective: 1000px;
  width: 100%;
  max-width: 400px;
  margin: auto;
}

.flipper {
  position: relative;
  transition: transform 0.8s ease;
  transform-style: preserve-3d;
}

.flip-container.flip .flipper {
  transform: rotateY(180deg);
}

.front,
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.front {
  z-index: 2;
}

.back {
  transform: rotateY(180deg);
  z-index: 1;
}

.back .back-content {
  padding: 20px;
  text-align: center;
}

.back .back-content h3 {
  margin-bottom: 10px;
  color: #111;
}

.back .back-content p {
  margin: 6px 0;
  color: #444;
  font-size: 14px;
}

.back .ticket-note {
  background-color: #f0f0f0;
  text-align: center;
  padding: 12px;
  font-size: 13px;
  color: #777;
  border-top: 1px solid #ddd;
}

@media (max-width: 768px) {
  .booked-tickets-heading {
    text-align: center;
  padding-top: 0.8rem;
  padding-bottom: 0.8rem;
  }

}