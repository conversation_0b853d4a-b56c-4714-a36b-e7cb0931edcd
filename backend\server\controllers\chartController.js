const BookingModel = require("../models/BookingModel");
const CeremonyModel = require("../models/CeremonyModel");
const TicketModel = require("../models/TicketModel");
const CorporateBookingModel = require("../models/CorporateBookingModel");

/**
 * Get sales data for charts with time range filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getSalesData = async (req, res) => {
  try {
    const { startDate, endDate, period = 'daily', createdBy } = req.query;
    
    // Set default date range if not provided (last 30 days)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // Ensure end date includes the full day
    end.setHours(23, 59, 59, 999);
    start.setHours(0, 0, 0, 0);

    // Build aggregation pipeline based on period
    let groupBy;
    let dateFormat;
    
    switch (period) {
      case 'hourly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } },
          hour: { $hour: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d %H:00";
        break;
      case 'weekly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          week: { $week: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-W%U";
        break;
      case 'monthly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m";
        break;
      case 'yearly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y";
        break;
      default: // daily
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d";
    }

    // Build match criteria for regular bookings
    let matchCriteria = {
      createdAt: {
        $gte: start.getTime(),
        $lte: end.getTime()
      },
      isPaid: true
    };

    // Regular bookings aggregation with optional creator filtering
    let regularSalesAggregation = [
      {
        $match: matchCriteria
      }
    ];

    // Add lookup and filtering by event creator if createdBy is provided
    if (createdBy) {
      regularSalesAggregation.push(
        {
          $lookup: {
            from: "ceremonymodels",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    regularSalesAggregation.push(
      {
        $group: {
          _id: groupBy,
          totalSales: { $sum: "$bookedTicketQuantities" },
          totalRevenue: { $sum: { $divide: ["$amount", 100] } },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const regularSales = await BookingModel.aggregate(regularSalesAggregation);

    // Corporate bookings aggregation with optional creator filtering
    let corporateSalesAggregation = [
      {
        $match: matchCriteria
      }
    ];

    // Add lookup and filtering by event creator if createdBy is provided
    if (createdBy) {
      corporateSalesAggregation.push(
        {
          $lookup: {
            from: "corporateevents",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    corporateSalesAggregation.push(
      {
        $group: {
          _id: groupBy,
          totalSales: { $sum: "$bookedTicketQuantities" },
          totalRevenue: { $sum: { $divide: ["$amount", 100] } },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const corporateSales = await CorporateBookingModel.aggregate(corporateSalesAggregation);

    // Combine and format data
    const combinedData = {};
    
    [...regularSales, ...corporateSales].forEach(item => {
      const key = item.dateString;
      if (!combinedData[key]) {
        combinedData[key] = {
          date: key,
          totalSales: 0,
          totalRevenue: 0,
          orderCount: 0
        };
      }
      combinedData[key].totalSales += item.totalSales || 0;
      combinedData[key].totalRevenue += item.totalRevenue || 0;
      combinedData[key].orderCount += item.orderCount || 0;
    });

    const salesData = Object.values(combinedData).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.status(200).json({
      success: true,
      data: salesData,
      period,
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      totalRecords: salesData.length
    });

  } catch (error) {
    console.error("Error fetching sales data:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching sales data",
      error: error.message
    });
  }
};

/**
 * Get revenue data for charts with time range filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getRevenueData = async (req, res) => {
  try {
    const { startDate, endDate, period = 'daily', createdBy } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    end.setHours(23, 59, 59, 999);
    start.setHours(0, 0, 0, 0);

    let groupBy;
    let dateFormat;

    switch (period) {
      case 'hourly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } },
          hour: { $hour: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d %H:00";
        break;
      case 'weekly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          week: { $week: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-W%U";
        break;
      case 'monthly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m";
        break;
      case 'yearly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y";
        break;
      default:
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d";
    }

    // Build match criteria for revenue data
    let revenueMatchCriteria = {
      createdAt: {
        $gte: start.getTime(),
        $lte: end.getTime()
      },
      isPaid: true
    };

    // Regular bookings revenue with optional creator filtering
    let regularRevenueAggregation = [
      {
        $match: revenueMatchCriteria
      }
    ];

    if (createdBy) {
      regularRevenueAggregation.push(
        {
          $lookup: {
            from: "ceremonymodels",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    regularRevenueAggregation.push(
      {
        $group: {
          _id: groupBy,
          grossRevenue: { $sum: { $divide: ["$amount", 100] } },
          paidAmount: { $sum: { $divide: ["$paid_Amount", 100] } },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const regularRevenue = await BookingModel.aggregate(regularRevenueAggregation);

    // Corporate bookings revenue with optional creator filtering
    let corporateRevenueAggregation = [
      {
        $match: revenueMatchCriteria
      }
    ];

    if (createdBy) {
      corporateRevenueAggregation.push(
        {
          $lookup: {
            from: "corporateevents",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    corporateRevenueAggregation.push(
      {
        $group: {
          _id: groupBy,
          grossRevenue: { $sum: { $divide: ["$amount", 100] } },
          paidAmount: { $sum: { $divide: ["$paid_Amount", 100] } },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const corporateRevenue = await CorporateBookingModel.aggregate(corporateRevenueAggregation);

    // Combine revenue data
    const combinedRevenue = {};

    [...regularRevenue, ...corporateRevenue].forEach(item => {
      const key = item.dateString;
      if (!combinedRevenue[key]) {
        combinedRevenue[key] = {
          date: key,
          grossRevenue: 0,
          paidAmount: 0,
          orderCount: 0
        };
      }
      combinedRevenue[key].grossRevenue += item.grossRevenue || 0;
      combinedRevenue[key].paidAmount += item.paidAmount || 0;
      combinedRevenue[key].orderCount += item.orderCount || 0;
    });

    const revenueData = Object.values(combinedRevenue).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.status(200).json({
      success: true,
      data: revenueData,
      period,
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      totalRecords: revenueData.length
    });

  } catch (error) {
    console.error("Error fetching revenue data:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching revenue data",
      error: error.message
    });
  }
};

/**
 * Get profit data for charts with time range filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getProfitData = async (req, res) => {
  try {
    const { startDate, endDate, period = 'daily', createdBy } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    end.setHours(23, 59, 59, 999);
    start.setHours(0, 0, 0, 0);

    let groupBy;
    let dateFormat;

    switch (period) {
      case 'hourly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } },
          hour: { $hour: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d %H:00";
        break;
      case 'weekly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          week: { $week: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-W%U";
        break;
      case 'monthly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m";
        break;
      case 'yearly':
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y";
        break;
      default:
        groupBy = {
          year: { $year: { $toDate: "$createdAt" } },
          month: { $month: { $toDate: "$createdAt" } },
          day: { $dayOfMonth: { $toDate: "$createdAt" } }
        };
        dateFormat = "%Y-%m-%d";
    }

    // Build match criteria for profit data
    let profitMatchCriteria = {
      createdAt: {
        $gte: start.getTime(),
        $lte: end.getTime()
      },
      isPaid: true
    };

    // Get revenue and basic price data for profit calculation with optional creator filtering
    let profitDataAggregation = [
      {
        $match: profitMatchCriteria
      },
      {
        $lookup: {
          from: "tickets",
          localField: "tickets",
          foreignField: "_id",
          as: "ticketDetails"
        }
      }
    ];

    if (createdBy) {
      profitDataAggregation.push(
        {
          $lookup: {
            from: "ceremonymodels",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    profitDataAggregation.push(
      {
        $group: {
          _id: groupBy,
          grossRevenue: { $sum: { $divide: ["$amount", 100] } },
          basicPriceCosts: {
            $sum: {
              $multiply: [
                "$bookedTicketQuantities",
                { $divide: [{ $avg: "$ticketDetails.basic_price" }, 100] }
              ]
            }
          },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $project: {
          _id: 1,
          grossRevenue: 1,
          basicPriceCosts: 1,
          netProfit: { $subtract: ["$grossRevenue", "$basicPriceCosts"] },
          orderCount: 1,
          dateString: 1
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const profitData = await BookingModel.aggregate(profitDataAggregation);

    // Corporate profit data with optional creator filtering
    let corporateProfitAggregation = [
      {
        $match: profitMatchCriteria
      },
      {
        $lookup: {
          from: "tickets",
          localField: "tickets",
          foreignField: "_id",
          as: "ticketDetails"
        }
      }
    ];

    if (createdBy) {
      corporateProfitAggregation.push(
        {
          $lookup: {
            from: "corporateevents",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    corporateProfitAggregation.push(
      {
        $group: {
          _id: groupBy,
          grossRevenue: { $sum: { $divide: ["$amount", 100] } },
          basicPriceCosts: {
            $sum: {
              $multiply: [
                "$bookedTicketQuantities",
                { $divide: [{ $avg: "$ticketDetails.basic_price" }, 100] }
              ]
            }
          },
          orderCount: { $sum: 1 },
          dateString: { $first: { $dateToString: { format: dateFormat, date: { $toDate: "$createdAt" } } } }
        }
      },
      {
        $project: {
          _id: 1,
          grossRevenue: 1,
          basicPriceCosts: 1,
          netProfit: { $subtract: ["$grossRevenue", "$basicPriceCosts"] },
          orderCount: 1,
          dateString: 1
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1, "_id.hour": 1 }
      }
    );

    const corporateProfitData = await CorporateBookingModel.aggregate(corporateProfitAggregation);

    // Combine profit data
    const combinedProfit = {};

    [...profitData, ...corporateProfitData].forEach(item => {
      const key = item.dateString;
      if (!combinedProfit[key]) {
        combinedProfit[key] = {
          date: key,
          grossRevenue: 0,
          basicPriceCosts: 0,
          netProfit: 0,
          orderCount: 0
        };
      }
      combinedProfit[key].grossRevenue += item.grossRevenue || 0;
      combinedProfit[key].basicPriceCosts += item.basicPriceCosts || 0;
      combinedProfit[key].netProfit += item.netProfit || 0;
      combinedProfit[key].orderCount += item.orderCount || 0;
    });

    const finalProfitData = Object.values(combinedProfit).sort((a, b) => new Date(a.date) - new Date(b.date));

    res.status(200).json({
      success: true,
      data: finalProfitData,
      period,
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      totalRecords: finalProfitData.length
    });

  } catch (error) {
    console.error("Error fetching profit data:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching profit data",
      error: error.message
    });
  }
};

/**
 * Get dashboard summary with key metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDashboardSummary = async (req, res) => {
  try {
    const { startDate, endDate, createdBy } = req.query;

    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    end.setHours(23, 59, 59, 999);
    start.setHours(0, 0, 0, 0);

    // Get current period data
    const currentPeriodMatch = {
      createdAt: {
        $gte: start.getTime(),
        $lte: end.getTime()
      },
      isPaid: true
    };

    // Get previous period for comparison (same duration before start date)
    const periodDuration = end.getTime() - start.getTime();
    const previousStart = new Date(start.getTime() - periodDuration);
    const previousEnd = new Date(start.getTime());

    const previousPeriodMatch = {
      createdAt: {
        $gte: previousStart.getTime(),
        $lte: previousEnd.getTime()
      },
      isPaid: true
    };

    // Build aggregation pipelines for current period
    let currentRegularAggregation = [{ $match: currentPeriodMatch }];
    let currentCorporateAggregation = [{ $match: currentPeriodMatch }];

    if (createdBy) {
      currentRegularAggregation.push(
        {
          $lookup: {
            from: "ceremonymodels",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );

      currentCorporateAggregation.push(
        {
          $lookup: {
            from: "corporateevents",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    currentRegularAggregation.push({
      $group: {
        _id: null,
        totalSales: { $sum: "$bookedTicketQuantities" },
        totalRevenue: { $sum: { $divide: ["$amount", 100] } },
        orderCount: { $sum: 1 }
      }
    });

    currentCorporateAggregation.push({
      $group: {
        _id: null,
        totalSales: { $sum: "$bookedTicketQuantities" },
        totalRevenue: { $sum: { $divide: ["$amount", 100] } },
        orderCount: { $sum: 1 }
      }
    });

    // Current period metrics
    const [currentRegular, currentCorporate] = await Promise.all([
      BookingModel.aggregate(currentRegularAggregation),
      CorporateBookingModel.aggregate(currentCorporateAggregation)
    ]);

    // Build aggregation pipelines for previous period
    let previousRegularAggregation = [{ $match: previousPeriodMatch }];
    let previousCorporateAggregation = [{ $match: previousPeriodMatch }];

    if (createdBy) {
      previousRegularAggregation.push(
        {
          $lookup: {
            from: "ceremonymodels",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );

      previousCorporateAggregation.push(
        {
          $lookup: {
            from: "corporateevents",
            localField: "eventDetails",
            foreignField: "_id",
            as: "eventInfo"
          }
        },
        {
          $match: {
            "eventInfo.eventCreatedBy": createdBy
          }
        }
      );
    }

    previousRegularAggregation.push({
      $group: {
        _id: null,
        totalSales: { $sum: "$bookedTicketQuantities" },
        totalRevenue: { $sum: { $divide: ["$amount", 100] } },
        orderCount: { $sum: 1 }
      }
    });

    previousCorporateAggregation.push({
      $group: {
        _id: null,
        totalSales: { $sum: "$bookedTicketQuantities" },
        totalRevenue: { $sum: { $divide: ["$amount", 100] } },
        orderCount: { $sum: 1 }
      }
    });

    // Previous period metrics
    const [previousRegular, previousCorporate] = await Promise.all([
      BookingModel.aggregate(previousRegularAggregation),
      CorporateBookingModel.aggregate(previousCorporateAggregation)
    ]);

    // Calculate current totals
    const currentTotals = {
      totalSales: (currentRegular[0]?.totalSales || 0) + (currentCorporate[0]?.totalSales || 0),
      totalRevenue: (currentRegular[0]?.totalRevenue || 0) + (currentCorporate[0]?.totalRevenue || 0),
      orderCount: (currentRegular[0]?.orderCount || 0) + (currentCorporate[0]?.orderCount || 0)
    };

    // Calculate previous totals
    const previousTotals = {
      totalSales: (previousRegular[0]?.totalSales || 0) + (previousCorporate[0]?.totalSales || 0),
      totalRevenue: (previousRegular[0]?.totalRevenue || 0) + (previousCorporate[0]?.totalRevenue || 0),
      orderCount: (previousRegular[0]?.orderCount || 0) + (previousCorporate[0]?.orderCount || 0)
    };

    // Calculate percentage changes
    const calculatePercentageChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const summary = {
      currentPeriod: {
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        ...currentTotals,
        averageOrderValue: currentTotals.orderCount > 0 ? currentTotals.totalRevenue / currentTotals.orderCount : 0
      },
      previousPeriod: {
        startDate: previousStart.toISOString(),
        endDate: previousEnd.toISOString(),
        ...previousTotals,
        averageOrderValue: previousTotals.orderCount > 0 ? previousTotals.totalRevenue / previousTotals.orderCount : 0
      },
      changes: {
        salesChange: calculatePercentageChange(currentTotals.totalSales, previousTotals.totalSales),
        revenueChange: calculatePercentageChange(currentTotals.totalRevenue, previousTotals.totalRevenue),
        orderChange: calculatePercentageChange(currentTotals.orderCount, previousTotals.orderCount),
        aovChange: calculatePercentageChange(
          currentTotals.orderCount > 0 ? currentTotals.totalRevenue / currentTotals.orderCount : 0,
          previousTotals.orderCount > 0 ? previousTotals.totalRevenue / previousTotals.orderCount : 0
        )
      }
    };

    res.status(200).json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error("Error fetching dashboard summary:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching dashboard summary",
      error: error.message
    });
  }
};

module.exports = {
  getSalesData,
  getRevenueData,
  getProfitData,
  getDashboardSummary
};
