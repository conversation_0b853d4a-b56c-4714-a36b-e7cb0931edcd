
.productDetailDiv {
    width:100%;
    max-width: 1280px;
    margin: auto;
}
.price-details-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.detailDiv {
    display:flex;
    justify-content: start;
    align-items: start;
    flex-direction:row;
    width:100%
}

.event-image {
    width: 100%;
    position: relative;
}
.event-image-internal{
    border-radius: 1rem !important;
}
.event-info{
    width: 100%;
}

.event_name {
    font-weight: bold;
    font-size: 24px;
    
}

.event-category {
    font-size: 12px;
    
    padding: 0.1rem 0.5rem;
    background: #2C9CF0;
    color: #ffffff;
    border-radius: 1rem;
}

.event-detail {
    font-size: 16px !important;
    
    color: #6A6A6A;
}

.directionspan {
    
    font-size: 10px !important;
    color: #2C9CF0;
    margin-left: 3%;
    text-decoration: none !important;
}

.event-timing {
    font-size: 15px;
    
}

.ticket-amt {
    font-size: 30px;
    font-weight: bold;
    
}

.booking-btn {
    background: #2C9CF0;
    color: #ffffff;
    width: 218px;
    height: 50px;
    
    border: none;
}

.aboutArtistDiv {
    width: 100%;
    display:flex;
    flex-direction:row;
    justify-content: space-evenly;
    align-items: start;
    font-size: 25px;
    margin: auto !important;
    margin-top: 3rem !important;
}

.artistSocialdetail {
    width: 100%;
    overflow-x: auto; /* Enable horizontal scrolling */
    display: flex; /* Use flexbox for horizontal layout */
    gap: 10%; /* Add spacing between items */
    margin-top: 0.5rem;
}

.backarrow{
    position: absolute;
    top: 8px;
    left:18px;
    opacity: 0.5;
}
.artistItem {
    flex: 0 0 auto; /* Prevent shrinking and allow scrolling */
    margin-right: 10px;
    text-align: center; /* Center the text and image */
    word-wrap: break-word; /* Break long words */
    overflow-wrap: break-word; /* Ensure text wraps */
    white-space: normal; /* Allow wrapping inside the item */
}

.artist-name {
    font-size: 1rem; /* Adjust font size as needed */
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal; /* Ensure text can wrap */
    line-height: 1.5;
    text-align: center;
    margin-top: 1rem;
}

.aboutArtist {
    min-height: 170px !important;
    padding: 1rem;
    margin-bottom: 10px !important;
}

.profileHead{
    
    font-size: 22px;
}
.profile-Head{
    
    font-size: 20px;
}
.profilesub {
    font-size: 16px;
    max-height: 70px; /* Adjust as needed for ~3-4 lines of text */
    overflow: hidden;
    text-overflow: ellipsis;
    transition: max-height 0.3s ease-in-out; /* Optional: for a smooth transition */
    white-space: pre-wrap; /* You already have this, which is good */
}

.profilesub-seemore {
    max-height: 100%; /* A large enough value to show all content */
}
.readmore{
    font-size: 12px;
}

.artistName{
    font-size: 12px;
    
    margin-top: 0.5rem;
    text-align: center;
}

.user-image {
    width: 77px;
    height: 77px;
    border-radius: 50%;
    object-fit: cover;
}

.venueDiv {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
}

.venueMap {
    width: 100%;
    min-height: 17rem;
    max-height: 17rem;
    text-align: center;
    font-weight: bold;
    font-size: xx-large;
    background-color: rgb(221, 217, 217);
}

.moreInfo {
    width: 90% !important;
    min-height: 16rem;
    max-height: 16rem;
}

.extraDetail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    height: 38px;
    background-color: #FFFFFF;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); /* subtle, even shadow */
    border-radius: 0.5rem;
    cursor: pointer;
}

.product-collapse-text{
    font-size: 16px;
    font-weight: 600;
    padding-left: 1rem;
}
/* .arrowdown {
    font-size: 16px !important;
} */

#venueLoyout {
    width: 70%;
    margin: auto;
}

.layoutDiv {
    width: 95% !important;
    font-size: 25px;
    margin: auto !important;
    margin-top: 47px !important;
}

.layoutimgdiv {
    height: 225px;
    width: 95%;
}

.layoutImg{
    object-fit: contain;
}

.lastDiv {
    width: 100%;
}

.event-schedule {
    display: flex;
    flex-direction: row;
    margin-top: 1rem;
    justify-content: space-between;
}

.guidelines {
    font-size: 20px;
}

.event-price {
    display: none;
}

@media screen and (max-width:1215px) {

}

@media screen and (max-width:1145px) {
    .event-image {
        width: 60%;
    }
    .event-info{
        width: 40%;
    }
}

@media screen and (max-width:995px) {

    .event-image {
        width: 60%;
    }
    .event-info{
        width: 40%;
    }
    .aboutArtistDiv{
        flex-direction: column;
    }

    .event-category {
        font-size: 11px;
        
        padding: 0.1rem 0.5rem;
        background: #2C9CF0;
        color: #ffffff;
        text-align: center;
    }

    .event-detail {
        font-size: 25px;
        
        color: #6A6A6A;
    }

    .event-schedule {
        display: flex;
        flex-direction: column;
        margin-top: 5px !important;
    }

    .ticket-amt {
        font-size: 30px;
        font-weight: bold;
        
        margin-top: 5px;
    }

    .booking-btn {
        margin-top: 5px;
        background: #2C9CF0;
        color: #ffffff;
        
        
        border: none;
    }
}

@media screen and (max-width:768px) {
    .event-image-internal{
        border-radius: 0rem !important;
        
    }
    .event-price {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        /* height: 60px; */
        /* padding: 0rem 1rem; */
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(94, 94, 94, 0.2);
        background:white;
        z-index: 1000; 
    }
    .detailDiv {
        flex-direction: column;
    }
    .price-details-content{
        display: none
    }
    .event-image {
        width: 100%;
        padding-bottom: 0 !important;
        padding:0rem !important;
        border-radius: 0px !important;;
        
    }
    .aboutArtistDiv{
        margin-top: 0rem !important;
        padding: 0rem !important;
        margin-bottom: 0rem !important;
    }
    .lastDiv{
        padding: 1rem !important;
        margin-top: 0px !important;
        margin-bottom: 0px !important;
    }
    .booking-btn {
        display: none;
    }
    .event-info{
        width: 100%;
    }
    .product-collapse-text{
        padding-left: 0;
    }
    .event-ticket {
        font-size: 25px;
        
        font-weight: bold;
    }
    .btn-booking {
        height: 40px;
        width: 160px;
        font-size: 18px !important;
        background: #2C9CF0;
        border: none !important;
        border-radius: 0.5rem !important;
    }
        
}

/* @media screen and (max-width: 586px) {


    

    .event-category {
        max-width: 7rem !important;
        background: #2C9CF0;
        color: #ffffff;
        font-size: 12px;
        margin-bottom: 4px;
    }

    .event-lang {
        
        font-size: 16px;
        color: #6A6A6A;
    }
    .event-datetime {
        
        font-size: 15px;
        margin: 0.7rem 0rem 0rem 0rem;
    }

    .event-price {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 60px;
        padding: 0rem 1rem;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(94, 94, 94, 0.2);
        background:white;
        z-index: 1000; 
    }

    .event-ticket {
        font-size: 25px;
        
        font-weight: bold;
    }

    .btn-booking {
        height: 40px;
        width: 160px;
        font-size: 14px !important;
        
        background: #2C9CF0;
        border: none !important;
        border-radius: 0.5rem !important;
    }

    .artistDiv {
        width: 100%;
        margin: auto;
        margin-top: 0px;
        
        font-size: 15px;
    }
    .artistDivabout{
        min-height: 85px;
        width: 100%;
        margin: auto;
        
        font-size: 15px;
    }

    .artistHeading {
        
        font-size: 16px;
    }

    .contents {
        
        font-size: 14px;
    }

    .user-image {
        width: 61px;
        height: 61px;
        border-radius: 50%;
    }

    .extraDetail {
        margin: auto;
        display: flex;
        justify-content: space-between;
        
        font-size: 18px;
        background-color: #FFFF;
        padding: 0.1rem 2rem;
        margin-bottom: 0.5rem;
        box-shadow: 2px 2px 4px 0.1px #d7d5d5 ;
    }

    #venueLoyout {
        width: 98%;
        margin: auto;
        height: 264px;
    }
    .readmore{
        font-size: 10px;
    }
} */


.primarycolor{
    color: #2C9CF0;
}

.product-detail-skeleton-box {
    background: #e0e0e0;
    border-radius: 4px;
    animation: shimmer 1.5s infinite linear;
    position: relative;
    overflow: hidden;
}

.product-detail-skeleton-img {
    width: 100%;
    height: 300px;
}

.product-detail-skeleton-title {
    width: 60%;
    height: 24px;
}

.product-detail-skeleton-subtitle {
    width: 40%;
    height: 16px;
}

.product-detail-skeleton-line {
    width: 100%;
    height: 14px;
}

.product-detail-skeleton-paragraph {
    width: 100%;
    height: 12px;
}

.product-detail-skeleton-button {
    width: 30%;
    height: 40px;
    border-radius: 8px;
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.product-detail-skeleton-box::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150px;
    height: 100%;
    width: 150px;
    background: linear-gradient(to right, transparent 0%, #f0f0f0 50%, transparent 100%);
    animation: loading 1.2s infinite;
}

@keyframes loading {
    0% {
        left: -150px;
    }
    100% {
        left: 100%;
    }
}