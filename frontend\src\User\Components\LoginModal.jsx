import { useState, useEffect, useRef } from "react";
import { TextField } from "@mui/material";
// import { Button } from "react-bootstrap";
import { RecaptchaVerifier, signInWithPhoneNumber } from "firebase/auth";
import auth from "../../firebaseConfig";
import apiService from "../../services/apiService";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { BsXCircle } from "react-icons/bs";
import "./LoginModal.css";
import SyncLoader from "react-spinners/SyncLoader";
import indian_flag from '../../Assets/flag.png'
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

/* global grecaptcha */

function LoginModal({ onLoginSuccess, nextAction }) {
  const [phone, setPhone] = useState("");
  const [user, setUser] = useState(null);
  const [otp, setOtp] = useState("");
  const [showOTPField, setShowOTPField] = useState(false);
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const recaptchaRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false); // State for modal visibility
  const [usernotLogin, setusernotLogin] = useState(false);

  useEffect(() => {
    let countdown;
    if (isResendDisabled && showOTPField) {
      countdown = setInterval(() => {
        setTimer((prevTimer) => {
          if (prevTimer <= 1) {
            clearInterval(countdown);
            setIsResendDisabled(false);
            return 60;
          }
          return prevTimer - 1;
        });
      }, 1000);
    }
    return () => clearInterval(countdown);
  }, [isResendDisabled, showOTPField]);

  const setupRecaptcha = () => {
    if (!recaptchaRef.current) {
      console.error("reCAPTCHA element not found.");
      return;
    }

    if (!window.recaptchaVerifier) {
      window.recaptchaVerifier = new RecaptchaVerifier(auth, "recaptcha", {
        size: "invisible",
        callback: (response) => {
          console.log("Response", response);
        },
        "expired-callback": () => {
          console.log("Recaptcha expired, please try again.");
        },
      });

      window.recaptchaVerifier.render().then((widgetId) => {
        window.recaptchaWidgetId = widgetId;
      });
    } else {
      if (typeof grecaptcha !== "undefined" && window.recaptchaWidgetId) {
        grecaptcha.reset(window.recaptchaWidgetId);
      }
    }
  };

  const sendOTP = async () => {
    setLoading(true);
    setupRecaptcha();
    const phoneNumber = "+91" + phone;
    const appVerifier = window.recaptchaVerifier;
    try {
      const confirmation = await signInWithPhoneNumber(
        auth,
        phoneNumber,
        appVerifier
      );
      setUser(confirmation);
      setShowOTPField(true);
      setIsResendDisabled(true);
    } catch (err) {
      console.error("Error during OTP send:", err.message);
      if (typeof grecaptcha !== "undefined" && window.recaptchaWidgetId) {
        grecaptcha.reset(window.recaptchaWidgetId);
      }
      alert(`Failed to send OTP. Error: ${err.message}`);
    } finally {
      setLoading(false); // Reset loading to false after OTP is verified
    }
  };

  const verifyOtp = async (event) => {
    event.preventDefault();
    setLoading(true);
    try {
      const data = await user.confirm(otp);
      if (data) {
        apiService.setData(data.user);
        const isUser = await apiService.get(
          `/user/getUserWithID/${data.user.phoneNumber}`
        );
        if (isUser) {
          apiService.setData(isUser);
          onLoginSuccess(isUser); // Close modal and update main state
        } else {
          // window.location.href = "/signup";
          setusernotLogin(true);
        }
      }
    } catch (err) {
      console.error(err);
      alert("Invalid OTP. Please try again.");
    } finally {
      setLoading(false); // Reset loading to false after OTP is verified
    }
  };

  const navigate = useNavigate();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [gender, setGender] = useState("");
  const [dob, setDob] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [renderFlag, setRenderFlag] = useState("");
  const location = useLocation();
  const userData = apiService.getData();

  const handleDateChange = (date) => {
    setDob(date);
  };

  const saveUserandProceed = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const body = {
        _id: userData.uid,
        phone: userData.phoneNumber,
        firstName: firstName,
        lastName: lastName,
        email: email,
        gender: gender,
        dob: dob,
      };
      const res = await apiService.post("/user/addUser", body);
      if (res.status === 200) {
        // Update local storage with the new user data
        apiService.setData(body);
        onLoginSuccess(body);

        // switch (renderFlag) {
        //     case "event":
        //         window.location.href = "https://hfs-live-31172.web.app";
        //         break;
        //     case "login":
        //         navigate("/");
        //         break;
        //     case "ticket":
        //         navigate("/paymentgateway");
        //         break;
        //     default:
        //         navigate("/");
        //         break;
        // }
      }
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fullscreen-login">
      {!usernotLogin && (
        <div className="login-containermodal p-3">
          <div className="d-flex flex-row justify-content-end align-items-center">
            {/* <BsXCircle
              onClick={onLoginSuccess}
              className="bg-white rounded-pill"
            /> */}
          </div>

          <span className="bookhead" style={{}}>
            Book <span className="your-fs"> Your Front Seat</span>
          </span>

          {!showOTPField && (
            <form className="mt-4">
              <div className="div-num">
                <p className="enter-number">Login with your mobile number</p>
              </div>
              <div className="mb-2">
                <div className="input-group mb-3">
                  <span className="input-group-text border-primary rounded-start-3">
                    <img className="me-2 mt-1" src={indian_flag} alt="India" />{" "}
                    +91
                  </span>
                  <input
                    type="tel"
                    className="form-control mobileinput border-primary"
                    name="mobileNumber"
                    placeholder="e.g. 9226773937"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                  />
                </div>

                {/* <input
                className="form-control mobileinput py-3 border-primary"
                type="tel"
                name="mobileNumber"
                placeholder="+91"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                required
              /> */}
              </div>
              {/* <Button onClick={sendOTP} className="btn getotpbtn btn-primary w-50">
              Get OTP
            </Button> */}
              <button
                onClick={sendOTP}
                className="btn btn-primary w-100 mt-2 rounded-3"
                disabled={loading || !phone || phone.length !== 10}
                style={{ backgroundColor: "#2C9CF0", borderColor: "#2C9CF0" }}
              >
                {loading ? (
                  <SyncLoader
                    animation="border"
                    color="#FFFF"
                    size="7"
                    speedMultiplier={1}
                    margin={4}
                  />
                ) : (
                  "Get OTP"
                )}{" "}
                {/* Loader for Get OTP */}
              </button>
            </form>
          )}

          {showOTPField && (
            <>
              <div className="div-num mt-4">
                <p className="enter-number">Enter OTP</p>
              </div>
              {/* <TextField
                className="form-control"
                onChange={(e) => setOtp(e.target.value)}
                variant="outlined"
                placeholder="xxxxxx"
              /> */}
              <input
                type="password"
                className="form-control mobileinput input-border-primary"
                name="otp"
                placeholder="e.g. xxxxxx"
                onChange={(e) => setOtp(e.target.value)}
                required
                style={{ borderColor: "#2C9CF0" }}
              />
              {/* <br /> */}
              <div>
                {/* <Button className="btn btn-primary" onClick={verifyOtp}>
                Login
              </Button> */}
                <button
                  className="btn btn-primary w-100 mt-4 rounded-3"
                  onClick={verifyOtp}
                  disabled={loading || !otp || otp.length !== 6}
                  style={{ backgroundColor: "#2C9CF0", borderColor: "#2C9CF0" }}
                >
                  {loading ? (
                    <SyncLoader
                      animation="border"
                      color="#FFFF"
                      size="7"
                      speedMultiplier={1}
                      margin={4}
                    />
                  ) : (
                    "Login"
                  )}{" "}
                  {/* Loader for Login */}
                </button>
              </div>
              <div className="resend-otp">
                <Link
                  className={`resend-text ${
                    isResendDisabled ? "isResendDisabled" : "isResendEnabled"
                  }`}
                  onClick={sendOTP}
                  disabled={isResendDisabled}
                >
                  Resend OTP
                </Link>
                {timer === 60 ? (
                  ""
                ) : (
                  <span className="countdown"> in {timer} seconds</span>
                )}
              </div>
            </>
          )}
        </div>
      )}
      {usernotLogin && (
        <>
          <div
            className="personaldet login-containermodal p-3"
            id="desktopView"
          >
            <div className="containerr h-100">
              <div className="cont-pers">
                <span>
                  {/* <BsXCircle
                    onClick={onLoginSuccess}
                    className="closebtn bg-white rounded-pill"
                  /> */}
                </span>
                <div className="row p-3">
                  <div>
                    <h5 className="mt-3">
                      <span className="fw-bold">Book</span>{" "}
                      <span className="your-fs"> Your Front Seat</span>
                    </h5>
                  </div>
                  <h4 className="fw-bold text-start mt-3">Personal Details</h4>
                  <div>
                    <input
                      className="form-control mobileinput input-border-primary mb-3"
                      type="text"
                      id="first-name"
                      placeholder="First Name"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                    />
                  </div>
                  <div>
                    <input
                      className="form-control mobileinput input-border-primary mb-3"
                      type="text"
                      id="last-name"
                      placeholder="Last Name"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                    />
                  </div>
                  <div>
                    <input
                      className="form-control mobileinput input-border-primary mb-1"
                      type="text"
                      id="email"
                      placeholder="Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <p className="mail-recieve text-start mb-3">
                    The email will receive the m-ticket
                  </p>
                  <div className="d-flex flex-column justify-content-start align-items-start mb-3">
                    <div className="">
                      <DatePicker
                        className="form-control mobileinput input-border-primary mb-3"
                        selected={dob}
                        id="dob"
                        onChange={handleDateChange}
                        placeholderText="DOB    DD/MM/YYYY"
                        dateFormat="dd/MM/yyyy"
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                      />
                    </div>
                    <div className="text-start">
                      <div className="d-flex justify-content-between">
                        <div className="gender">
                          <input
                            className="pers-selection"
                            type="radio"
                            id="female"
                            name="gender"
                            value="female"
                            checked={gender === "female"}
                            onChange={() => setGender("female")}
                          />
                          <label className="pers-gender ms-1" htmlFor="female">
                            Female
                          </label>
                        </div>
                        <div className="gender ms-3">
                          <input
                            className="pers-selection"
                            type="radio"
                            id="male"
                            name="gender"
                            value="male"
                            checked={gender === "male"}
                            onChange={() => setGender("male")}
                          />
                          <label className="pers-gender ms-1" htmlFor="male">
                            Male
                          </label>
                        </div>
                      </div>
                      {/* <div className="d-flex justify-content-center">
                          <div className="gender m-auto">
                            <input
                              className="pers-selection"
                              type="radio"
                              id="other"
                              name="gender"
                              value="other"
                              checked={gender === "other"}
                              onChange={() => setGender("other")}
                            />
                            <label className="pers-gender" htmlFor="others">
                              Others
                            </label>
                          </div>
                        </div> */}
                    </div>
                  </div>
                  {/* Progress Bar */}
                  {isLoading && (
                    <div className="progress-bar-container">
                      <div className="progress-bar"></div>
                    </div>
                  )}

                  <div className="BtnDiv">
                    <button
                      className="btn btn-primary w-100 mt-1 rounded-3"
                      onClick={saveUserandProceed}
                      style={{
                        backgroundColor: "#2C9CF0",
                        borderColor: "#2C9CF0",
                      }}
                    >
                      {renderFlag === "ticket" ? (
                        <span>Proceed to Pay</span>
                      ) : (
                        <span>Register Yourself</span>
                      )}
                    </button>
                  </div>
                </div>
                <div className="pers-accept-div text-center">
                  {/* <input type="checkbox" id="terms-pers" /> */}
                  <label htmlFor="terms" className="text-terms-acc">
                    I accept the Terms of Use & Privacy Policy
                  </label>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      <div id="recaptcha" ref={recaptchaRef}></div>
    </div>
  );
}

export default LoginModal;
