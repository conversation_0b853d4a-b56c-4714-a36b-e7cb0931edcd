const path = require('path');
const crypto = require('crypto');
const { Storage } = require('@google-cloud/storage');
const CorporateEventModel = require('../models/CorporateEventModel');
const TicketModel = require('../models/TicketModel');
const CorporateUserModel = require('../models/CorporateUserModel'); const puppeteer = require('puppeteer');
const nodemailer = require('nodemailer');
const ejs = require('ejs');
const fs = require('fs');
const QRCode = require('qrcode');
const { toWords } = require('number-to-words');
const StateModel = require('../models/StateDataModel');
const ScannerLinkData = require('../VendorDatabase/VendorModel/ScannerLinkData');
const CorporateBookingModel = require('../models/CorporateBookingModel');
const CorporateCompanyDetailModel = require('../models/CorporateCompanyDetailModel');
const mongoose = require('mongoose');
const xlsx = require('xlsx');
const sharp = require('sharp');

const storage = new Storage({
  keyFilename: path.join(__dirname, '../googleCloudBucketConfig.json'),
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your bucket name
// Multer Setup for Local File Upload
const multer = require('multer');
const EmployeeModel = require('../models/EmployeeDataModel');
const upload = multer({ dest: 'uploads/' });
// File Controller (multiple file upload)
const corporateEventImages = async (req, res) => {
  try {
    const uploadedFiles = {
      venueLayout: req.files.venueLayout ? req.files.venueLayout[0] : null,
      cover_image: req.files.cover_image ? req.files.cover_image[0] : null,
      artist_picture: req.files.artist_picture ? req.files.artist_picture[0] : null,
    };

    // Upload venue picture
    if (uploadedFiles.venueLayout) {
      const venueFile = uploadedFiles.venueLayout;
      const gcsFileName = `corporate-pictures/${Date.now()}-${venueFile.originalname}`;
      const gcsFile = bucket.file(gcsFileName);

      // Upload the file to Google Cloud Storage
      await gcsFile.save(venueFile.buffer, {
        metadata: {
          contentType: venueFile.mimetype,
        },
      });

      uploadedFiles.venueLayout = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;
    }

    if (uploadedFiles.cover_image) {
      const cover_pic = uploadedFiles.cover_image;
      const gcsFileName = `corporate-pictures/${Date.now()}-${cover_pic.originalname}`;
      const gcsFile = bucket.file(gcsFileName);

      // Upload the file to Google Cloud Storage
      await gcsFile.save(cover_pic.buffer, {
        metadata: {
          contentType: cover_pic.mimetype,
        },
      });

      uploadedFiles.cover_image = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;
    }

    if (uploadedFiles.artist_picture) {
      const artist_pic = uploadedFiles.artist_picture;
      const gcsFileName = `corporate-pictures/${Date.now()}-${artist_pic.originalname}`;
      const gcsFile = bucket.file(gcsFileName);

      // Upload the file to Google Cloud Storage
      await gcsFile.save(artist_pic.buffer, {
        metadata: {
          contentType: artist_pic.mimetype,
        },
      });

      uploadedFiles.artist_picture = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;
    }

    res.send({
      message: 'Files uploaded successfully',
      files: uploadedFiles,
    });
  } catch (err) {
    console.error('Error uploading files:', err);
    res.status(500).send({
      message: 'Error occurred while uploading files',
      error: err.message,
    });
  }
};

const StoreExcelSheetInDB = async (req, res) => {
  const file = req.files.emp_data_excelSeet ? req.files.emp_data_excelSeet[0] : null;
  console.log("_________________", req.files, "________________")
  if (!file) {
    return res.status(400).send('No file uploaded.');
  }

  // Validate file type
  if (file.mimetype !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    return res.status(400).send('Invalid file type. Please upload an Excel file.');
  }

  const workbook = xlsx.readFile(file.path);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = xlsx.utils.sheet_to_json(worksheet);
  const uniquepasscode = crypto.randomBytes(4).toString('hex').toUpperCase();

  try {
    for (const row of data) {
      const { Employee_ID,
        Employee_Name,
        Employee_Phone_number,
        Employee_Email_ID } = row;

      const employee = new EmployeeModel({
        Employee_ID,
        Employee_Name,
        Employee_Phone_number,
        Employee_Email_ID,
        uniqueCode: uniquepasscode
      });

      await employee.save();
    }

    fs.unlinkSync(file.path); // Remove the file from local storage

    res.status(200).json({message: 'File uploaded and data inserted into the database.', uniquepasscode});
  } catch (error) {
    console.error('Error saving data:', error);
    res.status(500).send('Error processing the file.');
  }
};

// create New Event 
const corporateEventCreation = async (req, res) => {
  const lastEvent = await CorporateEventModel.findOne().sort({ createdAt: -1 }).exec();
  let event_id;

  if (!lastEvent || !lastEvent.corporate_event_id) {
    // Take the first character from each word in company_name and join them together
    const companyInitials = req.body.company_name
      .split(' ')        // Split the company name by spaces
      .map(word => word[0])  // Get the first character of each word
      .join('');         // Join them together
  
    event_id = companyInitials.slice(0, 3).toUpperCase() + "00001";
  } else {
    // Extract the numerical part of the last corporate_event_id
    const lastId = lastEvent.corporate_event_id;
    const lastNumber = parseInt(lastId.substring(3), 10); // Get the number after the company initials
    const newIdNumber = lastNumber + 1; // Increment the number
    
    // Get the company initials and form the new event_id
    const companyInitials = req.body.company_name
      .split(' ')        // Split the company name by spaces
      .map(word => word[0])  // Get the first character of each word
      .join('');         // Join them together
  
    event_id = `${companyInitials.slice(0, 3).toUpperCase()}${String(newIdNumber).padStart(5, '0')}`;
  }
  
  try {
    const eventData = new CorporateEventModel({
      corporate_event_id: event_id,
      ...req.body
    });
    const savedEvent = await eventData.save();

    await Promise.all(req.body.tickets.map(id =>
      TicketModel.findByIdAndUpdate(id, { event_id: savedEvent._id }, { new: true })
    ));

    res.status(200).json({ message: "Event created successfully", event_unique_ID: savedEvent.corporate_event_id });
  } catch (error) {
    res.status(500).json({ message: "Error saving event", error: error.message });
  }
};

const CreateCorporateTicket = async (req, res) => {
  try {
    const { total_Quantity, per_day } = req.body;

    // Initialize daily quantities if `per_day` is true
    //   let dailyTicketQuantities = {};
    //   let daysCount = 1;

    //   const eventDates = await CorporateEventModel.findOne({ _id: req.body.event_id });
    //   if (per_day && eventDates?.event_start_date && eventDates?.event_end_date) {
    //     let currentDate = moment(eventDates.event_starts_date);
    //     const endDate = moment(eventDates.event_ends_date);

    //     // Calculate the number of days in the event
    //     daysCount = endDate.diff(currentDate, 'days') + 1;
    //     const dailyQuantity = total_Quantity; // Set total quantity per day

    //     // Distribute the quantity per day in `daily_ticket_quantities`
    //     while (currentDate.isSameOrBefore(endDate)) {
    //       dailyTicketQuantities[currentDate.format("YYYY-MM-DD")] = dailyQuantity;
    //       currentDate.add(1, 'days');
    //     }
    //   }

    //   console.log("TICKET : REQ>BODY ", req.body,"_____________________END_________________")

    // Set the `ticket_avability` to the total quantity across the event as usual
    const NewTicket = new TicketModel({
      ticket_Name: req.body.ticket_Name,
      ticket_type: "corporate",
      // total_Quantity: per_day ? total_Quantity * daysCount : total_Quantity,
      // ticket_avability: per_day ? total_Quantity * daysCount : total_Quantity,
      total_Quantity: total_Quantity,
      ticket_avability: total_Quantity,
      ticket_price: 0,
      basic_price: 0,
      sale_start: req.body.sale_start,
      sale_start_Time: req.body.sale_start_Time,
      sale_end: req.body.sale_end,
      sale_end_Time: req.body.sale_end_Time,
      min_booking: req.body.min_booking,
      max_booking: req.body.max_booking,
      ticket_description: req.body.ticket_description,
      // daily_ticket_quantities: dailyTicketQuantities, 
      // available_daily_ticket_quantities: dailyTicketQuantities,
      isTicketForPerDay: true,
      cover_price: 0,
      isTicket_for_date: false,
      ticket_for_Date: req.body.ticket_for
    });

    await NewTicket.save()
      .then(ticketCreated => {
        res.status(200).send({ message: "Ticket Created successfully", ticketCreated });
      })
      .catch(error => {
        res.status(400).send({ message: "Unable to Create Ticket", error });
      });
  }
  catch (error) {
    res.status(500).send({ message: "Something went Wrong, try again later", error: error.message });
  }
}

const GetCorporateTicket = async (req, res) => {
  try {
    const tics = await TicketModel.findOne({ _id: req.params.ticket_id })
    if (tics) {
      res.status(200).send({ tics })
    } else {
      res.status(204).send({ message: "No tickets found" })
    }
  } catch (error) {
    res.status(500).send({ message: "Unable to Fetch Tickets", error })
  }
}

const corporateEventFetching = async (req, res) => {
  try {
    const events = await CorporateEventModel.find().populate('tickets');
    res.status(200).send(events);
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
}

const GetCorporateEvent = async (req, res) => {
  try {
    const events = await CorporateEventModel.findOne({ corporate_event_id: req.params.id }).populate('tickets');;
    res.status(200).send(events);
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
}

const GetCorporateAllEvent = async (req, res) => {
  try {
    const events = await CorporateEventModel.find({ eventCreatedBy: req.params.id }).populate('tickets');;
    res.status(200).send(events);
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
}

const GetCompanyDetailById = async (req, res) => {
  try {
    // Find the company by ID from the database
    const company = await CorporateCompanyDetailModel.findById(req.params.company_id);

    // Check if the company was found
    if (!company) {
      return res.status(404).json({ message: "Company not found" });
    }

    console.log("Company details:", company);
    // Respond with the company details if found
    res.status(200).json({ company });
  } catch (err) {
    console.error("Error retrieving company details:", err);

    // Respond with a generic error message
    res.status(500).json({ message: "Something went wrong" });
  }
}

const GenerateGateScannerLink = async (req, res) => {
  try {
    const password = crypto.randomBytes(4).toString('hex').toUpperCase();
    const uniqueKey = Math.floor(100 + Math.random() * 900).toString();

    const eventName = await CorporateEventModel.findOne({ corporate_event_id: req.body.event_id });

    const event_idFound = await ScannerLinkData.findOne({ corporate_event_id: req.body.event_id })
    if (event_idFound) {
      return res.status(201).json({ message: "Link already Created for this event", eventName: eventName.event_name })
    }

    const response = new ScannerLinkData({
      event_id: req.body.event_id,
      login_id: req.body.loginId + "ts" + uniqueKey,
      password: password
    })
    await response.save();
    res.status(200).json({ message: "Scanner link generated", eventName });

  } catch (error) {
    res.status(500).send({ message: "Internal Server Error", error: error.message });
  }
};

const GetGeneratedGateScannerLink = async (req, res) => {
  const { id } = req.params;
  try {
    const getLinks = await ScannerLinkData.findOne({ event_id: id });
    if (getLinks) {
      res.status(200).json({ Links: getLinks })
    } else {
      res.status(201).json({ message: "No data found" });
    }

  } catch (error) {
    res.status(500).json({ message: "Server error", err: error.message })
  }
}

const IndividualCorporateEventData = async (req, res) => {
  try {
    // Fetch the event and populate related data
    const event = await CorporateEventModel.findOne({ corporate_event_id: req.params.event_id }).populate('tickets');
    res.status(200).send(event);

  } catch (err) {
    res.status(500).send({ message: "Something went wrong", error: err.message });
  }
};

const CorporateEventBookInvoiceDetails = async (req, res) => {
  const ticket_id = req.query.ticket_id;
  try {
    const event = await CorporateEventModel.find({ _id: req.params.id }).populate('tickets');
    if (event.length > 0) {
      const eventDetails = {
        _id: event[0]._id,
        corporate_event_id: event[0].corporate_event_id,
        event_name: event[0].event_name,
        event_starts_date: event[0].event_start_date,
        event_starts_Time: event[0].event_start_Time,
        event_venue: event[0].event_venue,
        event_city: event[0].event_city,
        basic_price: event[0].basic_price,
        cover_image: event[0].cover_image,
        eventCreatedBy: event[0].eventCreatedBy,
      }
      // const ticket = event[0].ticket
      const ticket = event[0].tickets.find(t => t._id.toString() === ticket_id);

      res.status(200).send({ event: eventDetails, ticket: ticket });
    } else {
      res.status(201).send({ message: "No events are available" });
    }
  } catch (err) {
    res.status(500).send({ message: "Something went wrong" });
  }
};

const GenerateEmpLogin = async (req, res) => {
  try {
    // Find the employee using the Employee ID
    const FindEmp = await EmployeeModel.findOne({ Employee_ID: req.body.Emp_id });
    console.log("Found Employee:", FindEmp);

    // If employee is not found, return 401 status
    if (!FindEmp) {
      return res.status(401).json({ message: "Employee verification failed" });
    }

    // Check if the email matches
    if (FindEmp.Employee_Email_ID !== req.body.Email) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    const isEmpAlreadyExisit = await CorporateUserModel.findOne({
      Email: req.body.Email, Emp_id: req.body.Emp_id,
      Phone_number: req.body.phone_number
    })

    console.log("Emp Already Exisit : ", isEmpAlreadyExisit)
    if (isEmpAlreadyExisit) {
      return res.status(400).json({ message: "You are already registered!", isEmpAlreadyExisit });
    }
    const SaveDeatils = new CorporateUserModel({
      First_name: req.body.firstName,
      Last_name: req.body.lastName,
      Email: req.body.Email,
      Emp_id: req.body.Emp_id,
      Phone_number: req.body.phone_number,
      Gender: req.body.gender,
      DateOfBirth: req.body.dob,
    })

   const empSaved = await SaveDeatils.save()
    // Return the employee details if everything is valid
    const employee = {
      _id: empSaved._id,
      Employee_Name: SaveDeatils.First_name + " " + SaveDeatils.Last_name,
      Email: SaveDeatils.Email,
      Phone_number: SaveDeatils.Phone_number
    };

    res.status(200).json({ message: "Employee verified!", employee });
  } catch (error) {
    // Log the error and return a generic error message
    console.error("Login Error:", error);
    res.status(500).json({ message: "Login error", error: error.message });
  }
};

const PocLoginVerification = async (req, res) => {
  try {
    // Find the employee using the Employee ID
    const FindEmp = await CorporateCompanyDetailModel.findOne({ poc_contact_number: req.body.login_id });
    console.log("Found Employee:", FindEmp);

    // If employee is not found, return 401 status
    if (!FindEmp) {
      return res.status(401).json({ message: "Employee verification failed" });
    }

    // Check if the email matches
    if (FindEmp.passcode !== req.body.password) {
      return res.status(400).json({ message: "Invalid credentials" });
    }

    // Return the employee details if everything is valid
    const employee = {
      _id: FindEmp._id,
      company_name: FindEmp.company_name
    };

    res.status(200).json({ message: "Employee verified!", employee });
  } catch (error) {
    // Log the error and return a generic error message
    console.error("Login Error:", error);
    res.status(500).json({ message: "Login error", error: error.message });
  }
};

// Import `fetch` dynamically if node-fetch is an ESM
const loadFetch = async () => {
  const fetchModule = await import('node-fetch');
  return fetchModule.default;
};


const formatTimespanDate = (timestamp) => {
  const date = new Date(timestamp);

  const options = {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  };

  return date.toLocaleString("en-US", options);
};

const encodeBase64 = async (filePath) => {
  const fetch = await loadFetch(); // Load fetch dynamically
  if (filePath.startsWith("http")) {
    const response = await fetch(filePath);
    const buffer = await response.buffer();
    return buffer.toString("base64");
  } else {
    const fileData = fs.readFileSync(filePath);
    return fileData.toString("base64");
  }
};

const fetchEjsTemplate = async (url) => {
  const fetch = await loadFetch(); // Load fetch dynamically
  const response = await fetch(url);
  return response.text();
};

const formatDate = (dateString) => {
  const dateObj = new Date(dateString);
  const day = String(dateObj.getDate()).padStart(2, '0');
  const monthNames = [
    "Jan", "Feb", "Mar", "Apr", "May", "June",
    "July", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  const month = monthNames[dateObj.getMonth()];
  const year = String(dateObj.getFullYear()).slice(-2);
  return `${day}-${month}-${year}`;
};


const SendMailToCorporate = async (req, res) => {
  try {
    console.log("Fetching ticket details...");
    const ticket = await CorporateBookingModel.findOne({
      order_id: req.body.orderId,
    });

    if (!ticket) {
      return res.status(404).send("Ticket not found.");
    }

    // let invoiceId = "MFSGST00000";
    // const lastNumber = parseInt(invoiceId.substring(6));
    // const newIdNumber = lastNumber + 1;
    // invoiceId = `MFS${String(newIdNumber).padStart(5, "0")}`;

    // console.log("Invoice ID:", invoiceId);

    let invoiceId = `MFS${Date.now()}`;

    const baseUrl =
      process.env.RAILWAY_ENVIRONMENT_NAME === "production"
        ? "https://my-front-seat-backend-production-3e06.up.railway.app"
        : "http://localhost:3001";

    const antsLogoPath = `${baseUrl}/Ejs/antsLogo.png`;
    const mfsLogoPath = `${baseUrl}/Ejs/MFSLogo.png`;
    const SignPath = `${baseUrl}/Ejs/sign.png`;

    console.log("Encoding images to base64...");
    const antsLogoBase64 = await encodeBase64(antsLogoPath);
    const mfsLogoBase64 = await encodeBase64(mfsLogoPath);
    const SignPathBase64 = await encodeBase64(SignPath);

    const bookingFeeInWords = toWords(req.body.bookingfee);

    const Organizer = await CorporateEventModel.findById(req.body.vendor_id);

    console.log("ORRRGAAANNNNIIIZZEERRR : ", Organizer);

    const eventCity = req.body.event_city.trim();

    console.log("Processing event_city:", eventCity);

    const state = await StateModel.findOne({ City: eventCity });

    console.log("STATE  : ", state);

    const invoiceData = {
      invoiceNo: invoiceId,
      date: ticket.eventreserveDate,
      dateofBooking: formatTimespanDate(ticket.createdAt),
      order_id: ticket.order_id,
      UserName: req.body.userName,
      Email: req.body.email,
      eventName: req.body.event_name,
      basicRate: req.body.basicRate,
      totalBasicPrice: req.body.totalBasicPrice,
      booking_fee: req.body.bookingfee,
      ticketQuantity: ticket.bookedTicketQuantities,
      GST: req.body.gst,
      state: state.State || " ",
      stateCode: state.GSTIN_Code || " ",
      phoneNo: req.body.phone_no,
      amount: ticket.amount / 100,
      transaction: ticket.payment_id || " ",
      payment_mode: ticket.payment_mode || " ",
      paid_amount: ticket.amount / 100,
      antsLogoBase64,
      mfsLogoBase64,
      SignPathBase64,
      bookingFeeInWords,
      Organizer_Name: Organizer.company_name,
    };

    console.log("Rendering invoice template...");
    const invoiceHtml = await fetchEjsTemplate(
      `${baseUrl}/invoice/Invoice.ejs`
    ).then((template) => ejs.render(template, invoiceData));

    console.log("Generating PDF with Puppeteer...");
    const browser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();
    await page.setContent(invoiceHtml);
    const pdfBuffer = await page.pdf({
      format: "A4",
      margin: {
        top: "15mm",
        bottom: "15mm",
        left: "10mm",
        right: "10mm",
      },
    });
    await browser.close();

    const transporter = nodemailer.createTransport({
      host: "smtp.gmail.com",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: "smyqjahvhwmijbrr", // Use an App Password if using Gmail
      },
    });

    console.log("Generating QR Code...");
    const qrCodeData = `${ticket.tickets[0]}_${req.body.orderId}`;
    const qrCodeSVG = await QRCode.toString(qrCodeData, { type: "svg" });
    const qrCodeImageBuffer = await sharp(Buffer.from(qrCodeSVG), {
      density: 300,
    })
      .png()
      .toBuffer();

    console.log("Preparing ticket data...");
    const ticketData = {
      cover_image: `${baseUrl}/uploads/${req.body.cover_image}`,
      order_id: req.body.orderId,
      ticket_id: ticket.tickets[0],
      amount: ticket.amount / 100,
      eventName: req.body.event_name,
      bookings: ticket.bookedTicketQuantities,
      ticketName: req.body.ticketName,
      venue: req.body.event_venue,
      time: req.body.event_time,
      date: ticket.eventreserveDate,
      status: ticket.isPaid ? "Amount paid" : "Free",
      QRCodeSVG: qrCodeSVG,
      UserName: req.body.userName,
    };

    console.log("Rendering ticket template...");
    const ticketHtml = await fetchEjsTemplate(
      `${baseUrl}/invoice/ticketview.ejs`
    ).then((template) => ejs.render(template, ticketData));

    console.log("Generating ticket image with Puppeteer...");
    const ticketBrowser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const ticketPage = await ticketBrowser.newPage();
    await ticketPage.setContent(ticketHtml);
    const ticketImageBuffer = await ticketPage.screenshot({ type: "png" });
    await ticketBrowser.close();

    const emailHtml = `
      <div>
        <h3>Hi ${req.body.userName},</h3>
        <p>Thank you for booking with <b>My Front Seat!</b> Your tickets for ${
          req.body.event_name
        } are confirmed. Below are your ticket details:</p>
        <h5>Event Details:</h5>
        <ul>
          <li><b>Event: ${req.body.event_name}</b></li>
          <li><b>Date: ${req.body.event_date}</b></li>
          <li><b>Time: ${req.body.event_time}</b></li>
          <li><b>Venue: ${req.body.event_venue}</b></li>
          <li><b>Seats: ${req.body.event_seats}</b></li>
        </ul>
        <p>Access tickets: ${req.body.ticket_link}</p>
      //  <div 
      //  style="width: 300px; 
      //  height: 370px;
      //  background-image: url('https://storage.googleapis.com/my-front-seat-bucket2024/tickettemplate/Ticket%20template-mail.png');
      //  background-repeat: no-repeat;">
      //  <div style="padding: 0.1rem 0.5rem">
      //  <p style="text-align: center; font-size: 26px; font-weight: bold;">
      //  ${req.body.event_name}
      //   </p>
      //    <div>
      //       <table style="width: 100%;">
      //         <thead>
      //           <tr>
      //             <th style="width: 30%; text-align:center">
      //               <span style="color: #6a6a6a;">Venue</span>
      //             </th>
      //             <th style="width: 30%; text-align:center">
      //               <span style="color: #6a6a6a;">Time</span>
      //             </th>
      //             <th style="width: 40%; text-align:center">
      //               <span style="color: #6a6a6a;">Date</span>
      //             </th>
      //           </tr>
      //         </thead>
      //         <tbody>
      //           <tr>
      //             <td style="width: 30%; text-align:center">
      //               <span style="color: black;">
      //                  ${req.body.event_venue}
      //               </span>
      //             </td>
      //             <td style="width: 30%; text-align:center">
      //               <span style="color: black;">
      //                 ${req.body.event_time}
      //               </span>
      //             </td>
      //             <td style="width: 40%; text-align:center">
      //                 <span style="color: black;">
      //                     ${formatDate(ticket.eventreserveDate)}
      //                 </span>
      //             </td>
      //           </tr>
      //         </tbody>
      //       </table>
      //     </div>
      //     <div style="padding: 0rem 0.5rem;">
      //     <hr/>
      //     </div>
      //    <div style="width: 40%; margin: auto;">
      //   <img src="cid:qrCodeImage" alt="QR Code" style="width: 100%; height: auto;"/>
      // </div>
      //     <div style=" padding: 0.2rem 0.5rem;">
      //         <table style="width: 100%; ">
      //             <tr>
      //                 <td style="width: 30%; text-align: start;  white-space: nowrap;">
      //                     <span>
      //                         ${ticket.bookedTicketQuantities} x ${
      req.body.ticketName
    }
      //                     </span>
      //                 </td>
      //                 <td style="width: 70%; text-align: end;">
      //                     <span>
      //                         Booked by: ${req.body.userName}
      //                     </span>
      //                 </td>
      //             </tr>
      //         </table>
      //     </div>
      //     <div style="padding: 0rem 0.5rem;">
      //     <hr/>
      //     </div>
      //     <div style="margin-top: 20px; padding-left: 0.4rem">
      //         <span
      //             style="background-color: #00AC07; color: white; border-radius: 0.5rem; padding: 0.5rem 1.5rem;">
      //             ${ticket.isPaid ? "Amount paid" : "Free"}
      //         </span>
      //     </div>
      //  </div>
      //  </div>
        <p>Please present the attached <b>QR code(s)</b> at the event entrance for quick and easy entry.</p>
        <span><b>Need Help</b></span>
        <p>If you have any questions, please contact support at <b>+91 9226773937, <EMAIL></b></p>
        <span><b>Reminder:</b></span>
        <ul>
          <li>Plan to arrive at least 60 minutes before the event start time.</li>
          <li>Check any event-specific guidelines on our website www.myfrontseat.in.</li>
        </ul>
        <p>Thank you for choosing <b>My Front Seat</b>. We look forward to seeing you at the event!</p>
      </div>
    `;

    console.log("Sending email...");
    await transporter.sendMail({
      from: '"My Front Seat" <<EMAIL>>',
      to: req.body.email,
      subject: `Your Ticket For ${req.body.event_name}`,
      text: "View Your Ticket",
      html: emailHtml,
      attachments: [
        {
          filename: "ticket.png",
          content: ticketImageBuffer,
          contentType: "image/png",
        },
        {
          filename: "qrCode.png",
          content: qrCodeImageBuffer,
          contentType: "image/png",
          cid: "qrCodeImage", // This matches the 'cid' used in the HTML email
        },
      ],
    });

    console.log("Mail sent successfully.");
    res.send("Mail sent successfully with invoice and ticket image");
  } catch (error) {
    console.error("Error in SendMail:", error);
  }
};

const FetchUserDataByEvent = async (req, res) => {
  const { event_id } = req.params;
  try {
    const bookings = await CorporateBookingModel.find({ eventDetails: event_id })
      .populate("tickets")
      .populate("eventDetails");

    if (bookings && bookings.length > 0) {
      const userErrors = [];

      // Loop through each booking and attempt to find user data
      const usersData = await Promise.all(
        bookings.map(async (booking) => {
          try {
            // Find user data by user ID or fetch related user data if required
            const user = await CorporateUserModel.findById(booking.user); // Assuming `UserModel` is the user schema
            if (user) {
              return { booking, user };
            } else {
              throw new Error(`User with ID ${booking.user} not found`);
            }
          } catch (err) {
            userErrors.push({ bookingId: booking._id, error: err.message });
            return null; // Continue processing other bookings
          }
        })
      );

      const filteredUsersData = usersData.filter((data) => data !== null); // Exclude null entries

      // Return response with bookings, user data, and any errors encountered
      res.status(200).json({
        message: "Booked Events",
        users: filteredUsersData,
        userErrors: userErrors.length ? userErrors : undefined,
      });
    } else {
      res.status(404).json({ message: "No Ticket Booked" });
    }
  } catch (err) {
    console.error("Error fetching booked events:", err);
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
};

const CorporateDashBoardMatrices = async (req, res) => {
  try {
    const data = await CorporateEventModel.find().populate('tickets');
    const bookings = await CorporateBookingModel.find().populate('tickets');

    let event_count = 0;
    let ticket_count = 0;
    let ticket_sell = 0;
    let revenue = 0;

    for (let i = 0; i < data.length; i++) {
      event_count += 1;

      // Check if tickets exist for the event
      if (data[i].tickets && data[i].tickets.length > 0) {
        for (let j = 0; j < data[i].tickets.length; j++) {
          ticket_count += data[i].tickets[j].total_Quantity;
          ticket_sell += data[i].tickets[j].total_Quantity - data[i].tickets[j].ticket_avability;
        }
      }

      const soldData = await CorporateBookingModel.find({ eventDetails: data[i]._id });
      // console.log("SOLDDATA_DASHBORD : ", soldData)
      if (soldData && soldData.length) {
        for (let k = 0; k < soldData.length; k++) {
          if (soldData[k].isPaid) {
            revenue += (soldData[k].amount) / 100;
          }
        }
      }
    }
    if (data && data.length > 0) {
      res.status(200).json({
        event_hosted: event_count,
        ticket_hosted: ticket_count,
        ticket_sold: ticket_sell,
        ticket_revenue: revenue,
        bookings: bookings
      });
    } else {
      res.status(404).json({ message: "No data found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).send({ message: "Something went wrong" });
  }

}

const NoOfTicketScanned = async (req, res) => {
  let NoOfScannedTicket = 0
  try {
    const NoOfScan = await ScannerLinkData.find({ event_id: req.params.event_id })
    if (NoOfScan && NoOfScan.length > 0) {
      for (let i = 0; i < NoOfScan.length; i++) {
        NoOfScannedTicket += NoOfScan.total_no_of_Customer_attended;
      }
    }
    res.status(200).json({ NoOfScannedTicket });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
}

const StoreFreeCorporateTickets = async (req, res) => {
  const order_id = crypto.randomBytes(6).toString('hex').toUpperCase();
  console.log("FREETICKET : ", req.body)
  try {
    const freeTicketBooking = new CorporateBookingModel(
      {
        user: req.body.user_id,
        order_id: order_id,
        tickets: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)), // Convert IDs to ObjectId
        eventDetails: req.body.eventDetails,
        amount: req.body.amount,
        receipt: "ticket",
        payment_id: null,
        paid_Amount: 0,
        isPaid: true,
        isfree: true,
        status: "Booked",
        bookedTicketQuantities: req.body.quantity,
        available_entry: req.body.quantity,
        eventreserveDate: req.body.eventreserveDate
      }
    );

    await freeTicketBooking.save();
    const ticket = await TicketModel.findOne({ _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) });
    const updatedTicket = await TicketModel.findOneAndUpdate(
      { _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) },
      {
        $inc: { ticket_avability: - req.body.quantity } // Decrement availability
      },
      { new: true }
    );
    if (ticket.isTicketForPerDay) {
      const updatedavailable_daily_ticket_quantities = await TicketModel.findOneAndUpdate(
        { _id: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)) },
        {
          $inc: { [`available_daily_ticket_quantities.${req.body.eventreserveDate}`]: -req.body.quantity } // Decrement availability
        },
        { new: true }
      );
    }

    res.status(200).json({ message: "Ticket save successful!", freeTicketBooking });
  } catch (e) {
    return res.status(500).json({ message: "Ticket save failed!", error: e.message });
  }
}

const FetchCorporateBookedEvent = async (req, res) => {
  const { user_id } = req.params;
  try {
    const bookedEvents = await CorporateBookingModel.find({ user: user_id }).populate('tickets').populate('eventDetails');
    if (bookedEvents && bookedEvents.length > 0) {
      res.status(200).json({ message: "Booked Events", data: bookedEvents })
    } else {
      res.status(404).json({ message: "No Ticket Booked" })
    }
  }
  catch (err) {
    console.error("Error fetching booked events:", err); // Log error details
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
}

const SaveNewCorporateCompanyDetail = async (req, res) => {
  const uniquecode = crypto.randomBytes(4).toString('hex').toUpperCase();
  try {
    const { poc_contact_number, poc_email } = req.body;

    console.log("POC Contact Number received: ", poc_contact_number);
    console.log("POC Email received: ", poc_email);

    // Check if the phone number already exists
    const pocNumberExists = await CorporateCompanyDetailModel.findOne({ poc_contact_number: poc_contact_number });
    if (pocNumberExists) {
      return res.status(400).json({ message: "Phone number already registered" });
    }

    // Check if the email already exists
    const pocEmailExists = await CorporateCompanyDetailModel.findOne({ poc_email: poc_email });
    if (pocEmailExists) {
      return res.status(400).json({ message: "Email ID already created" });
    }

    // If both are unique, proceed to create the new company detail
    const companyDetail = new CorporateCompanyDetailModel({
      passcode: uniquecode,
      role: "corporate",
      ...req.body,
    });

    const savedCompany = await companyDetail.save();
    res.status(201).json({ message: "Company created successfully", company: savedCompany });

  } catch (error) {
    res.status(500).json({ message: "Error saving company details", error: error.message });
  }
};
;

const CompanyName = async (req, res) => {
  try {
    const companies = await CorporateCompanyDetailModel.find();
    res.status(200).send(companies);
  }
  catch (err) {
    res.status(500).send({ message: "Something went wrong", error: err.message });
  }
};

const GetCorporateComapny = async (req, res) => {
  try {
    const company = await CorporateCompanyDetailModel.find();
    res.status(200).send(company);
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
}

const GetEventCounts = async (req, res) => {
  console.log("eventCreatedBy:req.params.company_id", req.params.company_id)
  try {
    const eventCount = await CorporateEventModel.find({ eventCreatedBy: req.params.company_id });
    if (eventCount && eventCount.length > 0) {
      return res.status(200).json({ eventCount: eventCount.length });
    }
    res.status(200).json({ eventCount: 0 });
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).json({ message: "Something went wrong" });
  }
}

const FetchCorporateBookedEventByBookingId = async (req, res) => {
  const { order_id } = req.params;
  try {
    const bookedEvents = await CorporateBookingModel.findOne({ _id: order_id }).populate('tickets').populate('eventDetails');
    if (bookedEvents) {
      const username = await EmployeeModel.findById(bookedEvents.user)
      res.status(200).json({ message: "Booked Events", data: bookedEvents, username: username.Employee_Name })
    } else {
      res.status(404).json({ message: "No Ticket Booked" })
    }
  }
  catch (err) {
    console.error("Error fetching booked events:", err); // Log error details
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
}

const DeleteEventRecord = async (req, res) => {
  try {
    // Find the event by ID
    const deleteCompanyEvents = await CorporateEventModel.findByIdAndDelete(req.params.event_id);
    console.log("DeleteCompanyEvents : ", deleteCompanyEvents)
    if (!deleteCompanyEvents) {
      return res.status(404).json({ message: "Event not found" });
    }

    // Delete employees associated with the event (if any)
    const deleteEmployeeData = EmployeeModel.deleteMany({ uniqueCode: deleteCompanyEvents.empDataPasscode });
    
    // If tickets exist, delete them
    let deleteTicketsPromises = [];
    if (deleteCompanyEvents.tickets && deleteCompanyEvents.tickets.length > 0) {
      deleteTicketsPromises = deleteCompanyEvents.tickets.map(ticketId => TicketModel.findByIdAndDelete(ticketId));
    }

    // Wait for employee and ticket deletions to complete
    await Promise.all([deleteEmployeeData, ...deleteTicketsPromises]);

    // Send success response
    res.status(200).json({ message: "Event deleted successfully!" });
  } catch (error) {
    console.error("Error in deleting event record:", error);  // Log error with context
    res.status(500).json({ message: "Unable to delete event record" });
  }
};


const DeleteCompanyRecord = async (req, res) => {
  try {
    // Delete the company record
    const deleteCompany = await CorporateCompanyDetailModel.findByIdAndDelete(req.params.company_id);
    if (!deleteCompany) {
      return res.status(404).json({ message: "Company not found" });
    }

    // Delete events created by the company
    const deleteCompanyEvents = await CorporateEventModel.find({ eventCreatedBy: req.params.company_id });

    if (deleteCompanyEvents.length > 0) {
      const ticketDeletionPromises = deleteCompanyEvents.map(async (event) => {
        const deleteCompanyEvent = await CorporateEventModel.findByIdAndDelete(event._id);
        // Delete employees associated with the event (if any)
        const deleteEmployeeData = EmployeeModel.deleteMany({ uniqueCode: event.empDataPasscode });
        
        // If tickets exist, delete them
        if (event.tickets && event.tickets.length > 0) {
          const deleteTicketsPromises = event.tickets.map(ticketId => TicketModel.findByIdAndDelete(ticketId));
          await Promise.all(deleteTicketsPromises);  // Wait for ticket deletion
        }

        // Wait for employee deletion as well
        await deleteEmployeeData;
      });

      // Wait for all event-related deletions to complete
      await Promise.all(ticketDeletionPromises);
    }

    // Send success response
    res.status(200).json({ message: "Company deleted successfully!" });
  } catch (error) {
    console.error("Error in deleting company record:", error);  // Log error with context
    res.status(500).json({ message: "Unable to delete company record" });
  }
};


const FetchCorporateBookedTicket = async (req, res) => {
  try {
      const { booking_id, ticket_id } = req.params;

      // Find the booking by booking_id and populate the referenced event and tickets
      const booking = await CorporateBookingModel.findOne({order_id:booking_id}).populate('eventDetails').populate('tickets');

      if (!booking) {
          return res.status(404).json({ message: 'Booking not found' });
      }
      const userName = await CorporateUserModel.findOne({_id:booking.user});
      console.log("USER : ", booking.user, userName)
      // Check if the ticket_id exists in the tickets array
      const ticket = booking.tickets.find(t => t._id.toString() === ticket_id);
      const event = booking.eventDetails

      if (!ticket) {
          return res.status(404).json({ message: 'Ticket not found in this booking' });
      }

      // Respond with the booking and ticket details
      res.status(200).json({
        booking: booking,
        ticket: ticket,
        event: event,
        userName:userName.First_name + " " + userName.Last_name
      });
  } catch (error) {
      res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const UpdateValidateCorporateEntry = async (req, res) => {
  try {

    const { id } = req.params;
    const { total_no_of_Guest, no_of_Guest_attended, attended_event_ticket_id } = req.body;


    const updateEntry = await ScannerLinkData.findByIdAndUpdate(
      id,
      {
        total_no_of_Guest, // ES6 shorthand
        no_of_Guest_attended, // ES6 shorthand
        $push: { attended_event_ticket_id }, // Push the ticket ID to the array
        $inc: { total_no_of_Customer_attended: no_of_Guest_attended } // Increment by the number of attended guests
      },
      { new: true, upsert: false } // Return updated document without creating a new one if not found
    );
    const updatePendingGuestEntry = await CorporateBookingModel.findOneAndUpdate(
      { order_id: req.body.order_id },
      {
        $inc: { available_entry: -no_of_Guest_attended },
      },
      { new: true, upsert: false }
    );

    if (!updateEntry) {
      return res.status(404).json({ error: 'Entry not found' });
    }


    return res.status(200).json({ message: 'Entry updated successfully', data: updateEntry });

  } catch (error) {
    // Handle errors
    console.error(error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};


module.exports = {
  corporateEventImages, corporateEventCreation, corporateEventFetching,
  GetCorporateEvent, GenerateGateScannerLink, GetGeneratedGateScannerLink,
  IndividualCorporateEventData, CreateCorporateTicket, GetCorporateTicket,
  CorporateEventBookInvoiceDetails, GenerateEmpLogin, SendMailToCorporate,
  FetchUserDataByEvent, CorporateDashBoardMatrices, NoOfTicketScanned,
  StoreFreeCorporateTickets, FetchCorporateBookedEvent, StoreExcelSheetInDB,
  SaveNewCorporateCompanyDetail, CompanyName, GetCorporateComapny, GetEventCounts,
  GetCompanyDetailById, FetchCorporateBookedEventByBookingId, GetCorporateAllEvent,
  PocLoginVerification, DeleteCompanyRecord, DeleteEventRecord, FetchCorporateBookedTicket,
  UpdateValidateCorporateEntry
};