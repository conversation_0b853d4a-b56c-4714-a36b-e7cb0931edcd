const MONGODB= require("mongoose");

const TicketSchema = new MONGODB.Schema({
    ticket_Name: {type: String, required: true},
    ticket_type: {type: String, required: true},
    total_Quantity: {type: Number, required: true},
    ticket_avability: {type: Number},
    bookingQuantities : {type: Number, default: 0},
    ticket_price: {type: Number},
    basic_price : {type: Number, default: 0},
    cover_price : {type: Number, default: 0},
    sale_start: {type: String},
    sale_start_Time: {type: String},
    sale_end: {type: String},
    sale_end_Time: {type: String},
    min_booking: {type: Number},
    max_booking: {type: Number},
    ticket_description: {type: String},
    reserveDate : {type: String},
    event_id:{type: MONGODB.Schema.Types.ObjectId, ref:'CeremonyModel'},
    isTicketForPerDay: {type: Boolean},
    ticket_for_Date : {type: String},
    ticket_for_city : {type: String},
    event_show_time : {type: String},
    ticket_for_city_venue : {type: String},
    isTicket_for_date: {type: Boolean},
    daily_ticket_quantities: { type: Map, of: Number }, 
    available_daily_ticket_quantities: { type: Map, of: Number },
    showTicketSoldOut : {
        type: Boolean,
        default: false
    }
});

const TicketModel = MONGODB.model("Ticket",TicketSchema);
module.exports = TicketModel;