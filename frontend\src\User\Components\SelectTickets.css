.ticketlist{
    max-width: 729px;
    margin: auto;
    padding-top: 68px;
}

.dropdown-container {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s ease, opacity 0.3s ease;
    opacity: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* padding: 0.5rem; */
    cursor: pointer;
    
  }
  
  .dropdown-container.expanded {
    max-height: 200px; /* adjust based on actual content height */
    opacity: 1;
    margin: 2px;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
#smalldeviceview{
    display: none !important;
}
.event-heading{
    display: flex;
    justify-content: center;
}
.booking-btn {
    background: #2C9CF0;
    color: #ffffff;
    width: 218px;
    height: 50px;
    
    border: none;
}
.backarrowlink{
    position: relative;
} 
.link-dark:hover{
    color: #2C9CF0;
}
.hedingcontent{
    margin-left: 20%;
}
.event-name{
    
    font-size: 30px;
    font-weight: bold;
    text-align: center;
}
.event-detail{
    
    font-size: 15px;
    text-align: center;
}
.head{
    
    font-size: 20px;
    font-weight: bold;
    margin-top: 21px;
}
.listoftickets{
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    flex-wrap: wrap;
}
.listofcity{
    min-height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 2px;
    padding: 0.5rem;
    cursor: pointer;
}

.highlightSelection{
    background-color: #2C9CF0;
    color: #fff;
}
.highlightSelection-city{
    /* border: 1px solid #2C9CF0; */
    background-color: #2c9bf03b;
}
.ticketDetails{
    display: flex;
    flex-direction: column;
}
.ticketname{
    
    font-size: 20px;
    font-weight: bold;
}
.ticketdescription{
    
    font-size: 15px;
}
.ticketcount{
    
    font-size: 1rem;
}
.couting{
    font-size: 1.5rem;
    /* margin-top: 0.2rem; */
    cursor: pointer;
}
.footerPart{
    width: 100%;
    /* height: 80px; */
    position: fixed; 
    background: #ffff;
    bottom: 0;
    box-shadow: 0 -2px 3px rgba(0, 0, 0, 0.14)
}
.footer-content{
    max-width: 729px;
    margin: auto;
    display: flex;
    justify-content: end;
    align-items: center;
}
.ticketPrice{
    
    font-size: 24px;
}
.totalCount{
    
    margin-top: -10px;
    font-size: 20px;
    color: #6A6A6A;
}
.proceedbtn{
    color: white;
    font-size: 14px !important;
    width: 124px;
    height: 45px;
    border: none !important;
    border-radius: 0.5rem;
}
.btnisDisabled{
    background: #6A6A6A !important  ;
}
.btnisEabled{
    background: #2C9CF0 !important  ;
}
.add-btn{
    background: #2C9CF0;
    color: #ffff;
    border: none;
    border-radius: 1rem;
    padding: 0.1rem 1rem;
    font-size: 15px;
}
.transition-all{
    transition: all 0.5s ease !important;
}
/* Mobile view */
@media screen and (max-width:578px){
    .select-ticket {
        /* height: 100vh !important; */
        min-height: 100vh;
        min-height: -webkit-fill-available;
        min-height: -moz-available;
        min-height: fill-available;
        overflow-y: hidden;
        padding-bottom: 80px;
        position: relative;
    }
    .booking-btn {
        display: none;
    }
    .dropdown-container.expanded {
        max-height: 200px; /* adjust based on actual content height */
        opacity: 1;
        margin: 1rem;
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
      }
    .btn-booking {
        height: 40px;
        width: 160px;
        font-size: 18px !important;
        
        background: #2C9CF0;
        border: none !important;
        border-radius: 0.5rem !important;
    }
    .ticketlist{
        max-width: 100%;
        margin-top: 0px;
        margin-bottom: 172px;
        padding-top: 0px;
    }
    .event-heading{
        display: flex;
        padding: 0rem 0.5rem;
    } 
    #smalldeviceview{
        display: block !important;
    }
    #largedeviceview{
        display: none !important;
    }
    .backarrowlink{
        position: relative;
        left: 0;
    }
    .event-name{
        
        font-size: 18px;
        text-align: center;
    }
    .event-detail{
        
        font-size: 14px !important;
        text-align: center;
    }
    .head{
        font-weight: bold;
        margin: 0px 1rem;
        margin-top: 16px;
    }
    .listoftickets{
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fff;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        flex-wrap: wrap;
    }

    .listofcity{
        min-height: 56px !important;
        display: flex;
        justify-content: space-between;
        /* box-shadow: 1px 2px #bdbcbc; */
        /* border: 1px solid #D9D9D9; */
        margin: 0px 1rem;
        padding: 0.1rem 0.5rem;
    }
    .ticketname{
        
        font-size: 14px;
        font-weight: bold;
        margin-top: 5px;
    }
    .ticketdescription{
        
        font-size: 14px;
        margin-top: 10px;
        color: #6A6A6A;
    }
    .ticketcount{
        
        font-size: 1rem;
    }
    .couting{
        
    }

    .add-btn{
        background: #2C9CF0;
        color: #ffff;
        border: none;
        border-radius: 1rem;
        padding: 0.1rem 1rem;
        font-size: 10px;
        margin: none !important;
        display: flex;
        justify-content: end;
        font-size: 0.8rem;
    }
    
    .smallfooterPart {
        width: 100%;
        position: fixed; 
        background: #fff;
        bottom: 0; /* Change from bottom: 1% to bottom: 0 */
        left: 0; /* Ensure it stays aligned with the screen */
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .ticketPrice{
        font-size: 25px;
        
        font-weight: bold !important;
    }
    .totalCount{
        
        font-size: 16px;
        color: #6A6A6A;
    }
    .proceedbtn{
        height: 40px;
        width: 160px;
        font-size: 14px !important;
        
        color: white;
        border: none !important;
        border-radius: 0.5rem !important;
    }

}