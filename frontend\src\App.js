import { signOut } from "firebase/auth";
import { useEffect, useRef, useState, lazy, Suspense } from "react";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";
import "./App.css";
import auth from "./firebaseConfig";
import axios from "axios";
import { API_URL } from "./config";
import {
  onMessageListener,
  requestFCMToken,
} from "./utils/firebaseconfig_notification";
import { PuffLoader } from "react-spinners";

// Lazy-loaded components
const HomePage = lazy(() => import("./User/Pages/HomePage"));
const LoginPage = lazy(() => import("./User/Pages/LoginPage"));
const SignUpPage = lazy(() => import("./User/Pages/SignUpPage"));
const AboutUsPage = lazy(() => import("./User/Pages/AboutUsPage"));
const CostumerSupportPage = lazy(() =>
  import("./User/Pages/CostumerSupportPage")
);
const CategoryPage = lazy(() => import("./User/Pages/CategoryPage"));
const ProductPage = lazy(() => import("./User/Pages/ProductPage"));
const SelectCity = lazy(() => import("./User/Components/SelectCity"));
const TicketPage = lazy(() => import("./User/Pages/TicketPage"));
const DateSelect = lazy(() => import("./User/Components/DateSelect"));
const BookingPage = lazy(() => import("./User/Pages/BookingPage"));
const PrivacyPolicy = lazy(() => import("./User/Pages/PrivacyPolicy"));
const TermsCondition = lazy(() => import("./User/Pages/TermsCondition"));
const UserProfile = lazy(() => import("./User/Pages/UserProfile"));
const SingleBookedTicket = lazy(() =>
  import("./User/Pages/SingleBookedTicket")
);
const YourBookedTicket = lazy(() => import("./User/Pages/YourBookedTicket"));
const RewardsPage = lazy(() => import("./User/Pages/RewardsPage"));
const ListyourEventPage = lazy(() => import("./User/Pages/ListyourEventPage"));
const SucessBookingTicketPage = lazy(() =>
  import("./User/Pages/SucessBookingTicketPage")
);
const HostedEventPage = lazy(() => import("./User/Pages/HostedEventPage"));
const SearchPage = lazy(() => import("./User/Pages/SearchPage"));
const CorporateTicketSelectPage = lazy(() =>
  import("./User/Pages/CorporateTicketSelectPage")
);
const CorpSelectDate = lazy(() => import("./User/Components/CorpSelectDate"));
const CorporateEventBookingPage = lazy(() =>
  import("./User/Pages/CorporateEventBookingPage")
);
const CorporateSucessBookingTicketPage = lazy(() =>
  import("./User/Pages/CorporateSucessBookingTicketPage")
);
const CorporateEventProfilePage = lazy(() =>
  import("./User/Pages/CorporateEventProfilePage")
);
const CorporateYourTicketPage = lazy(() =>
  import("./User/Pages/CorporateYourTicketPage")
);
const CorporateSingleYourTicketPage = lazy(() =>
  import("./User/Pages/CorporateSingleYourTicketPage")
);
const CorporateLoginPage = lazy(() =>
  import("./User/Pages/CorporateLoginPage")
);
const CorporateCompanyDashboard = lazy(() =>
  import("./User/Components/CorporateCompanyDashBoard")
);
const CorporateEventDetail = lazy(() =>
  import("./User/Components/CorporateEventDetail")
);

function App() {
  const navigate = useNavigate();
  const inactivityTimer = useRef(null);
  const user = JSON.parse(localStorage.getItem("userData"));
  const Copuser = JSON.parse(localStorage.getItem("CorporateEmp"));
  const [fcmToken, setFcmToken] = useState(null);

  const isAuthenticated = () => {
    return !!localStorage.getItem("userData");
  };

  const PrivateRoute = ({ element: Component }) => {
    return isAuthenticated() ? (
      Component
    ) : (
      <Navigate to="/login?source=yourtickets" />
    );
  };

  const logoutUser = () => {
    signOut(auth)
      .then(() => {
        localStorage.setItem("hasVisited", "false");
        navigate("/login");
      })
      .catch((error) => {
        console.error("Sign out error", error);
      });
  };

  const resetTimer = () => {
    if (inactivityTimer.current) {
      clearTimeout(inactivityTimer.current);
    }
    inactivityTimer.current = setTimeout(logoutUser, 3600000);
  };

  useEffect(() => {
    const events = ["click", "mousemove", "keypress", "scroll"];
    events.forEach((event) => {
      window.addEventListener(event, resetTimer);
    });
    resetTimer();
    return () => {
      events.forEach((event) => {
        window.removeEventListener(event, resetTimer);
      });
      if (inactivityTimer.current) {
        clearTimeout(inactivityTimer.current);
      }
    };
  }, []);

  const saveTokenToBackend = async (token) => {
    try {
      const response = await axios.put(`${API_URL}/admin/save-fcm-token`, {
        token,
      });
      if (response.status === 200) {
        console.log("Token saved successfully!");
      }
    } catch (error) {
      console.error("Error saving token:", error);
    }
  };

  useEffect(() => {
    const fetchFCMToken = async () => {
      try {
        const token = await requestFCMToken();
        setFcmToken(token);
        await saveTokenToBackend(token);
      } catch (error) {
        console.error("Error getting FCM token:", error);
      }
    };
    fetchFCMToken();
  }, []);

  onMessageListener()
    .then((payload) => {
      console.log("Received Message", payload);
    })
    .catch((error) => {
      console.error("message error:", error);
    });

  return (
    <Suspense
      fallback={
        <div
          className="loaderShowing d-flex justify-content-center align-items-center"
          style={{ height: "100vh" }}
        >
          <PuffLoader loading={true} />
        </div>
      }
    >
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup" element={<SignUpPage />} />
        <Route path="/aboutus" element={<AboutUsPage />} />
        <Route path="/contact" element={<CostumerSupportPage />} />
        <Route path="/category/:category" element={<CategoryPage />} />
        <Route path="/productpage/:id" element={<ProductPage />} />
        <Route path="/:event_city/:event_name/:id" element={<ProductPage />} />
        <Route path="/:event_name/select-city" element={<SelectCity />} />
        <Route path="/ticketpage/:event_name/:id" element={<TicketPage />} />
        <Route path="/dateselect/:event_name/:id" element={<DateSelect />} />
        <Route
          path="/eventbooking/:event_name/:event_id"
          element={<BookingPage />}
        />
        <Route path="/policy" element={<PrivacyPolicy />} />
        <Route path="/terms-condition" element={<TermsCondition />} />
        <Route path="/userprofile" element={<UserProfile />} />
        <Route
          path="/yourtickets/:booking_id"
          element={<SingleBookedTicket />}
        />
        <Route
          path="/yourtickets"
          element={<PrivateRoute element={<YourBookedTicket />} />}
        />
        <Route path="/reward-points" element={<RewardsPage />} />
        <Route path="/listyourevent" element={<ListyourEventPage />} />
        <Route
          path="/ticketbooked/:event_name"
          element={<SucessBookingTicketPage />}
        />
        <Route path="/hosted-events" element={<HostedEventPage />} />
        <Route path="/search" element={<SearchPage />} />
        <Route
          path="/corporateevent/ticketpage/:event_name/:id"
          element={<CorporateTicketSelectPage />}
        />
        <Route
          path="/corporateevent/dateselect/:event_name/:id"
          element={<CorpSelectDate />}
        />
        <Route
          path="/corporateevent/booking-summary/:event_name/:event_id"
          element={<CorporateEventBookingPage />}
        />
        <Route
          path="/corporateevent/ticketbooked/:event_name"
          element={<CorporateSucessBookingTicketPage />}
        />
        <Route
          path="/corporateevent/:event_name/:id"
          element={<CorporateEventProfilePage />}
        />
        <Route
          path="/corporateevent/yourtickets"
          element={<CorporateYourTicketPage />}
        />
        <Route
          path="/corporateevent/yourticket/:booking_id"
          element={<CorporateSingleYourTicketPage />}
        />
        <Route path="/corporate/login" element={<CorporateLoginPage />} />
        <Route
          path="/corporate/:company_name/dashbord"
          element={<CorporateCompanyDashboard />}
        />
        <Route
          path="/corporate/:eventName/event-detail/:event_id"
          element={<CorporateEventDetail />}
        />
      </Routes>
    </Suspense>
  );
}

export default App;
