// models/User.js

const mongoose = require('mongoose');

const CorporateUserModelSchema = new mongoose.Schema({
    First_name : String,
    Last_name : String,
    Email : {type:String, unique:true},
    Emp_id : {type:String, unique:true},
    Phone_number : {type:String, unique:true},
    Gender : String,
    DateOfBirth : String,
  createdAt: {
    type: Number,
    default: Date.now,
  }
});

const CorporateUserModel = mongoose.model('CorporateUserModel', CorporateUserModelSchema);

module.exports = CorporateUserModel;

