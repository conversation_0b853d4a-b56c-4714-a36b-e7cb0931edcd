import axios from "axios";
import { useState } from "react";
import { AiOutlineEnvironment, AiOutlineUser } from "react-icons/ai";
import { BiFile } from "react-icons/bi";
import { <PERSON><PERSON><PERSON>ist, BsTicketDetailed } from "react-icons/bs";
import { CiLocationOn } from "react-icons/ci";
import { IoPersonCircleSharp, IoSearch } from "react-icons/io5";
import { SlArrowDown } from "react-icons/sl";
import { TbArrowLeftFromArc, TbArrowLeftToArc, TbUser } from "react-icons/tb";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import femaleProfilePic from "../../Assets/femaleprofile.png";
import Logo from "../../Assets/logo_224x61-02.png";
import maleProfilePic from "../../Assets/maleprofile.png";
import { API_URL } from "../../config";
import { IoClose } from "react-icons/io5";
import "./NavBar.css";

function NavBar({
  onNext,
  onData,
  isLoggedIn,
  userLocation,
  disableMargin = false,
}) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState([]);
  const [error, setError] = useState("");
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const userLoggedIn = JSON.parse(localStorage.getItem("userData"));
  const locationModal = () => {
    onNext(true);
  };
  const highlightText = (text, highlight) => {
    const parts = text.split(new RegExp(`(${highlight})`, "gi"));
    return (
      <span>
        {parts.map((part, index) =>
          part.toLowerCase() === highlight.toLowerCase() ? (
            <span key={index} className="highlight">
              {part}
            </span>
          ) : (
            part
          )
        )}
      </span>
    );
  };
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    if (value) {
      handleSearch(value);
    } else {
      setResults([]);
    }
  };
  const handleSearch = async (searchQuery) => {
    try {
      const response = await axios.get(`${API_URL}/api/search`, {
        params: {
          query: searchQuery,
        },
      });
      const res = response.data;
      setResults(res);
      if (typeof onData === "function") {
        onData(res);
      }
      console.log("RESULT : ", results);
    } catch (error) {
      setError("Error fetching data");
    }
  };
  const handleListUrEvent = (e) => {
    e.preventDefault();
    navigate("/listyourevent");
  };
  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      handleSearch(query);
    }
  };
  const handleLogout = () => {
    localStorage.clear();
    navigate("/");
  };
  // Toggle mobile menu state
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Close mobile menu when a link inside is clicked
  const handleNavLinkClick = () => {
    setIsMobileMenuOpen(false);
  };
  return (
    <>
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#04092c",
          padding: 10,
          position: "fixed",
          zIndex: 1000,
        }}
        id="largeScreenDiv"
      >
        <Link to="/">
          <img
            style={{ height: 48, marginLeft: "20px" }}
            src={Logo}
            alt="logo"
          />
        </Link>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            border: "1px solid #04092c",
            borderRadius: "100px",
            padding: "4px 10px", // Adjusted padding
            backgroundColor: "#2C9CF020",
            width: "100%",
            maxWidth: "30%",
          }}
          onClick={() => navigate("/search")}
        >
          <div
            style={{
              backgroundColor: "transparent",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              cursor: "pointer",
            }}
          >
            <IoSearch
              className="SearchInputIcon fs-4 "
              style={{ color: "#999999", backgroundColor: "transparent" }}
            />
          </div>
          <input
            style={{
              border: "none",
              outline: "none",
              width: "100%",
              fontSize: "small",
              paddingLeft: "10px", // Padding after icon
              backgroundColor: "transparent",
            }}
            type="text"
            placeholder="Search by Event, Artist, Venue..."
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
          />
        </div>

        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: "1rem",
            paddingRight: 10,
          }}
        >
          <button to="#" className="btnlink" onClick={handleListUrEvent}>
            {"List Your Event"}
          </button>
          <Link
            className="loactionlink"
            onClick={locationModal}
            style={{ color: "white" }}
          >
            <CiLocationOn className="locationIcon" />
            {userLocation ? (
              <span className="mx-1"> {userLocation}</span>
            ) : (
              <span className="mx-1">{"Your City"}</span>
            )}

            <SlArrowDown className="ms-2" size={7} />
          </Link>
          {userLoggedIn ? (
            <Link
              to="/userprofile"
              className="userlink"
              style={{ display: "inline-flex" }}
            >
              <TbUser className="fw-semibold fs-5 text-primary" />
              <span className="ps-1" style={{ display: "inline-block" }}>
                {" "}
                Hi, {userLoggedIn ? userLoggedIn?.firstName : "Guest"}{" "}
              </span>
            </Link>
          ) : (
            <Link to="/login?source=login" className="userlink">
              <TbUser className="fw-bold fs-5 text-primary" />
              <span className="ps-1"> Login </span>
            </Link>
          )}
        </div>
      </div>

      {/* Div for Mobile Device */}
      <div style={{ width: "100%" }} id="smallScreenDiv">
        <div className="navbar">
          <div className="dropdown togglebtn">
            <BsList
              className="dropdown-toggle fs-1 ms-2 fw-bold"
              onClick={toggleMobileMenu}
            />

            {isMobileMenuOpen && (
              <ul
                style={{
                  top: 0, // Positions it below the toggle button
                  left: 0, // Aligns it to the left
                  width: "70%", // Full viewport width
                  height: "100vh", // Full viewport height minus navbar height (adjust as needed)
                  backgroundColor: "#fff", // White background
                  zIndex: 1000, // Ensure it's above other content
                  padding: "0.5rem", // Add some padding
                  boxShadow: "0px 8px 16px 0px rgba(0,0,0,0.2)", // Subtle shadow
                  border: "none", // Remove default border
                  borderRadius: "0", // No border radius
                  overflowY: "hidden", // Enable scrolling for long content
                  listStyle: "none", // Remove default list style
                  margin: 0, // Remove default margin
                  position: "fixed", // Ensure it's fixed to the viewport
                  overflowX: "hidden",
                  maxWidth: "278px",
                }}
              >
                <li
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                  }}
                >
                  <IoClose
                    className="text-dark"
                    size={24}
                    onClick={toggleMobileMenu}
                  />
                </li>
                <li>
                  <div
                    style={{
                      paddingBottom: "15px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {userLoggedIn ? (
                      userLoggedIn.gender === "female" ? (
                        <img
                          className="userProfilePic"
                          src={femaleProfilePic}
                          alt="profilePic"
                          style={{
                            width: 80,
                            height: 80,
                            borderRadius: "50%",
                          }}
                        ></img>
                      ) : (
                        <img
                          className="userProfilePic"
                          src={maleProfilePic}
                          alt="profilePic"
                          style={{
                            width: 80,
                            height: 80,
                            borderRadius: "50%",
                          }}
                        />
                      )
                    ) : (
                      <IoPersonCircleSharp
                        style={{ color: "#333" }}
                        size={100}
                      />
                    )}
                    <h5 className="fw-bold text-dark" style={{ marginTop: 10 }}>
                      Hi, {userLoggedIn ? userLoggedIn?.firstName : "Guest"}
                    </h5>
                  </div>
                </li>
                <li>
                  <ul
                    style={{
                      listStyle: "none",
                      paddingLeft: 10,
                      marginTop: 10,
                    }}
                  >
                    <li style={{ marginBottom: "15px" }}>
                      <Link
                        className="link"
                        onClick={locationModal}
                        style={{
                          textDecoration: "none",
                          color: "#333",
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <AiOutlineEnvironment
                          className="text-dark"
                          style={{ marginRight: 5 }}
                          size={20}
                        />
                        {userLocation ? (
                          <span className="ps-1 text-dark fw-semibold">
                            {userLocation}
                          </span>
                        ) : (
                          <span className="ps-1 text-dark fw-semibold">
                            Your City
                          </span>
                        )}
                      </Link>
                    </li>
                    {userLoggedIn ? (
                      <>
                        <li
                          className="nav-content"
                          style={{ marginBottom: "15px" }}
                        >
                          <Link
                            to="/userprofile"
                            className="link-dark link-underline-opacity-0 fw-semibold"
                            style={{
                              textDecoration: "none",
                              color: "#333",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <AiOutlineUser
                              className="text-dark"
                              style={{ marginRight: 5 }}
                              size={20}
                            />
                            <span className="ps-1 text-dark fw-semibold">
                              Profile
                            </span>
                          </Link>
                        </li>
                        <li
                          className="nav-content"
                          style={{ marginBottom: "15px" }}
                        >
                          <Link
                            to="/yourtickets"
                            className="link-dark link-underline-opacity-0 fw-semibold"
                            style={{
                              textDecoration: "none",
                              color: "#333",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <BsTicketDetailed
                              className="text-dark"
                              style={{ marginRight: 5 }}
                              size={20}
                            />
                            <span className="ps-1 text-dark fw-semibold">
                              Your Ticket
                            </span>
                          </Link>
                        </li>
                        <li
                          className="nav-content"
                          style={{ marginBottom: "15px" }}
                        >
                          <Link
                            to="#"
                            className="link-dark link-underline-opacity-0 fw-semibold"
                            style={{
                              textDecoration: "none",
                              color: "#333",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            <BiFile
                              className="text-dark"
                              style={{ marginRight: 5 }}
                              size={20}
                            />
                            <span className="ps-1 text-dark fw-semibold">
                              Reward Points
                            </span>
                          </Link>
                        </li>
                      </>
                    ) : (
                      ""
                    )}
                    <li className="nav-content">
                      {userLoggedIn ? (
                        <Link
                          to="/"
                          className="link"
                          onClick={handleLogout}
                          style={{
                            textDecoration: "none",
                            color: "#333",
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <TbArrowLeftFromArc
                            className="text-dark"
                            style={{ marginRight: 5 }}
                            size={20}
                          />
                          <span className="ps-1 text-dark fw-semibold">
                            Logout
                          </span>
                        </Link>
                      ) : (
                        <Link
                          to="/login"
                          className="link"
                          style={{
                            textDecoration: "none",
                            color: "#333",
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <TbArrowLeftToArc
                            className="text-dark"
                            style={{ marginRight: 5 }}
                            size={20}
                          />
                          <span className="ps-1 text-dark fw-semibold">
                            Login
                          </span>
                        </Link>
                      )}
                    </li>
                  </ul>
                </li>
              </ul>
            )}
          </div>
          <div className="logo-Image">
            <Link to="/">
              <img src={Logo} className="logomobileView" />
            </Link>
          </div>
          <div className="searchbtn dropdown">
            <IoSearch
              className="searchIcon dropdown-toggle text-white"
              size={30}
              onClick={() => navigate("/search")}
            />
          </div>
        </div>
      </div>
      {!disableMargin && <div className="m-navbar"></div>}
    </>
  );
}

export default NavBar;
