const Razorpay = require('razorpay')
const mongoose = require('mongoose'); 
const { key_id, key_secret } = require('../config/razorpaySecret')
const BookingModel = require('../models/BookingModel');
const User = require('../models/User');


const NewPayment = async (req, res) => {
  const razorpay = new Razorpay({
    key_id: key_id,
    key_secret: key_secret,
  })
  const amountInPaise = parseInt(req.body.amount, 10);
  const Options = {
    amount: amountInPaise,
    currency: req.body.currency,
    receipt: "ticket",
    payment_capture: 1,
  }


  try {
    const response = await razorpay.orders.create(Options)
    const payment = new BookingModel({
      user: req.body.user_id,
      // user: mongoose.Types.ObjectId(req.body.user_id),
      order_id: response.id,
      tickets: Object.keys(req.body.ticketDetails).map(id => new mongoose.Types.ObjectId(id)), // Convert IDs to ObjectId
      eventDetails: req.body.eventDetails,
      amount: response.amount,
      receipt: "ticket",
      bookedTicketQuantities: req.body.quantity,
      available_entry: req.body.quantity,
      eventreserveDate: req.body.eventreserveDate,
      reserveeventcity:req.body.reserveCity
  });

    await payment.save();
    res.json({
      response: response,
      order_id: response.id,
      currency: response.currency,
      amount: response.amount,
    })
  }
  catch (err) {
    res.status(500).json({message: "Internal Server Error postpayemnty be", error:err.message})

  }
}

const GetPaymentDetails = async (req, res) => {
  const { transactionId } = req.params;
  const razorpay = new Razorpay({
    key_id: key_id,
    key_secret: key_secret,
  })
  try {
    const payment = await razorpay.payments.fetch(transactionId);
    if (!payment) {
      return res.status(404).send("Payment Not Found");
    }
    res.status(200).json({
      status: payment.status,
      method: payment.method,
      amount: payment.amount,
      payment: payment
    })
  }
  catch (err) {
    res.status(500).json({message: "Internal Server Error getpayment be", error:err})
  }
}

// const SavePaymentDetails =  async (req, res) => {
//   try {
//     const { user_id, paymentId, amount, ticketDetails, eventDetails,paid } = req.body;

//     const payment = new BookingModel({
//       user: user_id,
//       payment_id:  paymentId,
//       tickets: ticketDetails ,
//       eventDetails: eventDetails ,
//       isPaid: paid ,
//       paid_Amount: amount
//     });

//     await payment.save();

//     res.status(201).json({ message: 'Payment details saved successfully!' });
//   } catch (error) {
//     console.error('Error saving payment details:', error);
//     res.status(500).json({ error: 'Failed to save payment details', error });
//   }
// };

const CheckPaymentStatus = async (req, res) => {
  const { order_id } = req.params;
  
  try {
    const booking = await BookingModel.findOne({order_id: order_id});
   
    if (!booking) {
      return res.status(404).send("Payment Not Found");
    }else{
      if(booking.isPaid){
        res.status(200).json({message: "Payment is successful", status: booking.isPaid})
      }else{
        res.status(400).json({message: "Payment is not successful", status: booking.isPaid})
      }
    }
  }
  catch (err) {
    res.status(500).send("Internal Server Error")
  }
}

const FetchBookedEvent = async (req,res)=>{
  const {user_id} = req.params;
  try{
    const bookedEvents = await BookingModel.find({ user: user_id })
      .populate("tickets")
      .populate("eventDetails")
      .sort({ createdAt: -1 });
    if(bookedEvents && bookedEvents.length>0){
      res.status(200).json({message: "Booked Events", data: bookedEvents})
    }else{
      res.status(404).json({message: "No Ticket Booked"})
    }
  }
  catch(err){
    console.error("Error fetching booked events:", err); // Log error details
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
}

const FetchBookedTicket = async (req, res) => {
  try {
      const { booking_id, ticket_id } = req.params;

      // Find the booking by booking_id and populate the referenced event and tickets
      const booking = await BookingModel.findOne({order_id:booking_id}).populate('eventDetails').populate('tickets');

      if (!booking) {
          return res.status(404).json({ message: 'Booking not found' });
      }
      const userName = await User.findOne({_id:booking.user});
      // Check if the ticket_id exists in the tickets array
      const ticket = booking.tickets.find(t => t._id.toString() === ticket_id);
      const event = booking.eventDetails

      if (!ticket) {
          return res.status(404).json({ message: 'Ticket not found in this booking' });
      }

      // Respond with the booking and ticket details
      res.status(200).json({
        booking: booking,
        ticket: ticket,
        event: event,
        userName:userName.firstName
      });
  } catch (error) {
      res.status(500).json({ message: 'Server error', error });
  }
};

const FetchUserDataByEvent = async (req, res) => {
  const { event_id } = req.params;
  try {
    const bookings = await BookingModel.find({ eventDetails: event_id })
      .populate("tickets")
      .populate("eventDetails");

    if (bookings && bookings.length > 0) {
      const userErrors = [];

      // Loop through each booking and attempt to find user data
      const usersData = await Promise.all(
        bookings.map(async (booking) => {
          try {
            // Find user data by user ID or fetch related user data if required
            const user = await User.findById(booking.user); // Assuming `UserModel` is the user schema
            if (user) {
              return { booking, user };
            } else {
              throw new Error(`User with ID ${booking.user} not found`);
            }
          } catch (err) {
            userErrors.push({ bookingId: booking._id, error: err.message });
            return null; // Continue processing other bookings
          }
        })
      );

      const filteredUsersData = usersData.filter((data) => data !== null); // Exclude null entries

      // Return response with bookings, user data, and any errors encountered
      res.status(200).json({
        message: "Booked Events",
        users: filteredUsersData,
        userErrors: userErrors.length ? userErrors : undefined,
      });
    } else {
      res.status(404).json({ message: "No Ticket Booked" });
    }
  } catch (err) {
    console.error("Error fetching booked events:", err);
    res.status(500).json({ message: "Unable to get tickets", error: err.message || err });
  }
};




module.exports = { NewPayment, GetPaymentDetails, CheckPaymentStatus, FetchBookedEvent, FetchBookedTicket, FetchUserDataByEvent};