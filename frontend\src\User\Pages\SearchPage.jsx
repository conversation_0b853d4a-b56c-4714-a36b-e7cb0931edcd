import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { IoMdClose } from "react-icons/io";
import { IoSearch } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import CategoryImages from "../../Assets/CategoryImages.js";
import { API_URL } from "../../config.js";
import BrowseByCategorySearch from "../Components/BrowseByCategorySearch.jsx";
import "./SearchPage.css";

const SearchPage = ({ onData }) => {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState([]);
  const [error, setError] = useState("");
  const [searchEntered, setSearchEntered] = useState(false);
  const [categories, setCategories] = useState([]);
  const navigate = useNavigate();
  const [searchHistory, setSearchHistory] = useState([]);
  const debounceTimeoutRef = useRef(null);
  const debouncedSearch = (value) => {
    // Clear the previous timeout if it exists
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set a new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      handleSearch(value);
    }, 500); // Debounce delay: 500 milliseconds (adjust as needed)
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    if (value) {
      debouncedSearch(value); // Call the debounced function
    } else {
      setResults([]);
      // Clear any pending debounce if the input is cleared
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    }
  };

  // Optional: Cleanup the timeout on component unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      setSearchEntered(true);
      handleSearch(query);
    }
  };

  const handleSearch = async (searchQuery) => {
    if (!searchQuery) return; // Avoid searching if query is empty
    try {
      const response = await axios.get(`${API_URL}/api/search`, {
        params: {
          query: searchQuery,
        },
      });
      const res = response.data;
      setResults(res);
      if (typeof onData === "function") {
        onData(res);
      }
      console.log("RESULT : ", results);
    } catch (error) {
      setError("Error fetching data");
    }
  };

  const highlightText = (text, highlight) => {
    const parts = text.split(new RegExp(`(${highlight})`, "gi"));
    return (
      <span>
        {parts.map((part, index) =>
          part.toLowerCase() === highlight.toLowerCase() ? (
            <mark key={index} className="highlight">
              {part}
            </mark>
          ) : (
            part
          )
        )}
      </span>
    );
  };

  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);

    // Get the day of the week
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dayOfWeek = dayNames[dateObj.getDay()];

    // Get the day, month, and year
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);

    return `${dayOfWeek}, ${day} ${month}`;
  };

  const handleClose = () => {
    navigate(-1); // Navigate back to the previous page
  };

  const handleEventCategory = (cat) => {
    navigate(`/category/${cat}`);
  };

  useEffect(() => {
    loadSearchHistory();
    (async () => {
      // const listofCategory = await axios.get(`${API_URL}/api/category`);
      setCategories(CategoryImages);
    })();
  }, []);

  const loadSearchHistory = () => {
    const history = JSON.parse(localStorage.getItem("searchHistory")) || [];
    setSearchHistory(history);
  };

  const saveSearchHistory = (newHistory) => {
    localStorage.setItem("searchHistory", JSON.stringify(newHistory));
    setSearchHistory(newHistory);
  };

  // Clear search history
  const handleClearHistory = () => {
    localStorage.removeItem("searchHistory");
    setSearchHistory([]);
  };

  // const StoreSearchHistory = (SearchItem) => {
  //     console.log("Search ITEM : ", SearchItem)
  //     let updatedHistory = [...searchHistory];
  //     if (!updatedHistory.includes(SearchItem) && SearchItem.length > 3) {
  //         updatedHistory.unshift(SearchItem); // Add new search to the start
  //         if (updatedHistory.length > 5) {
  //             updatedHistory = updatedHistory.slice(0, 5); // Limit history to 5 searches
  //         }
  //         saveSearchHistory(updatedHistory);
  //     }
  // }

  const StoreSearchHistory = (SearchItem, eventLink) => {
    console.log("Search ITEM : ", SearchItem);

    // Create an object with the search item and its corresponding link
    const searchHistoryItem = { event: SearchItem, link: eventLink };

    // Check if the item already exists in the history, using both event and link as unique identifiers
    let updatedHistory = [...searchHistory];
    const isItemExist = updatedHistory.some(
      (item) => item.event === SearchItem && item.link === eventLink
    );

    // Only add the new search history if it's not already present and if the search item is more than 3 characters
    if (!isItemExist && SearchItem.length > 3) {
      updatedHistory.unshift(searchHistoryItem); // Add the new search to the beginning

      // Limit the history to 5 entries
      if (updatedHistory.length > 5) {
        updatedHistory = updatedHistory.slice(0, 5);
      }

      // Save the updated search history
      saveSearchHistory(updatedHistory);
    }
  };

  console.log({ results });

  return (
    <>
      <div className="serarchMainDiv">
        <div className="w-100 shadow-sm">
          <div className="mobilesearchDiv p-3 ">
            <div
              style={{
                display: "flex",
                alignItems: "center",
                border: "1px solid #ccc",
                borderRadius: "100px",
                padding: "4px 10px", // Adjusted padding
                backgroundColor: "white",
                width: "100%",
              }}
              onClick={() => navigate("/search")}
            >
              <div
                style={{
                  backgroundColor: "white",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <IoSearch
                  className="SearchInputIcon fs-4"
                  style={{ color: "#ccc" }}
                />
              </div>
              <input
                style={{
                  border: "none",
                  outline: "none",
                  width: "100%",
                  fontSize: "1rem",
                  paddingLeft: "10px", // Padding after icon
                }}
                type="text"
                placeholder="Search by Event, Artist, Venue..."
                value={query}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                autoFocus
              />
            </div>

            <button
              className="searchTab-close p-0"
              style={{ color: "#999999", backgroundColor: "#fff" }}
              onClick={handleClose}
            >
              <IoMdClose size={24} />
            </button>
          </div>
        </div>

        <hr className="horizontalLine mt-0 mb-0" />

        {searchEntered ? (
          <>
            <div className="search-result-container">
              <span> {results.length} Product Found</span>
              {results && results.length > 0 ? (
                results.map((searchItem, index) => (
                  <>
                    <Link
                      style={{ textDecoration: "none" }}
                      onClick={() =>
                        StoreSearchHistory(
                          searchItem.event_name,
                          `/${
                            searchItem.event_city
                          }/${searchItem.event_name.replace(/\s+/g, "_")}/${
                            searchItem.unique_id
                          }`
                        )
                      }
                      to={`/${
                        searchItem.event_city
                      }/${searchItem.event_name.replace(/\s+/g, "_")}/${
                        searchItem.unique_id
                      }`}
                      key={searchItem._id}
                    >
                      <div className="card" key={searchItem._id}>
                        <div className="card-body d-flex p-0">
                          <img
                            src={`${searchItem?.cover_image}`}
                            width={100}
                            alt="event"
                            style={{
                              borderTopLeftRadius: 5,
                              borderBottomLeftRadius: 5,
                            }}
                          />
                          <div className="d-flex w-100 flex-column justify-content-start align-items-start p-2">
                            <strong className="">
                              {searchItem.event_name}
                            </strong>
                            <p className="m-0">
                              <small style={{ color: "#6A6A6A" }}>
                                {formatDate(searchItem.event_starts_date)}
                                {searchItem.event_venue
                                  ? `| ${searchItem.event_venue}`
                                  : ""}{" "}
                                {searchItem.city ? `| ${searchItem.city}` : ""}
                              </small>
                            </p>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </>
                ))
              ) : (
                <p>No Search Found!</p>
              )}
            </div>
          </>
        ) : (
          <>
            {query ? (
              <>
                {results.length > 0 && (
                  <div className="search-suggestion-container">
                    <div className="search-suggestion-main">
                      {results.map((result, index) => (
                        <>
                          <Link
                            className="search_result_link"
                            to={`/${
                              result.event_city
                            }/${result.event_name.replace(/\s+/g, "_")}/${
                              result.unique_id
                            }`}
                            key={result._id}
                            onClick={() => {
                              setQuery(result.event_name || result.Artist);
                              StoreSearchHistory(
                                result.event_name,
                                `/${
                                  result.event_city
                                }/${result.event_name.replace(/\s+/g, "_")}/${
                                  result.unique_id
                                }`
                              );
                            }}
                          >
                            {result.event_name ? (
                              <strong>
                                {highlightText(result.event_name, query)}
                              </strong>
                            ) : null}
                            {result.event_venue ? (
                              <small>
                                Venue:{" "}
                                {highlightText(result.event_venue, query)}
                              </small>
                            ) : null}
                            {result.event_city ? (
                              <small>
                                City: {highlightText(result.event_city, query)}
                              </small>
                            ) : null}
                            {result.Artist ? (
                              <small>
                                Artist: {highlightText(result.Artist, query)}
                              </small>
                            ) : null}
                          </Link>
                          {index < results.length - 1 && (
                            <hr className="horizontalLine mt-2 mb-2" />
                          )}
                        </>
                      ))}
                    </div>
                  </div>
                )}
              </>
            ) : (
              searchHistory?.length > 0 && (
                <>
                  <div className="search-history-container">
                    <div className=" d-flex  justify-content-between">
                      <div>
                        <span className="search_history">
                          Your search history
                        </span>
                      </div>
                      <div>
                        <span
                          className="history_clear"
                          onClick={handleClearHistory}
                          style={{ cursor: "pointer" }}
                        >
                          Clear
                        </span>
                      </div>
                    </div>
                    <div className="history mt-3">
                      {searchHistory.length > 0 ? (
                        searchHistory.map((item, index) => (
                          <div className="history_List" key={index}>
                            <Link className="search_links" to={item.link}>
                              {item.event}
                            </Link>
                          </div>
                        ))
                      ) : (
                        <div>No Search History</div>
                      )}
                    </div>
                  </div>
                </>
              )
            )}
          </>
        )}
        <BrowseByCategorySearch />
      </div>
    </>
  );
};

export default SearchPage;
