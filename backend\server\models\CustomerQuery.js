const { query } = require('express');
const MongoDB = require('mongoose');

const CustomerQuerySchema = new MongoDB.Schema({
    Query_type:{
        type:String,
        required:true
    },
     queryRaisedBy:{
        type:String,
     },
     queryEmail:{
        type:String,
     },
     message:{
        type:String,
     },
     Contact_No:{
        type:String,
     },
     Status:{
        type:String,
        default:"active"
     },
    createdAt: {
        type: Number,
        default: Date.now,
      },

});

const CustomerQueryModel = MongoDB.model("customerquery", CustomerQuerySchema);
module.exports = CustomerQueryModel;