import { QRCodeSVG } from "qrcode.react";
import "./SingleBookedTicket.css";
import "./TickerCard.css";
import moment from "moment";
import brokenImage from "../../Assets/broken_image.jpg";
import { useState } from "react";

export const TickerCard = ({ ticket }) => {
  const [active, setActive] = useState(false);
  // const [flipped, setFlipped] = useState(false);
  // const handleCardClick = () => {
  //   setFlipped(!flipped);
  // };
  const formatDate = (dateString) => {
    const dateObj = new Date(dateString);
    const day = String(dateObj.getDate()).padStart(2, "0");
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "June",
      "July",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[dateObj.getMonth()];
    const year = String(dateObj.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
  };
  const handleSupportClick = () => {
    setActive(!active);
    // alert("For support, contact: <EMAIL>\nOr call: +91-XXXXXXXXXX");
  };
  return (
    <>
      <div key={ticket._id} id="ticket-card-small-view">
        {ticket.ticket_type === "paid" ? (
          ticket.isPaid ? (
            <div
              className={`singlebookedticket-flip-container shadow-lg`}
              id="ticketCard"
              // onClick={handleCardClick}
            >
              <div className="singlebookedticket-flipper">
                <div className="singlebookedticket-container singlebookedticket-front shadow">
                  <div className="singlebookedticket-header">
                    <div className="singlebookedticket-info">
                      <h2>{ticket.eventDetails?.event_name}</h2>
                      <p>Venue: {ticket?.reserveeventcity}</p>
                      <p>Time: {ticket.eventDetails?.event_starts_Time}</p>
                      <p>
                        Date:{" "}
                        {formatDate(
                          ticket.eventreserveDate
                            ? ticket.eventreserveDate
                            : ticket.eventDetails?.event_starts_date
                        )}
                      </p>
                    </div>
                  </div>

                  <div
                    className={`singlebookedticket-actions ${
                      active ? "singlebookedticket-active" : ""
                    }`}
                    onClick={handleSupportClick}
                  >
                    Tap for support, details & more actions
                  </div>

                  <div className="singlebookedticket-middle">
                    <p>
                      {ticket.bookedTicketQuantities} x {ticket.ticket_Name}
                    </p>
                    <h3>ENTRY PASS</h3>
                    <p>Booked by: {"replace use here"}</p>
                    <QRCodeSVG
                      className="singlebookedticket-qr-image"
                      value={`${ticket._id}_${ticket.order_id}`}
                    />
                  </div>

                  <div className="singlebookedticket-note">
                    Show this QR at entry. No re-entry allowed once scanned.
                  </div>

                  <div
                    className="singlebookedticket-footer"
                    style={{ background: "#2c9cf0", color: "white" }}
                  >
                    <span>Total Amount</span>
                    <strong>₹ {ticket?.paid_Amount / 100}</strong>
                  </div>
                </div>

                <div className="singlebookedticket-container singlebookedticket-back">
                  <div className="singlebookedticket-back-content">
                    <h3>Need Help?</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +91-9876543210</p>
                    <p>
                      Terms: No refund after booking. Entry allowed till 6:30
                      PM.
                    </p>
                  </div>
                  <div className="singlebookedticket-note">
                    Tap again to return
                  </div>
                </div>
              </div>
            </div>
          ) : (
            ""
          )
        ) : (
          <div
            className={`singlebookedticket-flip-container`}
            id="ticketCard"
            // onClick={handleCardClick}
          >
            <div className="singlebookedticket-flipper ">
              <div className="singlebookedticket-container singlebookedticket-front  shadow">
                <div className="singlebookedticket-header">
                  <div className="singlebookedticket-info">
                    <h2>{ticket.eventDetails?.event_name}</h2>
                    <p>Venue: {ticket?.reserveeventcity}</p>
                    <p>Time: {ticket.eventDetails?.event_starts_Time}</p>
                    <p>
                      Date:{" "}
                      {formatDate(
                        ticket.eventreserveDate
                          ? ticket.eventreserveDate
                          : ticket.eventDetails?.event_starts_date
                      )}
                    </p>
                  </div>
                </div>

                <div
                  className={`singlebookedticket-actions ${
                    active ? "singlebookedticket-active" : ""
                  }`}
                  onClick={handleSupportClick}
                >
                  Tap for support, details & more actions
                </div>

                <div className="singlebookedticket-middle">
                  <p>
                    {ticket.bookedTicketQuantities} x {ticket.ticket_Name}
                  </p>
                  <h3>ENTRY PASS</h3>
                  <p>Booked by: {`replace user here`}</p>
                  <QRCodeSVG
                    className="singlebookedticket-qr-image"
                    value={`${ticket._id}_${ticket.order_id}`}
                  />
                </div>

                <div className="singlebookedticket-note">
                  Show this QR at entry. No re-entry allowed once scanned.
                </div>

                <div
                  className="singlebookedticket-footer"
                  style={{ background: "#2c9cf0", color: "white" }}
                >
                  <span>Total Amount</span>
                  <strong>₹ {ticket?.paid_Amount / 100}</strong>
                </div>
              </div>

              <div className="singlebookedticket-container singlebookedticket-back">
                <div className="singlebookedticket-back-content">
                  <h3>Need Help?</h3>
                  <p>Email: <EMAIL></p>
                  <p>Phone: +91-9876543210</p>
                  <p>
                    Terms: No refund after booking. Entry allowed till 6:30 PM.
                  </p>
                </div>
                <div className="singlebookedticket-note">
                  Tap again to return
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div
        className="d-flex flex-row justify-content-start align-items-start w-100"
        id="ticket-card-large-view"
      >
        <div className="d-flex flex-column justify-content-start align-items-stretch w-100 ">
          <div class="ticket-cardWrap w-100 shadow-lg">
            <div class="ticket-card ticket-cardLeft  w-100 ">
              <div className="d-flex flex-row justify-content-start align-items-start w-100 ticket-card-left-content">
                {ticket.eventDetails?.cover_image ? (
                  <img
                    src={`${ticket.eventDetails?.cover_image}`}
                    alt="Event"
                    className="ticket-card-image"
                  />
                ) : (
                  <img
                    className="ticket-card-image"
                    src={brokenImage}
                    alt="Broken"
                  />
                )}
                <div className="d-flex flex-column justify-content-start align-items-start w-100 ticket-card-left-subcontent">
                  <span className="fw-semibold fs-3 text-black">
                    {ticket.eventDetails
                      ? ticket.eventDetails.event_name
                      : "Error while fetching event name"}
                  </span>
                  <div className="d-flex flex-row justify-content-between align-items-center ticket-card-details">
                    <span className="">
                      {ticket.bookedTicketQuantities} x {ticket.ticket_Name}
                    </span>
                  </div>
                  {/* <div className="border-dashed w-100 mt-2"></div> */}
                  <div className="d-flex flex-row justify-content-between align-items-start mt-2 w-100">
                    <div className="d-flex flex-column justify-content-start align-items-start">
                      <span
                        className="fw-normal fs-6"
                        style={{ color: "#808080" }}
                      >
                        Venue
                      </span>
                      <span className=" fw-normal fs-6 text-black">
                        {ticket.reserveeventcity}
                      </span>
                    </div>
                    <div className="d-flex flex-column justify-content-start align-items-start">
                      <span
                        className="fw-normal fs-6"
                        style={{ color: "#808080" }}
                      >
                        Time
                      </span>
                      <span className="fw-normal fs-6 text-black">
                        {ticket.eventDetails
                          ? ticket.eventDetails.event_starts_Time
                          : "--:--"}
                      </span>
                    </div>
                    <div className="d-flex flex-column justify-content-start align-items-start">
                      <span
                        className="fw-normal fs-6"
                        style={{ color: "#808080" }}
                      >
                        Date
                      </span>
                      <span className="fw-normal fs-6 text-black">
                        {moment(
                          ticket.eventreserveDate
                            ? ticket.eventreserveDate
                            : ticket.eventDetails?.event_starts_date
                        ).format("DD-MMM-YY")}
                      </span>
                    </div>
                  </div>

                  <span className="poppins15 text-primary fw-semibold mt-3">
                    {ticket.bookedTicketQuantities} Tickets
                  </span>
                  <div className="border-2 border-dashed w-100 mt-2"></div>
                  <div className="mt-2">
                    {ticket.ticket_type === "paid" && ticket.isPaid ? (
                      <span className="text-black fs-6 fw-semibold">
                        Amount Paid
                      </span>
                    ) : ticket.ticket_type === "paid" && !ticket.isPaid ? (
                      <span className="text-black fs-6 fw-semibold">
                        Amount not paid
                      </span>
                    ) : (
                      <span className="text-black fs-6 fw-semibold">
                        Free ticket
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div class="ticket-card ticket-cardRight">
              <div className="overlayticketScanner">
                <span className="ticket-card-qr-code-text">
                  Scan this QR at entry
                </span>
                <QRCodeSVG
                  style={{ width: "100%", height: "100%", maxWidth: 100 }}
                  className="ticket-card-qr-code"
                  value={`${ticket._id}_${ticket.order_id}`}
                />
                {/* <span className="text-wrap">{`${ticket._id}_${ticket.order_id}`}</span> */}
              </div>
            </div>
          </div>
          <div className="ticket-card-bottom-right shadow-lg"></div>
        </div>
        <div className="col-lg-3 col-md-3 ps-3 ticket-status-details">
          <div className="pb-2">
            <p className="poppins15 text-primary">Confirmation ID</p>
            <p className="poppins15 text-dark">{ticket.order_id}</p>
          </div>
          <div className="pb-2">
            <p className="poppins15 text-primary">Booking Date & Time</p>
            <p className="poppins15 text-dark">
              {moment(ticket.createdAt).format("DD-MMM-YY HH:mm:ss")}
            </p>
          </div>
          <div>
            <p className="poppins15 text-primary">Status</p>
            <p
              className={
                ticket.isPaid ? "ticketStatus-paid" : "ticketStatus-unpaid"
              }
            >
              {ticket.status ? ticket.status : "Pending"}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
