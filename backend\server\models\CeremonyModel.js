const MongoDB = require('mongoose');

const CeremonySchema = new MongoDB.Schema({
    unique_id : {
        type: String,
        require: true,
        unique: true
    },
    event_name: { type: String,required: true},
    category: { type: String,required: true},
    // event_visibility: { type: String },
    event_starts_date: { type: String, required: true},
    event_starts_Time: { type: String},
    event_ends_date: { type: String, required: true},
    event_ends_Time: { type: String},
    isEvent_repeat: { type: Boolean, default: false },
    isAllSession : {type: Boolean, default:false},
    eventForMultiCity : {type: Boolean, default:false},
    multiCityArray : [{
        event_city: String,
        event_venue: String,
        event_date: String,
        show_time: [{ time: String }],
    }],
    event_repeats: { type: String},
    event_city: { type: String},
    event_venue: { type: String},
    venue_latitude: { type: String},
    basic_price : { type: Number, default:0},
    venue_longitude: { type: String},
    event_description: { type: String, required: true},
    termsCondition: { type: String, required: true},
    guidelines: { type: String, required: true},
    cover_image: { type: String}, 
    eventCreatedBy:{ type:  String, required: true },
    ticket : [{ type: MongoDB.Schema.Types.ObjectId, ref:"Ticket"}],
    Addon : [{ type: MongoDB.Schema.Types.ObjectId, ref:"AdditionalData"}],
    isExpired : {
        type: Boolean,
        default: false
    },
    SM_Campaign:{
        type: Boolean,
        default: false
    },
    Daily_Budget:{
        type: Number
    },
    Paid_by:{
        type: String
    },
    createdAt: {
        type: Number,
        default: Date.now,
      },
});

const CeremonyModel = MongoDB.model("CeremonyModel", CeremonySchema);
module.exports = CeremonyModel;