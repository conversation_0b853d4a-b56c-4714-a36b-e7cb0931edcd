import { useEffect, useState } from "react";
import Reward_img from "../../Assets/reward_img.png";
import LocationModal from "../Components/LocationModal";
import NavBar from "../Components/NavBar";
import UserSideBar from "../Components/userSideBar";
import "./RewardsPage.css";
import "./YourTicketPage.css";

const RewardsPage = () => {
  const [userLocation, setUserLocation] = useState(
    localStorage.getItem("userLocation") || "Nagpur"
  );
  const [showLocationModal, setShowLocationModal] = useState(false);

  // Function to handle location change
  const handleLocationChange = (location) => {
    setUserLocation(location);
    localStorage.setItem("userLocation", location);
    setShowLocationModal(false); // Close modal after location change
  };

  // Function to handle closing the location modal
  const handleCloseLocationModal = () => {
    setShowLocationModal(false);
  };

  useEffect(() => {
    // Scroll to top when the component loads or data is done loading
    window.scrollTo({
      top: 0,
      behavior: "smooth", // Adds smooth scrolling
    });
  }, []);

  return (
    <>
      <div
        className="profilepage"
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "start",
          alignItems: "center",
        }}
      >
        {/* Show LocationModal */}
        {showLocationModal && (
          <LocationModal
            onClose={handleCloseLocationModal}
            onLocationChange={handleLocationChange}
          />
        )}
        <NavBar
          userLocation={userLocation}
          onNext={() => setShowLocationModal(true)}
        />
        <div className="w-100 d-flex flex-row justify-content-start align-items-stretch">
          <UserSideBar />
          <div
            className="col-lg-10 col-md-10 col-sm-12 col-12 flex-1 d-flex flex-column h-navbar justify-content-start align-items-center"
            style={{
              flex: 1,
              flexGrow: 1,
              overflowY: "auto",
            }}
          >
            <div className="reawrds-heading w-100">
              <span>Your Reward Points</span>
            </div>
            <div className="lists">
              <div className="p-4 w-100">
                <img src={Reward_img} alt="Reward " className="rewards-image" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RewardsPage;
