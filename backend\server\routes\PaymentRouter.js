const express = require('express');
const Razorpay = require('razorpay')
const { NewPayment, GetPaymentDetails, CheckPaymentStatus, FetchBookedEvent, FetchBookedTicket, FetchUserDataByEvent } = require('../controllers/paymentController');
const BookingModel = require('../models/BookingModel');
const TicketModel = require('../models/TicketModel');
const { key_id, key_secret } = require('../config/razorpaySecret');
const paymentRouter = express.Router();

paymentRouter.post('/payment-order', NewPayment);
paymentRouter.get('/payment/:transactionId', GetPaymentDetails);
paymentRouter.get('/payment/status/:order_id', CheckPaymentStatus);
paymentRouter.get('/ticket/:user_id', FetchBookedEvent);
paymentRouter.get('/events/:event_id', FetchUserDataByEvent);
paymentRouter.get('/ticket/:booking_id/:ticket_id', FetchBookedTicket);

paymentRouter.post("/verify/payment", async (req, res) => {
    try {
        // console.log("Entry To verify api", req);
        if (!req.body?.payload?.payment?.entity) {
            // console.log("Payment entity not found",req.body)
            return res.status(400).json({ error: "Payment entity not  found" })
        }
        const secret = "12345678";
        const crypto = require("crypto");
        const shasum = crypto.createHmac("sha256", secret);
        shasum.update(JSON.stringify(req.body));
        const digest = shasum.digest("hex");
        if (digest === req.headers["x-razorpay-signature"]) {
            const payEntity = req.body.payload.payment.entity;
            const orderId = payEntity["order_id"];
            const paymentId = payEntity["id"];
            const razorpay = new Razorpay({
                key_id: key_id,
                key_secret: key_secret,
            })
            const payment = await razorpay.orders.fetch(orderId);
            console.log("PAYMENT FETCH BY ORDER_ID ++++++++++++++++++++++++++++++++++++++++++++++++++++++++ :", payment);
            if (!payment) {
                // console.log("Payment not found",req.body)
                return res.status(201).send("Payment Not Found");
            }

            if (payment.status == "paid") {
                const booking = await BookingModel.findOne({ order_id: orderId });
                // console.log("Booking Data : ", booking)
                if (booking.receipt === "ticket") {
                    if (booking.isPaid === true) {
                        // console.log("booking true",req.body)
                        return res
                            .status(201)
                            .json({ message: "Ticket amount already paid." });
                    }
                    try {
                        const updatePayment = await BookingModel.findOneAndUpdate(
                            { order_id: orderId }, // Find by order_id
                            { 
                                isPaid: true,
                                paid_Amount: payment.amount_paid, 
                                payment_id: paymentId,
                                status: payment.status,
                                available_entry: booking.bookedTicketQuantities
                            },
                            { new: true } // Option to return the updated document
                        );
                        if (booking && booking.tickets && booking.tickets.length > 0) {
                            // Loop through each ticket in the booking to update availability
                            for (let ticketId of booking.tickets) {
                                const ticket = await TicketModel.findById(ticketId);
                                if (ticket) {
                                    // Update the ticket's availability
                                    // console.log("booking.bookedTicketQuantities : ", booking.bookedTicketQuantities )
                                    const updatedTicket = await TicketModel.findOneAndUpdate(
                                        { _id: ticketId },
                                        {
                                            $inc: { ticket_avability: -booking.bookedTicketQuantities } // Decrement availability
                                        },
                                        { new: true }
                                    );
                                    console.log("EVENT Reserve Date : ", booking.eventreserveDate)
                                    if (ticket.isTicketForPerDay) {
                                        const updatedavailable_daily_ticket_quantities = await TicketModel.findOneAndUpdate(
                                            { _id: ticketId },
                                            {
                                                $inc: { [`available_daily_ticket_quantities.${booking.eventreserveDate}`]: -booking.bookedTicketQuantities } // Decrement availability
                                            },
                                            { new: true }
                                        );
                                    }
                                    
                                    // console.log("Updated ticket:", updatedTicket);
                                }
                            }
                        } else {
                            console.log("No booking or tickets found.");
                        }
                        console.log("verify success________________________________________",req.body,"________________________________________________________",updatePayment)
                        res.status(200).json({ message: "Operation successful!", updatePayment });
                    } catch (e) {
                        // console.log("Error updating payment:", e);
                        return res.status(201).json({ message: "Transaction failed." });

                    }
                }
            } else {
                // console.log("Payment status not paid",req.body)
                return res.status(400).json({ error: "Payment status not paid." })
            }
        } else {
            // console.log("Invalid request",req.body)
            res.status(401).send({ message: "Invalid request." });
        }
    }
    catch (err) {
        console.error(err);
        res.status(200).send({ message: "Failed to verify the payment!" });
    }
});



// paymentRouter.post("/verify", async (req, res) => {
//     console.log("Verify API Call")
//     const secret = "12345678";
//     const crypto = require("crypto");
//     const shasum = crypto.createHmac("sha256", secret);
//     shasum.update(JSON.stringify(req.body));
//     const digest = shasum.digest("hex");
//     if (digest === req.headers["x-razorpay-signature"]) {
//         const payEntity = req.body.payload.payment.entity;
//         const orderId = payEntity["order_id"];
//         const paymentId = payEntity["id"];
//         const booking = await BookingModel.findOne({ order_id: orderId });

//         if (booking.receipt === "ticket") {
//             if (booking.isPaid === true) {
//                 return res
//                     .status(422)
//                     .json({ message: "Ticket is already paid." });
//             }

//             try {
//                 const updatePayment = await BookingModel.findOneAndUpdate(
//                     { order_id: orderId },
//                     { isPaid: true },
//                     { payment_id: paymentId },
//                     { ticket_avability: (booking.ticket_avability - booking.bookedTicketQuantities) },
//                     { available_entry: booking.bookedTicketQuantities },
//                     { new: true } // This option returns the updated document
//                 );
//                 console.log("Bookings : ",booking)

//                 if (booking && booking.tickets && booking.tickets.length > 0) {
//                     // Loop through each ticket in the booking to update availability
//                     for (let ticketId of booking.tickets) {
//                         console.log("TICKED ID : ",ticketId)
//                         const ticket = await TicketModel.findById(ticketId);
//                         if (ticket) {
//                             // Update the ticket's availability
//                             const updatedTicket = await TicketModel.findOneAndUpdate(
//                                 { _id: ticketId },
//                                 {
//                                     $inc: { ticket_avability: -booking.bookedTicketQuantities } // Decrement availability
//                                 },
//                                 { new: true }
//                             );
//                             console.log("Updated ticket:", updatedTicket);
//                         }
//                     }
//                 } else {
//                     console.log("No booking or tickets found.");
//                 }

//                 res.status(200).json({ message: "Operation successful!" });
//             } catch (e) {
//                 return res.status(500).json({ message: "Transaction failed." });
//             }

//         }
//     } else {
//         res.status(401).send({ message: "Invalid request." });
//     }
// });

module.exports = paymentRouter;