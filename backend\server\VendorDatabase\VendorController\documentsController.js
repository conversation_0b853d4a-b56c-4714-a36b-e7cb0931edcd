const fs = require('fs');

// const SetDocuments = async (req, res) => {
//   try {
//     res.send({
//       message: 'File uploaded successfully',
//       file: req.file,
//     });
//   } catch (err) {
//     res.status(500).send(err);
//   }
// }

const path = require('path');
const { Storage } = require('@google-cloud/storage');

const storage = new Storage({
  keyFilename: path.join(__dirname, '../../googleCloudBucketConfig.json'),
});
const bucket = storage.bucket('my-front-seat-bucket2024'); // replace with your bucket name

const SetDocuments = async (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).send({ message: 'No file uploaded' });
    }

    const gcsFileName = `ceremony-images/${Date.now()}-${file.originalname}`;
    const fileUpload = bucket.file(gcsFileName);

    // Upload the file to Google Cloud Storage
    await fileUpload.save(file.buffer, {
      metadata: {
        contentType: file.mimetype,
      },
    });

    const fileUrl = `https://storage.googleapis.com/${bucket.name}/${gcsFileName}`;

    res.send({
      message: 'File uploaded successfully to Google Cloud Storage',
      file: fileUrl,
    });
  } catch (err) {
    console.error('Error uploading file:', err);
    res.status(500).send({ message: 'Error uploading file', error: err.message });
  }
};



module.exports = { SetDocuments };