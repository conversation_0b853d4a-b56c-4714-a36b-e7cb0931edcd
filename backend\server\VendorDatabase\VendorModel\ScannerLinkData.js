// models/User.js

const mongoose = require('mongoose');

const ScannerLinkDataSchema = new mongoose.Schema({
    event_id: {
        type: String,
        required: true,
        unique: true,
    },
    login_id: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true,
        unique: true
    },
    total_no_of_Guest:{
        type:Number,    
    },
    no_of_Guest_attended:{
        type:Number,    
    },
    total_no_of_Customer_attended:{
        type:Number,    
    },
    attended_event_ticket_id:[{
        type: mongoose.Schema.Types.ObjectId,
        ref: "Ticket"}]
});

const ScannerLinkData = mongoose.model('ScannerLinkData', ScannerLinkDataSchema);

module.exports = ScannerLinkData;

