.footerDiv {
  background: #04092C;
  margin-top: 55px;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: start;
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
;
}

.footer-container{
  width: 100%;
  max-width: 1280px;
}

.footer-layout{
  width: 100%;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap:1rem

}
.footerLogo {
  display: flex;
  align-items: center;
}

.footerlogoImg {
  
  height: 60px;
}

.footerCols, .footerColsm {
  display: flex;
  justify-content: center;
}

.footerList ul li {
  list-style: none;
  padding: 0 !important;
}

.footerHeader {
  color: #2C9CF0;
  
  text-transform: uppercase;
  margin-bottom: 10px;
}

.footerContents, .footerContentsm{
  color: #A5A0A0;
  font-size: small;
  
  margin-bottom: 8px;
}
.link{
  color: #A5A0A0;
}

.footer-footer {
  color: #F2F6F9;
  
  font-size: small;
}

.smallscreeview {
  display: none;
}

.footer-social-md-main{
  display: none;
}
.footer-social-lg-main{
  display: block;
}
@media screen and (max-width: 1280px) {

}

@media screen and (max-width: 986px) {
  .footerLogo {
    display: none;
  }
  .footer-layout{
    grid-template-columns: repeat(4, 1fr);
    gap:1rem
  }
  .footer-container{
    flex-direction: column ;
  }
  .footerList {
    list-style: none;
    margin-top: 0%;
  }
  .footerCols {
    display: flex;
    justify-content: start;
  }
  .footerColsm{
    justify-content: center !important;

  }
  .footerContentsm{
    display: inline-flex;
  }
  .facebook{
    margin-left: 20px;
    margin-bottom: 0px;
  }
  .footer-social-lg-main{
    display: none;
  }
  .footer-social-md-main{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
  }

  .footer-social-md-ul{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .footerContentmd{
    color: #A5A0A0;
  font-size: small;
  
  }
}

@media screen and (max-width: 768px) {
  .footer-layout{
    grid-template-columns: repeat(2, 1fr);
    gap:1rem
  }
  .footer-container{
    margin-top: 1rem;
  }

}

