.mobilesearchDiv {
    max-width: 960px;
    width: 100%;
    margin: auto;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
}
.serarchMainDiv{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    
}

.search-history-container{
    max-width: 1280px;
    width: 100%;
    margin: auto;
    padding: 1rem;
}

.search-suggestion-container{
    max-width: 960px;
    width: 100%;
    margin: auto;
    padding: 1rem;
}

.search-suggestion-main{
    width: 100%;
    border: 1px solid #EBEBEB;
    border-radius: 1rem;
    padding:1rem
}

.search-result-container{
    max-width: 960px;
    width: 100%;
    margin: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: stretch;
    gap: 5px;
}

.search-result-main{
    width: 100%;
    border: 1px solid #EBEBEB;
    border-radius: 1rem;
    padding:1rem
}

.searchIcon-mobile {
    width: 15%;
    text-align: center;
    background-color: #EBEBEB;
    border-top-left-radius: 5rem;
    border-end-start-radius: 5rem;
    border: 1px solid #999999;
    border-right: none !important;
}

.searchTab-mobile {
    width: 85%;
    border-top-right-radius: 5rem;
    border-end-end-radius: 5rem;
    border: 1px solid #999999;
    border-left: none !important;
}

.searchTab-close {
    display: flex;
    align-items: center;
    margin: auto;
    margin-left: 1rem;
}

.searchTab-input {
    width: 100%;
    height: 100%;
    border-top-right-radius: 5rem;
    border-end-end-radius: 5rem;
    background-color: #EBEBEB !important;
    padding-left: 5px;
}

.searchTab-input:focus {
    outline: none;
    border: none;
}

.search_history {
    
  margin-top: 2rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.history_clear {
    font-size: 16px;
    
    color: #2C9CF0;
}

.history {
    display: flex;
    flex-wrap: wrap;
    /* Ensures items move to the next line */
    gap: 10px;
    /* Adjusts spacing between items */
    justify-content: flex-start;
    /* Aligns items to the start */
}

.history_List {
    border-radius: 5rem;
    background-color: #EBEBEB;
    padding: 0.5rem 0.7rem;
    width: calc(100% / 5 - 8px);
    /* Ensures exactly 5 items per row */
    font-size: 16px;
    
    white-space: nowrap;
    text-align: center;
}

.categoryBrowser {
    font-size: 18px;
    
    font-weight: 600;
}

.history .searchcategorycard {
    width: calc(100% / 4 - 10px);
    height: 85px !important;
    margin-left: 0px;
    border: none !important;
    margin-bottom: 2rem;
}

.history .categoryblock {
    position: relative;
    top: 2%;
    padding: 0.1rem 0.5rem !important;
    border-radius: 1rem;
    background-color: #FFFFFF;
    color: black;
    
    width: 99%;
    height: 21px;
    font-weight: bold;
    font-size: 12px;
    z-index: 1;
    white-space: nowrap;
    border-bottom: 2px solid white;
    border-left: 2px solid white;
    border-right: 2px solid white;
}
.history .categorycard  .card-img {
    height: 55.35px !important;
    width: 100%;
    border-radius: 1rem;
    position: relative;
    top: 5%;
  }

  .searchcategory-img {
    width: 100%;
    /* height: 100px; */
    border-radius: 1rem;
    z-index: -1;
  }
.search_links{
    text-decoration: none;
    color: black;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width:100%;
    display: block;
    
}
.search_result_link{
    text-decoration: none;
    color: black;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width:100%;
    display: flex;
    justify-content: start;
    align-items: start;
    flex-direction: column;
}
.highlight{
    font-weight: bold;
}
.searchdata-image{
    width: 112px;
    height: 61px;
}


@media screen and (max-width: 780px) {

    
    .searchIcon-mobile {
        width: 15%;
        text-align: center;
        background-color: #EBEBEB;
        border-top-left-radius: 5rem;
        border-end-start-radius: 5rem;
        border: 1px solid #999999;
        border-right: none !important;
    }
    
    .searchTab-mobile {
        width: 85%;
        border-top-right-radius: 5rem;
        border-end-end-radius: 5rem;
        border: 1px solid #999999;
        border-left: none !important;
    }
    
    .searchTab-close {
        margin-left: 0.8rem;
    }
    
    .searchTab-input {
        width: 100%;
        height: 100%;
        border-top-right-radius: 5rem;
        border-end-end-radius: 5rem;
        background-color: #EBEBEB !important;
        padding-left: 5px;
    }
    
    .searchTab-input:focus {
        outline: none;
        border: none;
    }
    
    .search_history {
        font-size: 16px;
        
        font-weight: 600;
    }
    
    .history_clear {
        font-size: 12px;
        
        color: #2C9CF0;
    }
    
    .history {
        display: flex;
        flex-wrap: wrap;
        /* Ensures items move to the next line */
        gap: 10px;
        /* Adjusts spacing between items */
        justify-content: flex-start;
        /* Aligns items to the start */
    }
    
    .history_List {
        border-radius: 5rem;
        background-color: #EBEBEB;
        padding: 0.5rem 0.7rem;
        width: calc(100% / 3 - 8px);
        /* Ensures exactly 5 items per row */
        font-size: 14px;
        
        white-space: nowrap;
        text-align: center;
    }
    
    .categoryBrowser {
        font-size: 16px;
        
        font-weight: 600;
    }
    
    .history .searchcategorycard {
        width: calc(100% / 3 - 10px);
        height: 85px !important;
        margin-left: 0px;
        border: none !important;
        margin-bottom: 0.1rem;
    }
    
    .history .categoryblock {
        position: relative;
        top: 2%;
        padding: 0.1rem 0.5rem !important;
        border-radius: 1rem;
        background-color: #FFFFFF;
        color: black;
        
        width: 99%;
        height: 21px;
        font-weight: bold;
        font-size: 12px;
        z-index: 1;
        white-space: nowrap;
        border-bottom: 2px solid white;
        border-left: 2px solid white;
        border-right: 2px solid white;
    }
    .history .categorycard  .card-img {
        height: 55.35px !important;
        width: 100%;
        border-radius: 1rem;
        position: relative;
        top: 5%;
      }
    
      .searchcategory-img {
        width: 100%;
        /* height: 100px; */
        border-radius: 1rem;
        z-index: -1;
      }
    .search_links{
        text-decoration: none;
        color: black;
        font-size: 14px;
    }
    .highlight{
        font-weight: bold;
    }
    .searchdata-image{
        width: 112px;
        height: 61px;
    }
}