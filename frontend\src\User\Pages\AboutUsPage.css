.about-content-container{
    max-width: 1280px;
    width: 100%;
    margin: auto;
    padding: 1rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.aboutHead {
    color: #FFE600;
    background: #04092C;
    height: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.about-head {
    color: #FFE600;
    background: #04092C;
    height: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    width:100%;
}
.aboutHeading {
    font-size: 76px;
    
    font-weight: bold !important;
}

.about-heading {
    font-size: 92px;
    
    font-weight: bold !important;
}

.aboutSubHeading {
    font-size: 22px;
    
}

.about-sub-heading {
    font-size: 18px;
    
}

.heading-style {
    font-size: 36px;
    
    font-weight: 600;
}

.headingStyle {
    font-size: 30px;
    
    font-weight: 400;
}


.para-style {
    font-size: 22px;
    
    margin-bottom: 77px;
}

.paraStyle {
    font-size: 20px;
    
    margin-bottom: 77px;
}

.about_img {
    width: 200px;
}
.about-offering-container{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: end;
    width: 100%;
}
.about-offering-text{
    font-weight: 600;
    margin-top: 1.5rem;
}

@media screen and (max-width:640px) {
    .about_img {
        width: 60px;
    }

    .about-content-container{
        text-align: start;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: none;
    }

    .aboutHeading {
        font-size: 36px;
        
        font-weight: bold !important;
    }

    .about-heading {
        font-size: 35px;
        
        font-weight: bold !important;
    }

    .aboutSubHeading {
        font-size: 18px;
        
    }

    .about-sub-heading {
        font-size: 14px;
        
    }

    .about-offering-container{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: end;
        width: 100%;
    }

    .headingStyle {
        font-size: 22px;
        
        font-weight: 400;
    }

    .heading-style {
        font-size: 18px;
        
        font-weight: 600;
    }

    .para-style {
        font-size: 14px;
        
        margin-bottom: 44px;
    }
    .paraStyle {
        font-size: 16px;
        
        margin-bottom: 77px;
    }

    .privacypolicy-width {
        width: 100% !important;
        max-width: 1280px;
    }
    .about-offering-text{
        font-weight: 600;
        margin-top: 1rem;
    }
}

.privacypolicy-width {
    width: 50%;
}