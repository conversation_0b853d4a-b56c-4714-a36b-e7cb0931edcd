const BookingModel = require("../models/BookingModel");
const CeremonyModel = require("../models/CeremonyModel");
const TicketModel = require("../models/TicketModel");
const CustomerQueryModel = require("../models/CustomerQuery");
const User = require("../models/User");
const ScannerLinkData = require("../VendorDatabase/VendorModel/ScannerLinkData");
const VendorUser = require("../VendorDatabase/VendorModel/VendorUser");
const VendorPaymentModel = require("../models/VendorPayment");
const CarouselDataModel = require("../models/CarouselDataModel");
const Admin = require("../models/AdminUserDataModel");
const axios = require('axios');
const NotificationService = require("../NotificationService");
const FCMToken = require("../models/FCMToken");
const CategoryModel = require("../models/CategoryModel");
const StateModel = require("../models/StateDataModel");
const DiscountCouponModel = require("../models/DiscountCoupon");
const NotificationModel = require("../models/NotificationModel");

const AdminSignUp = async (req, res) => {
  try {
    const { login_id, password, role } = req.body;

    const existingAdmin = await Admin.findOne({ login_id: login_id });
    if (existingAdmin) {
      return res.status(401).send("User already exists");
    }

    const adminCredentials = new Admin({
      login_id: login_id,
      password: password,
      role: role
    });

    const createdAdmin = await adminCredentials.save();
    if (createdAdmin) {
      return res.status(200).send({
        message: "User created successfully",
        user: {
          _id: createdAdmin._id,
          login_id: createdAdmin.login_id,
          role: createdAdmin.role
        }
      });
    }
  } catch (error) {
    return res.status(500).send({ message: "Something went wrong", error: error.message });
  }
}

const AdminLogin = async (req, res) => {
  try {
    const { login_id, password } = req.body;
    const response = await Admin.findOne({ login_id: login_id });
    if (response) {
      if (response.password === password) {
        res.status(200).send({
          message: "user Login sucessfully", user: {
            _id: response._id,
            user: response.login_id,
            role: response.role
          }
        });
      } else {
        res.status(401).send("Invalid Password");
      }
    } else {
      res.status(401).send("User not registered!");
    }

  } catch (error) {
    res.status(500).send("Something went wrong")
  }
}

const AdminDashBoardMatrices = async (req, res) => {
  try {
    const events = await CeremonyModel.find().populate('ticket');
    const bookings = await BookingModel.find().populate('tickets');
    const data = events.filter(e => e.Addon && e.Addon.length > 0);

    let event_count = 0;
    let ticket_count = 0;
    let ticket_sell = 0;
    let revenue = 0;

    for (let i = 0; i < data.length; i++) {
      event_count += 1;

      // Check if tickets exist for the event
      if (data[i].ticket && data[i].ticket.length > 0) {
        for (let j = 0; j < data[i].ticket.length; j++) {
          ticket_count += data[i].ticket[j].total_Quantity;
          ticket_sell += data[i].ticket[j].total_Quantity - data[i].ticket[j].ticket_avability;
        }
      }

      const soldData = await BookingModel.find({ eventDetails: data[i]._id });
      // console.log("SOLDDATA_DASHBORD : ", soldData)
      if (soldData && soldData.length) {
        for (let k = 0; k < soldData.length; k++) {
          if (soldData[k].isPaid) {
            revenue += (soldData[k].amount) / 100;
          }
        }
      }
    }
    if (data && data.length > 0) {
      res.status(200).json({
        event_hosted: event_count,
        ticket_hosted: ticket_count,
        ticket_sold: ticket_sell,
        ticket_revenue: revenue,
        bookings: bookings
      });
    } else {
      res.status(404).json({ message: "No data found" });
    }
  } catch (err) {
    console.error(err);
    res.status(500).send({ message: "Something went wrong" });
  }

}

const AdminAllEventsList = async (req, res) => {
  try {
    const events = await CeremonyModel.find().populate('ticket');
    const tickets = events.filter(e => e.Addon && e.Addon.length > 0);
    if (tickets.length > 0) {
      res.status(200).send(tickets);
    } else {
      res.status(201).send({ message: "No events are available" });
    }
  } catch (err) {
    console.error(err); // Log the error for debugging
    res.status(500).send({ message: "Something went wrong" });
  }
}

const GetVendorData = async (req, res) => {
  try {
    const vendorData = await VendorUser.findOne({ _id: req.params.vendor_id });
    if (vendorData) {
      const vendor = {
        Organiser_name: vendorData.name,
        email: vendorData.email,

      }
      res.status(200).send(vendor);
    } else {
      res.status(404).send({ message: "Vendor not found" });
    }
  } catch (error) {
    console.error(error);
  }
}

const GetAllTicketBuyers = async (req, res) => {
  try {
    const result = await BookingModel.aggregate([
      {
        $group: {
          _id: "$user", // Group by user ID
          totalBookedTicketQuantities: { $sum: "$bookedTicketQuantities" } // Sum bookedTicketQuantities
        }
      },
      {
        $project: {
          _id: 0, // Exclude the default `_id` field
          user: "$_id", // Rename `_id` to `user`
          bookedTicketQuantities: "$totalBookedTicketQuantities"
        }
      }
    ]);

    const data = result.filter(count => count.bookedTicketQuantities && count.bookedTicketQuantities > 0)

    const TicketBuyer = await Promise.all(
      data.map(async (ticket) => {
        const user = await User.findOne({ _id: ticket.user });
        return { ticket, user }
      })
    )
    res.status(200).send(TicketBuyer);
  } catch (error) {
    console.error("Error in aggregation:", error);
    res.status(201).send(error);
  }
}

const GetAllUsersData = async (req, res) => {
  try {
    let Female_Count = 0;
    let Male_Count = 0;
    let NewRegistration_Count = 0;

    const result = await User.find();
    if (result) {
      // Count male and female users
      result.forEach(user => {
        if (user.gender === "female") {
          Female_Count++;
        } else if (user.gender === "male") {
          Male_Count++;
        }
      });

      // Count users registered today
      const today = new Date().toDateString();
      const NewRegistered_user = result.filter(newUser =>
        new Date(newUser.createdAt).toDateString() === today
      );
      if (NewRegistered_user && NewRegistered_user > 0) {
        NewRegistration_Count = NewRegistered_user.length;
      }
    }

    // Send response
    res.status(200).json({
      Female_Count,
      Male_Count,
      NewRegistration_Count,
    });
  } catch (error) {
    // Handle error
    console.error("Error fetching users:", error);
    res.status(500).json({ message: "Error fetching users data" });
  }
};

const GetAllVendors = async (req, res) => {
  try {
    let Female_Count = 0;
    let Male_Count = 0;
    let NewRegistration_Count = 0;

    const VendorData = await VendorUser.find();
    if (VendorData) {
      // Count male and female VendorUser
      VendorData.forEach(VendorUser => {
        if (VendorUser.gender === "female") {
          Female_Count++;
        } else if (VendorUser.gender === "male") {
          Male_Count++;
        }
      });

      // Count VendorUsers registered today
      const today = new Date().toDateString();
      const NewRegistered_VendorUser = VendorData.filter(newVendorUser =>
        new Date(newVendorUser.createdAt).toDateString() === today
      );
      if (NewRegistered_VendorUser && NewRegistered_VendorUser > 0) {
        NewRegistration_Count = NewRegistered_VendorUser.length;
      }
    }
    let vendorEventById = {}
    for(let vendor of VendorData){
      const vendorEvents = await CeremonyModel.find({ eventCreatedBy: vendor._id });
      vendorEventById[vendor._id] = vendorEvents.length || 0;
    }

    // Send response
    res.status(200).json({
      Female_Count,
      Male_Count,
      NewRegistration_Count,
      VendorData,
      vendorEventById
    });
  } catch (error) {
    // Handle error
    console.error("Error fetching VendorUsers:", error);
    res.status(500).json({ message: "Error fetching VendorUsers data" });
  }
};

const GetVendorsEvent = async (req, res) => {
  let NoOfEvents = 0;
  try {
    const vendorEvents = await CeremonyModel.find({ eventCreatedBy: req.params.vendor_id }).populate('ticket');
    if (vendorEvents && vendorEvents.length > 0) {
      NoOfEvents = vendorEvents.length;
      return res.status(200).json({ NoOfEvents, vendorEvents })
    }
    res.status(201).json({message:"no events available"})
  } catch (error) {
    res.status(500).json({ message: "Error fetching data" });
  }

}

const GetVendorsDetails = async (req, res) => {
  console.log("vendor id:", req.params.vendorId);
  try {
    const vendorDetails = await VendorUser.findById(req.params.vendorId);
    console.log("GetVendorsEvent:", vendorDetails);
    if (vendorDetails) {
      return res.status(200).send(vendorDetails)
    }
     res.status(401).send("vendor not found")
  } catch (error) {
    res.status(500).json({ message: "Error fetching data" });
  }
}

const GetEnteredGuestOfEvent = async (req, res) => {
  let count = 0;
  try {
    const EnteredGuestOfEvent = await ScannerLinkData.find({ event_id: req.params.event_id })
    console.log(EnteredGuestOfEvent)
    if (EnteredGuestOfEvent && EnteredGuestOfEvent > 0) {
      for (let i = 0; i < EnteredGuestOfEvent.length; i++) {
        count = count + EnteredGuestOfEvent[i].total_no_of_Customer_attended
      }
    }
    res.status(200).json({ count });
  } catch (error) {
    res.status(500).json({ message: "Error fetching data" });
  }
}

const UpdateBasicPrice = async (req, res) => {
  try {
    // Fetch all tickets for the given event_id
    const basicprice = await TicketModel.find({ event_id: req.params.event_id });

    if (Array.isArray(basicprice) && basicprice.length > 0) {
      // Update each ticket's basic_price
      for (let ticket of basicprice) {
        ticket.basic_price = ticket.ticket_price * (req.body.basic_price / 100);
        await ticket.save(); // Save the updated ticket back to the database
      }
    } else {
      return res.status(404).json({ message: "No tickets found for the given event." });
    }

    res.status(200).json({ message: "Updated successfully" });
  } catch (error) {
    console.error("Error updating basic prices:", error);
    res.status(500).json({ message: "Error updating data", error });
  }
};


const EventPayoutsData = async (req, res) => {
  try {
    const events = await CeremonyModel.find();
    const results = [];

    for (let i = 0; i < events.length; i++) {
      // Fetch vendor details
      const vendor = await VendorUser.findById(events[i].eventCreatedBy);

      // Fetch related bookings
      const revenue = await BookingModel.find({ eventDetails: events[i]._id });

      // Calculate total revenue
      const revenue_amt = revenue.reduce((total, booking) => {
        return booking.isPaid ? total + booking.amount : total;
      }, 0);

      // Push event data to results array
      results.push({
        event: events[i],
        vendor,
        revenue_amt,
      });
    }

    // Send results as response
    res.status(200).json({ success: true, data: results });
  } catch (error) {
    console.error("Error fetching event payouts data:", error);
    res.status(500).json({ success: false, message: "An error occurred.", error });
  }
};

const GetEvent = async (req, res) => {
  try {
    const event = await CeremonyModel.findById(req.params.event_id).populate('ticket');
    if (event) {
      res.status(200).json({ success: true, data: event });
    }
  } catch (error) {
    res.status(500).json({ sucess: false, error: error })
  }
}

const IndividualEventRevenue = async (req, res) => {
  const { event_id } = req.params;
  try {
    // Fetch related bookings
    const revenue = await BookingModel.find({ eventDetails: event_id });

    // Calculate total revenue
    const revenue_amt = revenue.reduce((total, booking) => {
      return booking.isPaid ? total + booking.amount : total;
    }, 0);

    res.status(200).json({ success: true, data: revenue_amt });
  } catch (error) {
    console.error("Error fetching event payouts data:", error);
    res.status(500).json({ success: false, message: "An error occurred.", error });
  }
}

const CustonerQueries = async (req, res) => {
  try {
    const Querylist = await CustomerQueryModel.find()
    res.status(200).json({ success: true, data: Querylist })
  } catch (error) {
    res.status(500).json({ sucess: false, error: error })
  }
}

const SuccessVendorPayment = async (req, res) => {
  try {
    const SaveVendorPayment = new VendorPaymentModel({
      vendorId: req.body.vendorId,
      eventId: req.body.eventId,
      Amt_Paid: req.body.Amt_Paid,
      isAmtPaid: req.body.isAmtPaid
    });
    await SaveVendorPayment.save()
      .then(event => {
        res.status(200).send({ message: "VendorPayment added successfully" });
      })
      .catch(error => {
        res.status(201).send({ message: "Unable to add VendorPayment, please try again", error: error.message });
      });

  } catch (error) {
    res.status(500).send({ message: "Something went wrong, try again after some time", error: error.message });
  }
}

const VendorPaymentDetail = async (req, res) => {
  try {
    await VendorPaymentModel.findOne({
      eventId: req.params.eventId
    })
      .then(vendorPayment => {
        res.status(200).send({ vendorPayment: vendorPayment });
      })
      .catch(error => {
        res.status(201).send({ error: error.message });
      });

  } catch (error) {
    res.status(500).send({ message: "Something went wrong, try again after some time", error: error.message });
  }
}

const GetVendorPayment = async (req, res) => {
  try {
    const VendorPayment = await VendorPaymentModel.findOne({
      vendorId: req.params.vendorId,
      eventId: req.params.eventId,
    });

    if (VendorPayment) {
      return res.status(200).json({ success: true, data: VendorPayment });
    }

    // If no vendor payment is found
    res.status(201).json({ success: false, message: "Vendor payment not found" });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Something went wrong, try again after some time",
      error: error.message,
    });
  }
};

const SaveCarousel_Image = async (req, res) => {
  try {
    const carousel_img = new CarouselDataModel({
      image: req.body.image
    });

    await carousel_img.save();

    res.status(200).json({ message: "Image saved successfully" });
  } catch (error) {
    res.status(500).json({ message: "Unable to save image", error: error.message });
  }
};

const UpdateCarousel_Image = async (req, res) => {
  try {
    const updatedImage = await CarouselDataModel.findByIdAndUpdate(
      req.params.image_id,
      { image: req.body.image },
      { new: true }
    );

    if (!updatedImage) {
      return res.status(404).json({ message: "Image not found" });
    }

    res.status(200).json({ message: "Image updated successfully", data: updatedImage });
  } catch (error) {
    res.status(500).json({ message: "Unable to update image", error: error.message });
  }
};

const GetCarousel_Image = async (req, res) => {
  try {
    const carousel_img = await CarouselDataModel.find(); // Await the promise to resolve
    res.status(200).json({ carousel_img });
  } catch (error) {
    res.status(500).json({ message: "Unable to fetch image", error: error.message }); // Fixed error message
  }
};

const DeleteCarousel_Image = async (req, res) => {
  try {
    const carousel_img = await CarouselDataModel.findOneAndDelete({ _id: req.params.id }); // Await the promise to resolve
    res.status(200).json({ message: "Carousel image deleted successfully", carousel_img });
  } catch (error) {
    res.status(500).json({ message: "Unable to delete image", error: error.message }); // Fixed error message
  }
};

const UpdateCampaingsData = async (req, res) => {
  try {
    const UpdateCampaings = await CeremonyModel.findOneAndUpdate({ _id: req.body.event_ID }, {
      SM_Campaign: req.body.SM_Campaign,
      Daily_Budget: req.body.daily_Budget,
      Paid_by: req.body.paid_by,
    },
      { new: true }); // Await the promise to resolve
    if (!UpdateCampaings) {
      return res.status(404).json({ message: "Campaign data not found" });
    }

    // Success response
    res.status(200).json({ message: "Data updated successfully", UpdateCampaings });
  } catch (error) {
    res.status(500).json({ message: "Unable to update data", error: error.message }); // Fixed error message
  }
};

const SendWebNotification = async (req, res) => {
  try {
    const { title, body } = req.body;
    const deviceTokensOfAllUsers = await FCMToken.find({}, 'FCM_Token');

    await Promise.all(
      deviceTokensOfAllUsers.map(async (user) => {
        try {
          await NotificationService.sendNotification(user.FCM_Token, title, body);
          console.log(`Notification sent to: ${user.FCM_Token}`);
        } catch (err) {
          if (err.code === 'messaging/registration-token-not-registered') {
            console.log(`Invalid or unregistered token: ${user.FCM_Token}`);
          } else {
            console.error('Error sending notification:', err);
          }
        }
      })
    );

    const saveSentmessage = new NotificationModel({
      Title: title,
      Message: body
    });
    await saveSentmessage.save();

    res.status(200).json({ message: "Notification sent!" });
  } catch (err) {
    res.status(500).json({ message: "Unable to send notification", err: err.message });
  }
};


const Save_Fcm_Token = async (req, res) => {
  const { token } = req.body;
  try {
    const tokenFound = FCMToken.findOne({ FCM_Token: token });
    if (tokenFound) {
      return res.status(200).json({ message: "FCM token already registered" })
    }
    const saveFCMToken = new FCMToken(
      { FCM_Token: token }
    )
    await saveFCMToken.save()
    if (saveFCMToken) {
      return res.status(200).json({ message: "FCM token saved successfully" })
    }
  } catch (error) {
    res.status(500).json({ message: "FCM token not saved" })
  }
}


const GetAllCategory = async (req, res) => {
  try {
    const category = await CategoryModel.find(); // Await the promise to resolve
    res.status(200).json({ category });
  } catch (error) {
    res.status(500).json({ message: "Unable to fetch category", error: error.message }); // Fixed error message
  }
};

const Deletecategory = async (req, res) => {
  try {
    const category = await CategoryModel.findOneAndDelete({ _id: req.params.id }); // Await the promise to resolve
    res.status(200).json({ message: "Carousel image deleted successfully", category });
  } catch (error) {
    res.status(500).json({ message: "Unable to delete image", error: error.message }); // Fixed error message
  }
};

const GetCitiesName = async (req, res) => {
  try {
    const cities = await StateModel.find(); // Await the promise to resolve
    res.status(200).json({ cities });
  } catch (error) {
    res.status(500).json({ message: "Unable to fetch cities", error: error.message }); // Fixed error message
  }
};

const AddNewCity = async (req, res) => {
  const {
    city,
    state,
    gstin_code
  } = req.body;
  try {
    const newCity = new StateModel({
      City: city,
      State: state,
      GSTIN_Code: gstin_code
    });
    await newCity.save();
    res.status(200).send({ message: "New City Created successfully", city: newCity });
  } catch (error) {
    res.status(500).send({ message: "Unable to create city", error });
  }
};

const DeleteCity = async (req, res) => {
  try {
    const city = await StateModel.findOneAndDelete({ _id: req.params.id }); // Await the promise to resolve
    res.status(200).json({ message: "Carousel image deleted successfully", city });
  } catch (error) {
    res.status(500).json({ message: "Unable to delete image", error: error.message }); // Fixed error message
  }
};

const Coupon_Validation = async (req, res) => {
  const { couponCode, event_id, totalBillAmount } = req.body;
  
  try {
    const findCouponCode = await DiscountCouponModel.findOne({ coupon_Code: couponCode });

    if (!findCouponCode) {
      return res.status(401).json({ message: "Invalid Coupon Code" });
    }

    // Check if the coupon is valid within the date range
    const currentDate = Date.now();

    // Convert stored dates to timestamps (if they are strings)
    const validFrom = new Date(findCouponCode.coupon_Code_Valid_From).getTime();
    const validTo = new Date(findCouponCode.coupon_Code_Valid_To).getTime();

    console.log("__________________VALIDATION :",findCouponCode.coupon_Code_Valid_From, currentDate, findCouponCode.coupon_Code_Valid_To, currentDate)
    if (validFrom <= currentDate && validTo >= currentDate) {
      
      let givenDiscount, discountedAmount;
      let discount;

      if (findCouponCode.discount_Type === "Percentage") {
        givenDiscount = `${findCouponCode.DicountValue}%`;
        discount =  ((totalBillAmount * findCouponCode.DicountValue) / 100).toFixed(2);
        discountedAmount = totalBillAmount - discount;
      } else if (findCouponCode.discount_Type === "Amount") {
        givenDiscount = findCouponCode.DicountValue;
        discount = findCouponCode.DicountValue;
        discountedAmount = totalBillAmount - findCouponCode.DicountValue;
      } else {
        return res.status(400).json({ message: "Invalid discount type" });
      }

      return res.status(200).json({ givenDiscount, discountedAmount, discount });
    
    } else {
      return res.status(401).json({ message: "Coupon Code Expired" });
    }

  } catch (error) {
    console.error("Error in Coupon Validation:", error);
    return res.status(500).json({ message: "Sorry! Coupon code not applicable" });
  }
};


const Coupon_Creating = async (req, res) => {
  const {
    coupon,
    coupon_Code,
    discount_Type,
    coupon_Code_Valid_From,
    coupon_Code_Valid_To,
    DicountValue,
  } = req.body;
  try {
    const CreateCouponCode = new DiscountCouponModel({
      Name_of_Coupon: coupon,
      coupon_Code: coupon_Code,
      discount_Type: discount_Type,
      coupon_Code_Valid_From: coupon_Code_Valid_From,
      coupon_Code_Valid_To: coupon_Code_Valid_To,
      DicountValue: DicountValue,
    });

    await CreateCouponCode.save()
      .then((coupon_saved) => {
        return res.status(200).json({ message: "Coupon Code Created Successfully" })
      })
      .catch(err => {
        console.log("Coupon Error", err)
        res.status(401).json({ message: "Sorry! Unable to generate Coupon code" })
      })

  } catch (error) {
    return res.status(500).json({ message: "Sorry! Coupon code not generated" })
  }
}

const GetCoupons = async (req, res) => {
  try {
    const Coupons = await DiscountCouponModel.find(); // Await the promise to resolve
    res.status(200).json({ Coupons });
  } catch (error) {
    res.status(500).json({ message: "Unable to fetch Coupons", error: error.message }); // Fixed error message
  }
};

const DeleteCoupon = async (req, res) => {
  try {
    const coupon = await DiscountCouponModel.findOneAndDelete({ _id: req.params.id }); // Await the promise to resolve
    res.status(200).json({ message: "Coupon deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Unable to delete Coupon", error: error.message }); // Fixed error message
  }
};

const GetAllSentNotification = async (req,res)=>{
  try {
    const notifications = await NotificationModel.find(); // Await the promise to resolve
    res.status(200).json({notifications})
  } catch (error) {
    res.status(500).json({message:"Unable to get Data", error: error.message})
  }
}

module.exports = {
  GetAllSentNotification,
  AdminSignUp,
  AdminLogin,
  AdminDashBoardMatrices,
  AdminAllEventsList,
  GetVendorData,
  GetAllTicketBuyers,
  GetAllUsersData,
  GetAllVendors,
  GetVendorsEvent,
  GetVendorsDetails,
  GetEnteredGuestOfEvent,
  UpdateBasicPrice,
  EventPayoutsData,
  GetEvent,
  IndividualEventRevenue,
  CustonerQueries,
  SuccessVendorPayment,
  VendorPaymentDetail,
  GetVendorPayment,
  SaveCarousel_Image,
  GetCarousel_Image,
  DeleteCarousel_Image,
  UpdateCampaingsData,
  UpdateCarousel_Image,
  SendWebNotification,
  Save_Fcm_Token,
  GetAllCategory,
  Deletecategory,
  GetCitiesName,
  AddNewCity,
  DeleteCity,
  Coupon_Validation,
  Coupon_Creating,
  GetCoupons,
  DeleteCoupon
};