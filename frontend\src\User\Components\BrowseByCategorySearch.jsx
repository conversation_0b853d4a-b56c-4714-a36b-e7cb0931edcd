import axios from "axios";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { API_URL } from "../../config.js";
import "./BrowseByCategory.css";
import CategoryImages from "../../Assets/CategoryImages.js";
import { IoChevronBackOutline, IoChevronForwardOutline } from "react-icons/io5";

function BrowseByCategorySearch() {
  const navigate = useNavigate();
  const [categories, setCategories] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [isLoading, setIsLoading] = useState(true);

  const handleEventCategory = (cat) => {
    navigate(`/category/${cat}`);
  };

  useEffect(() => {
    const updateItemsPerPage = () => {
      if (window.innerWidth < 560) {
        setItemsPerPage(3);
      } else if (window.innerWidth < 915) {
        setItemsPerPage(3);
      } else {
        setItemsPerPage(4);
      }
    };

    updateItemsPerPage();
    window.addEventListener("resize", updateItemsPerPage);

    return () => {
      window.removeEventListener("resize", updateItemsPerPage);
    };
  }, []);

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      try {
        const listofCategory = await axios.get(`${API_URL}/api/category`);
        setCategories(listofCategory.data);
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
      }
    })();
  }, [setIsLoading, setCategories]);

  const nextSlide = () => {
    if (currentIndex + itemsPerPage < categories.length) {
      setCurrentIndex((prevIndex) => prevIndex + 2);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prevIndex) => prevIndex - 2);
    }
  };


  return isLoading ? null : (
    <>
      <div className="category-list-search-main">
        <h4 className="browseHeading mb-4">Browse By Category</h4>

        <div
          className="browseCarousel"
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "start",
            alignItems: "center",
          }}
        >
          <div className="carousel-search-container">
            {categories?.map((category, index) => (
              <div
                className=" category-type-card"
                key={index}
                onClick={() => handleEventCategory(category.category)}
              >
                <img
                  src={category.image}
                  alt={category.category}
                  style={{ width: "100%", height: "auto" }}
                />
                <span className="category-title bg-white text-dark">
                  {category.category}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}

export default BrowseByCategorySearch;
