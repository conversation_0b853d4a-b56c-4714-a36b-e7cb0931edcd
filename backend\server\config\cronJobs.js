// const cron = require('node-cron');
// const CeremonyModel = require('../models/CeremonyModel')

// cron.schedule('0 0 * * *', async () => {  // Runs daily at midnight
//   try {
//     await CeremonyModel.updateMany(
//       { event_ends_date: { $lt: new Date() } },
//       { $set: { isExpired: true } }
//     );
//     console.log('Expired events updated');
//   } catch (error) {
//     console.error('Error updating events:', error);
//   }
// });
