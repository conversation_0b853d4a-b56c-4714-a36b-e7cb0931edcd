<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.png" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="Leading event ticketing website of Central India" />
  <link rel="apple-touch-icon" href="%PUBLIC_URL%/favicon.png" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

  <!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<!-- Bootstrap Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap" rel="stylesheet">


  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>My Front Seat</title>
  <style>
    #safariPrompt {
      display: none;
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #ffcccb;
      color: #333;
      text-align: center;
      padding: 15px;
      font-size: 16px;
      z-index: 1000;
    }
    #safariPrompt a {
      color: #007aff;
      text-decoration: none;
      font-weight: bold;
    }
  </style>

  <script src="https://www.google.com/recaptcha/api.js" async defer></script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-BYVLE74FM7"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-BYVLE74FM7');
  </script>


  <!-- Google Font Poppins -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet">

  <!-- Poppins Style -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link
    href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap"
    rel="stylesheet">

    
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    <div id="safariPrompt">
      For the best experience, please open this page in Safari. Tap the three dots in the top-right corner and select "Open in Browser".
    </div>
  
    <script>
      // Existing functions (keep them)
      function isInstagramInAppBrowser() {
          const ua = navigator.userAgent || navigator.vendor || window.opera;
          return (ua.indexOf('Instagram') > -1);
      }
  
      function isIOS() {
          const ua = navigator.userAgent || navigator.vendor || window.opera;
          return (/iPad|iPhone|iPod/.test(ua) && !window.MSStream);
      }
  
      function isAndroid() {
          const ua = navigator.userAgent;
          return /android/i.test(ua);
      }
  
      // Attempt to redirect if in Instagram in-app browser
      if (isInstagramInAppBrowser()) {
          if (isIOS()) {
              // Attempt to open in Safari on iOS
              // This is the trickiest part and often requires user interaction.
              // Using a timeout is a common heuristic to prevent the in-app browser from cancelling.
              setTimeout(() => {
                  window.location.href = 'x-safari-http://' + window.location.hostname + window.location.pathname + window.location.search;
              }, 25); // Small delay might sometimes help bypass some in-app browser restrictions
  
              // Fallback for older iOS or if the above fails (might still open in-app, but Safari's "Open in Safari" button is usually there)
              setTimeout(() => {
                  window.location.href = 'https://' + window.location.hostname + window.location.pathname + window.location.search;
              }, 100);
  
              // Display the prompt as a backup, since automatic redirection often fails on iOS.
              document.getElementById('safariPrompt').style.display = 'block';
  
          } else if (isAndroid()) {
              // Attempt to open in Chrome on Android using an Intent URI
              // This typically opens a "Choose an app" dialog on Android.
              const currentUrl = encodeURIComponent(window.location.href);
              const chromeIntent = `intent://${window.location.hostname}${window.location.pathname}#Intent;scheme=${window.location.protocol.replace(':', '')};package=com.android.chrome;S.browser_fallback_url=${currentUrl};end;`;
  
              window.location.href = chromeIntent;
  
              // You might still want to show the prompt as a fallback for Android too,
              // in case Chrome isn't installed or the intent fails.
              // Or only show it after a delay if the redirect didn't happen.
              setTimeout(() => {
                  if (isInstagramInAppBrowser()) { // Check again if still in Instagram browser
                      document.getElementById('safariPrompt').style.display = 'block';
                  }
              }, 1000); // Give it a second to try redirecting
          } else {
              // For other unknown in-app browsers, just display the general prompt
              document.getElementById('safariPrompt').style.display = 'block';
          }
      }
  
      function hideSafariPrompt() {
          document.getElementById('safariPrompt').style.display = 'none';
      }
  </script>
</body>

</html>