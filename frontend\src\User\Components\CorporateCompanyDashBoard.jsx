import { <PERSON>, useNavigate, useParams } from "react-router-dom";
import { PuffLoader } from "react-spinners";
import CropNavbarComponent from "./CropNavbarComponent";
import { useEffect, useState } from "react";
import { AiOutlineCloudUpload, AiOutlineDashboard } from "react-icons/ai";
import { HiMiniArrowTrendingUp } from "react-icons/hi2";
import { IoMdCopy } from "react-icons/io";
import { API_URL } from "../../config";
import axios from "axios";
import CorporateSideBar from "./CorporateSideBar";
import * as XLSX from 'xlsx';

const CorporateCompanyDashboard = () => {
    const user = JSON.parse(localStorage.getItem('corporateCompanyData'));
    const [isLoading, setIsLoading] = useState(false);
    const [CorporateCompanyEvents, setCorporateCompanyEvents] = useState([]);
    const [CorporateCompanyDetailData, setCorporateCompanyDetailData] = useState();
    const [EmployeeExcel, setEmployeeExcel] = useState();
    const [uploaded_EmployeeExcel, setUploaded_EmployeeExcel] = useState("");
    const [isBtnDisabled, setIsBtnDisabled] = useState(true);
    const [isUploaded, setIsUploaded] = useState(false);
    const navigate = useNavigate();
    const [generatedLink, SetGeneratedLink] = useState();
    const [ticket_hosted, setTicket_hosted] = useState(0);
    const [ticket_sold, setTicket_sold] = useState(0);
    const [users, SetUsers] = useState([]);
    const [Revenue, setRevenue] = useState(0);
    const [visibleEvents, setVisibleEvents] = useState(5);
    const [totalGuestEntered, setTotalGuestEntered] = useState(0);
    const [isViewModal, setIsViewModal] = useState({
        uploadModal: false,
        ScannerLinkModal: false,
        TicketsModal: false,
        LoginDetailModal: false
    });

    const downloadExcel = () => {
        const tableData = CorporateCompanyEvents.map((event, index) => ({
            "Sr. No.": index + 1,
            "Event Name": event.event_name,
            "Location": event.event_city,
            "Date": new Date(event.event_start_date).toLocaleDateString('en-GB').replace(/\//g, '-'),
            "Tickets Hosted": event.tickets.length,
            "Ticket sold": 0,
            "Status": new Date(new Date(event.event_end_date).setHours(0, 0, 0, 0)) < new Date(new Date().setHours(0, 0, 0, 0))
            ? "Completed"
            : "Active",
        }));

        const worksheet = XLSX.utils.json_to_sheet(tableData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, "Ticket Buyers");

        // Download the Excel file
        XLSX.writeFile(workbook, "EventsList.xlsx");
    };
    const handleCopy = (copy_type) => {
        let cardContent = '';
        if (copy_type === "loginDetail") {
            const loginId = CorporateCompanyDetailData?.poc_contact_number;
            const password = CorporateCompanyDetailData?.passcode;

            cardContent = `
                Login id: ${loginId}
                Password: ${password}
            `;

        }
        if (copy_type === "scannerLink") {
            const ticketScannerLink = `https://www.hostyourfrontseat.in/event-${CorporateCompanyEvents?.event_name.replace(/\s+/g, '_')}/ticketscanner/${generatedLink?.event_id || ""}`;
            const loginId = generatedLink?.login_id;
            const password = generatedLink?.password;

            cardContent = `
                Ticket Scanner Link: ${ticketScannerLink}
                Login id: ${loginId}
                Password: ${password}
            `;

        }
        if (copy_type === "eventLink") {
            const eventLink = `https://www.myfrontseat.in/corporateevent/${(CorporateCompanyEvents?.event_name).replace(/\s+/g, '_')}/${CorporateCompanyEvents.corporate_event_id}`;

            cardContent = `
                Event Link: ${eventLink}
            `;

        }

        navigator.clipboard.writeText(cardContent)
            .then(() => {
                alert("Link copied!");
            })
            .catch((err) => {
                console.error("Failed to copy text: ", err);
            });
    };

    const handleSeeMore = () => {
        setVisibleEvents((prevVisible) => Math.min(prevVisible + 5, users.length));
    };

    const handleSeeLess = () => {
        setVisibleEvents(5);
    };

    const GetCompanyDetails = async () => {
        try {
            const companyDetailResponse = await axios.get(`${API_URL}/corporate/company-detail/${user._id}`)
            if (companyDetailResponse.status === 200 && companyDetailResponse.data.company) {
                setCorporateCompanyDetailData(companyDetailResponse.data.company); // Update state with event details
            }
        } catch (error) {

        }
    }

    const getCorporateCompanyEventss = async () => {
        try {
            // Fetch Event Details
            const responseData = await axios.get(`${API_URL}/corporate/get-corporate-all-event-list/${user._id}`);
            console.log(responseData)
            if (responseData.status === 200 && responseData.data) {
                setCorporateCompanyEvents(responseData.data); // Update state with event details
            }
        } catch (error) {
            console.error("Failed to fetch event details:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const getScannerLink = async () => {
        const response = await axios.get(`${API_URL}/corporate/get-scanner-link/${user._id}`);
        if (response.status === 200) {
            const link = response.data.Links;
            if (link) {
                SetGeneratedLink(link);
            }
        }
    };

    const handleFileSelection = (e, fieldName) => {
        setUploaded_EmployeeExcel(e.target.files[0].name);
        setEmployeeExcel(e.target.files[0]);
        setIsBtnDisabled(false); // Enable the PAN card upload button
    }

    const handleFileUpload = async () => {
        const formData = new FormData();
        formData.append('emp_data_excelSeet', EmployeeExcel);
        try {
            // upload excel
            const excelSheetresponse = await axios.post(`${API_URL}/corporate/upload/excel-sheet`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });
            if (excelSheetresponse.status === 200) {
                alert('Data uploaded and saved successfully!');
            } else {
                alert('Failed to save uploaded data');
            }

        } catch (error) {
            console.error('Error:', error);
            alert('An error occurred during the upload or save process');
        }

    }
    useEffect(() => {
        getCorporateCompanyEventss();
        getScannerLink();
        GetCompanyDetails();
    }, [user._id]);

    const handleShowEventDetails = (event_unique_id, event_name) => {
        navigate(`/corporate/${event_name}/event-detail/${event_unique_id}`);
    };

    return (
        <>
            <CropNavbarComponent />
            <div className="container-fluid">
                {isLoading ? (
                    <div
                        className="d-flex justify-content-center align-items-center"
                        style={{ height: "50vh" }}
                    >
                        <PuffLoader size={48} loading={isLoading} />
                    </div>
                ) : (
                    <div className="row">
                        <CorporateSideBar />
                        <div className="col-lg-10 col-md-10 col-12 px-2">
                            <div className='d-flex justify-content-between border-bottom border-primary'>
                                <span className="vender-heading">Your Dashboard</span>
                            </div>

                            <div className="mt-3 eventMaxtixMainDiv">
                                <div className="corporatematrixDiv me-lg-3">
                                    <div className="card h-100">
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between">
                                                <div>
                                                    <p className='dashboard-summary fw-semibold'>Total Event Hosted</p>
                                                    <p className="count-style">{totalGuestEntered}</p>
                                                </div>
                                                <div></div>
                                            </div>
                                            <div>
                                                <span className='dashboard-summary fw-semibold'><span className="text-success"><HiMiniArrowTrendingUp /> 1.8% </span>
                                                    <span>Up from Last year</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="corporatematrixDiv me-lg-3">
                                    <div className="card h-100">
                                        <div className="card-body">
                                            <div className="d-flex justify-content-between">
                                                <div>
                                                    <p className='dashboard-summary fw-semibold'>Total Ticket Sold</p>
                                                    <p className="count-style">{ticket_sold}</p>
                                                </div>
                                                <div></div>
                                            </div>
                                            <div>
                                                <span className='dashboard-summary fw-semibold'><span className="text-success"><HiMiniArrowTrendingUp /> 1.8% </span>
                                                    <span>Up from Last year</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="corporatematrixDiv-last smallViewSettopMargin text-center">
                                    <div className="card corporatematrixcard-last  mb-2">
                                        <div className="card-body d-flex flex-column">
                                            <span className='dashboard-summary fw-bold' style={{ whiteSpace: "wrap" }}>Company POC Login Credentials</span>
                                            <span>
                                                <button className="sub-mat-btn" data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                                    onClick={() => setIsViewModal(prevState => ({
                                                        ...prevState,
                                                        uploadModal: false,
                                                        LoginDetailModal: true
                                                    }))}>View</button>
                                            </span>
                                        </div>
                                    </div>
                                    <div className="card corporatematrixcard-last">
                                        <div className="card-body d-flex flex-column">
                                            <span className='dashboard-summary fw-bold' style={{ whiteSpace: "nowrap" }}>Upload Employee Database</span>
                                            <span ><button className="sub-mat-btn"
                                                data-bs-toggle="modal" data-bs-target="#TicketDetailsModal"
                                                onClick={() => setIsViewModal(prevState => ({
                                                    ...prevState,
                                                    uploadModal: true,
                                                    LoginDetailModal: false
                                                }))}>Upload</button></span>
                                            <span className="corporatedashboard-summary" style={{ whiteSpace: "nowrap" }}>
                                                Upload .XLS of your employee datsbase
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="mt-lg-3">
                                <div className="card mt-lg-3">
                                    <div className="card-body">
                                        <div className="d-flex justify-content-between">
                                            <span className="vender-heading">Event Details </span>
                                            <button className="download-btn" onClick={downloadExcel}>Download .xlsx</button>
                                        </div>
                                        <div className="table-responsive">
                                            <table className="table table-hover text-center mt-2">
                                                <thead className="tableHead">
                                                    <tr className="table-light">
                                                        <th className="table-field" scope="col">Event Name</th>
                                                        <th className="table-field" scope="col">Location</th>
                                                        <th className="table-field" scope="col">Date</th>
                                                        <th className="table-field" scope="col">Tickets Hosted</th>
                                                        <th className="table-field" scope="col">Ticket sold</th>
                                                        <th className="table-field" scope="col">Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {CorporateCompanyEvents && CorporateCompanyEvents.length > 0 ? (
                                                        <>
                                                            {
                                                                CorporateCompanyEvents.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, visibleEvents).map((event, index) => {
                                                                    return (
                                                                        <tr className="tableDataRow" key={event._id} onClick={() =>
                                                                            handleShowEventDetails(
                                                                                event.corporate_event_id,
                                                                                event.event_name
                                                                            )
                                                                        }>
                                                                            <td className="table-value">{event.event_name}</td>
                                                                            <td className="table-value">{event.event_city}</td>
                                                                            <td className="table-value">{new Date(event.event_start_date).toLocaleDateString('en-GB').replace(/\//g, '-')}</td>
                                                                            <td className="table-value">
                                                                                {event.tickets.length}
                                                                            </td>
                                                                            <td className="table-value">
                                                                                {/* {event.guestEntered} */}
                                                                            </td>
                                                                            <td className="table-value text-center" >

                                                                                {new Date(new Date(event.event_end_date).setHours(0, 0, 0, 0)) < new Date(new Date().setHours(0, 0, 0, 0))
                                                                                    ? <span className="vendor-event-status-complete">Completed</span>
                                                                                    : <span className="vendor-event-status-active">Active</span>}

                                                                            </td>
                                                                        </tr>
                                                                    )
                                                                })
                                                            }
                                                            {visibleEvents < users.length ? (
                                                                <tr className="text-center">
                                                                    <td colSpan={8}>
                                                                        <button className="btn-text btn btn-link" onClick={handleSeeMore}>
                                                                            See More
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            ) : visibleEvents > 5 ? (
                                                                <tr className="text-center">
                                                                    <td colSpan={8}>
                                                                        <button className="btn-text btn btn-link" onClick={handleSeeLess}>
                                                                            See Less
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                            ) : null}
                                                        </>
                                                    ) :
                                                        <tr>
                                                            <td className="table-value text-start" colSpan={8}>
                                                                <p>.........No Event Added........</p>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                )}
            </div>


            {/* <!-- Detail Modal --> */}
            <div className="modal fade" id="TicketDetailsModal" tabIndex="-1" aria-labelledby="TicketDetailsModalLabel" aria-hidden="true">
                <div className="modal-dialog modal-lg modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-body">
                            <div className="text-center">
                                <p className={`sub-mat-head ${isViewModal.uploadModal ? 'd-block' : 'd-none'}`} >Upload Employee Data Sheet</p>
                                <p className={`sub-mat-head ${isViewModal.LoginDetailModal ? 'd-block' : 'd-none'}`} >Company POC Login Details</p>
                            </div>
                            <div className={`${isViewModal.uploadModal ? 'd-block' : 'd-none'}`}>
                                <div className=" d-flex align-items-center justify-content-center w-100">
                                    <div className="text-center mb-3">
                                        <label className="poppins20 fw-semibold ms-1 mb-2 mt-3 text-center" htmlFor='EmployeeExcel'>Upload Excel Sheet</label>
                                        <div className='coverImg_style m-auto mb-3'>
                                            <div className='uploadFile text-center'>
                                                <AiOutlineCloudUpload className='uploadIcon' />
                                                {EmployeeExcel ? (
                                                    <div>
                                                        <span className='poppins16 fw-bold'>Uploaded: </span> <p>{uploaded_EmployeeExcel}</p>
                                                    </div>
                                                ) : (
                                                    <div className='text-center'>
                                                        <p className='poppins16'>Drag and Drop files or <a href="#">Browse</a></p>
                                                        <p className='poppins12'>Supported format: jpeg, jpg</p>
                                                    </div>
                                                )}
                                            </div>
                                            <input type="file" className='selectFileStyle w-100 h-100' onChange={handleFileSelection} />
                                        </div>
                                        <button type='button' className="btn uploadbtn" data-bs-dismiss="modal" onClick={handleFileUpload} disabled={isBtnDisabled}>Upload EmployeeExcel</button>
                                    </div>
                                </div>
                            </div>
                            <div className={`${isViewModal.LoginDetailModal ? 'd-block' : 'd-none'}`}>
                                <div className="mt-5 d-flex align-items-end w-100">
                                    <div className="card w-75 m-auto">
                                        <div className="card-body d-flex justify-content-between align-items-top">
                                            <div>
                                                <p><b>Login id:</b>
                                                    {" " + CorporateCompanyDetailData?.poc_contact_number}
                                                </p>
                                                <p><b>Password:</b>
                                                    {" " + CorporateCompanyDetailData?.passcode}
                                                </p>
                                            </div>
                                            <div>
                                                <IoMdCopy size={30} onClick={() => handleCopy("loginDetail")} style={{ color: "#2C9CF0" }} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

export default CorporateCompanyDashboard;